{"name": "talkai247-whisper-tests", "version": "1.0.0", "description": "Integration tests for Real-Time AI Whisper Feature", "type": "module", "scripts": {"test:whisper": "node test-whisper-integration.js", "test:whisper:dev": "SERVER_URL=http://localhost:3030 node test-whisper-integration.js", "test:whisper:prod": "SERVER_URL=https://your-production-url.com node test-whisper-integration.js"}, "dependencies": {"axios": "^1.6.0", "ws": "^8.14.0"}, "devDependencies": {"@types/node": "^20.0.0"}, "keywords": ["whisper", "livekit", "twi<PERSON>", "ai", "testing"], "author": "tAlkai247 Team", "license": "MIT"}