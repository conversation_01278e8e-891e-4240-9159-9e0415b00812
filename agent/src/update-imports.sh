#!/bin/bash

# Update imports in all TypeScript files to include .js extension
find ./src -name "*.ts" -type f -exec sed -i 's/from \(.\)\.\/\([^"]*\)\(.\);/from \1.\/\2.js\1;/g' {} \;
find ./src -name "*.ts" -type f -exec sed -i 's/from \(.\)\.\/config\(.\);/from \1.\/config\/index.js\1;/g' {} \;
find ./src -name "*.ts" -type f -exec sed -i 's/from \(.\)\.\/types\(.\);/from \1.\/types\/index.js\1;/g' {} \;

echo "Import statements updated to include .js extension"
