// SPDX-FileCopyrightText: 2024 LiveKit, Inc.
//
// SPDX-License-Identifier: Apache-2.0
import { tts } from '@livekit/agents';
import dotenv from 'dotenv';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

// Get directory name for ES modules
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const envPath = path.join(__dirname, '../../.env.local');

// Load environment variables
dotenv.config({ path: envPath });

/**
 * Configuration object for the application
 */
export const config = {
  pinecone: {
    apiKey: process.env.PINECONE_API_KEY || '',
    indexName: 'scraped-data',
  },
  twilio: {
    sid: process.env.TWILIO_SID || '',
    authToken: process.env.TWILIO_AUTH_TOKEN || '',
    phoneNumber: process.env.TWILIO_PHONE_NUMBER || '',
  },
  encryption: {
    key: process.env.ENCRYPTION_KEY,
  },
  googleGemini: {
    apiKey: process.env.GOOGLE_GEMINI_API_KEY || '',
    baseURL: process.env.OPENROUTER_BASE_URL || '',
    llmModel: process.env.OPENROUTER_LLM_MODEL || '',
  },
  openai: {
    baseURL: process.env.KOKORO_AI_BASE_URL || '',
  },
  tts: {
    baseURL: process.env.KOKORO_AI_BASE_URL || '',
    apiKey: process.env.KOKORO_AI_API_KEY || 'not-needed',
  },
  openRouter: {
    baseURL: process.env.OPENROUTER_BASE_URL || '',
    apiKey: process.env.OPENROUTER_API_KEY || '',
    llmModel: process.env.OPENROUTER_LLM_MODEL || '',
    ttsModel: process.env.OPENROUTER_TTS_MODEL || '',
  },
};

/**
 * Validates that required environment variables are set
 * @throws Error if any required environment variables are missing
 */
export function validateConfig(): void {
  // Core required variables
  const requiredVars = [{ key: 'openai.baseURL', value: config.openai.baseURL }];

  // Optional service-specific variables
  const optionalServiceVars = {
    pinecone: [{ key: 'pinecone.apiKey', value: config.pinecone.apiKey }],
    twilio: [
      { key: 'twilio.sid', value: config.twilio.sid },
      { key: 'twilio.authToken', value: config.twilio.authToken },
      { key: 'twilio.phoneNumber', value: config.twilio.phoneNumber },
    ],
  };

  // Check required variables
  const missingVars = requiredVars.filter((v) => !v.value);

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.map((v) => v.key).join(', ')}`,
    );
  }

  // Log warnings for optional service variables
  Object.entries(optionalServiceVars).forEach(([service, vars]) => {
    const missingServiceVars = vars.filter((v) => !v.value);
    if (missingServiceVars.length > 0) {
      console.warn(
        `Warning: ${service} service may not function correctly. Missing variables: ${missingServiceVars.map((v) => v.key).join(', ')}`,
      );
    }
  });
}
