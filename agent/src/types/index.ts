// SPDX-FileCopyrightText: 2024 LiveKit, Inc.
//
// SPDX-License-Identifier: Apache-2.0

/**
 * Interface for participant attributes
 */
export interface ParticipantAttributes {
  systemPrompt?: string;
  userCalId?: string;
  calEventTypeId?: string;
  organizationSlug?: string;
  isPremium?: string;
  firstMessage?: string;
}

/**
 * Interface for function context
 */
export interface FunctionContextType {
  [key: string]: any;
}
