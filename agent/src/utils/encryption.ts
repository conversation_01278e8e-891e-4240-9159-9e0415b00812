// SPDX-FileCopyrightText: 2024 LiveKit, Inc.
//
// SPDX-License-Identifier: Apache-2.0
import crypto from 'crypto';

const ALGORITHM = 'aes-256-cbc';

/**
 * Gets or generates an encryption key for secure data handling
 * @param encryptionKey Optional encryption key from environment
 * @returns Buffer containing the encryption key
 */
export function getEncryptionKey(encryptionKey?: string): Buffer {
  if (encryptionKey) {
    // Ensure the key is exactly 32 bytes (256 bits) for AES-256
    if (Buffer.from(encryptionKey, 'hex').length === 32) {
      return Buffer.from(encryptionKey, 'hex');
    }
    // If not hex-encoded, hash it to get 32 bytes
    return crypto.createHash('sha256').update(encryptionKey).digest();
  }
  // Generate a new key if none is provided
  return crypto.randomBytes(32);
}

/**
 * Decrypts an encrypted string using the provided secret key
 * @param encryptedText The text to decrypt
 * @param secretKey The secret key to use for decryption
 * @returns The decrypted string
 */
export function decrypt(encryptedText: string, secretKey: Buffer): string {
  try {
    const parts = encryptedText.split(':');
    const firstPart = parts.shift();
    if (!firstPart) throw new Error('Invalid encrypted text format');
    
    const iv = Buffer.from(firstPart, 'hex');
    const encrypted = parts.join(':');
    const decipher = crypto.createDecipheriv(ALGORITHM, secretKey, iv);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Decryption failed');
  }
}
