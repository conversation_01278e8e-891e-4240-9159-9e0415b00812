// SPDX-FileCopyrightText: 2024 LiveKit, Inc.
//
// SPDX-License-Identifier: Apache-2.0
// Core LiveKit imports
import {
  type JobContext,
  type JobProcess,
  WorkerOptions,
  cli,
  defineAgent,
  llm,
  pipeline,
} from '@livekit/agents';
// LiveKit plugins
import * as deepgram from '@livekit/agents-plugin-deepgram';
import * as elevenlabs from '@livekit/agents-plugin-elevenlabs';
import * as openai from '@livekit/agents-plugin-openai';
import * as silero from '@livekit/agents-plugin-silero';
// Third-party libraries
import { Pinecone } from '@pinecone-database/pinecone';
import { fileURLToPath } from 'node:url';
import { z } from 'zod';
// Local imports
import { config, validateConfig } from './config/index.js';
import { CalendarService } from './services/calendarService.js';
import { CommunicationService } from './services/communicationService.js';
import { WeatherService } from './services/weatherService.js';
import { WebScraperService } from './services/webScraper.js';
import { decrypt, getEncryptionKey } from './utils/encryption.js';

// Validate configuration
validateConfig();

// Initialize encryption
const secretKey = getEncryptionKey(config.encryption.key);

// Initialize services
const pineconeClient = new Pinecone({
  apiKey: config.pinecone.apiKey,
});

// Initialize services
const webScraperService = new WebScraperService(pineconeClient, config.pinecone.indexName);
const weatherService = new WeatherService();

// Initialize communication service if Twilio credentials are available
let communicationService: CommunicationService | null = null;
if (config.twilio.sid && config.twilio.authToken && config.twilio.phoneNumber) {
  communicationService = new CommunicationService(
    config.twilio.sid,
    config.twilio.authToken,
    config.twilio.phoneNumber,
  );
} else {
  console.warn('Twilio credentials not found. SMS and call features will be disabled.');
}

/**
 * Initialize a calendar service with the provided API key
 * @param apiKey Cal.com API key
 * @returns Initialized CalendarService
 */
function initializeCalendarService(apiKey: string): CalendarService {
  return new CalendarService(apiKey);
}

/**
 * Create function context for the agent
 * @param participant Participant with attributes
 * @param calAPIKey Decrypted Cal.com API key
 * @returns Function context object
 */
function createFunctionContext(participant: any, calAPIKey: string): llm.FunctionContext {
  const url = participant.attributes.scrapingUrl;
  return {
    weather: {
      description: 'Get the weather in a location',
      parameters: z.object({
        location: z.string().describe('The location to get the weather for'),
      }),
      execute: async ({ location }) => weatherService.getWeather(location),
    },
    bookAppointmentInCalCom: {
      description: 'Book an appointment using Cal.com',
      parameters: z.object({
        date: z
          .string()
          .describe(
            'The date of appointment (e.g., "tomorrow at 3pm", "next Monday at 2pm", or "2024-03-20")',
          ),
        name: z.string().describe('Your full name'),
        email: z.string().email().describe('Your email address'),
        notes: z.string().optional().describe('Additional notes for the booking'),
      }),
      execute: async ({ date, name, email, notes }) => {
        const calendarService = initializeCalendarService(calAPIKey);
        const eventTypeId = Number(participant.attributes.calEventTypeId);
        const organizationSlug = participant.attributes.organizationSlug;

        return calendarService.bookAppointment({
          date,
          name,
          email,
          notes,
          eventTypeId,
          ...(organizationSlug && { organizationSlug }),
        });
      },
    },
    scrapeWebsite: {
      description: 'Scrape a website and store data in vector DB',
      parameters: z.object({}),
      execute: async () => webScraperService.scrapeWebsite(url),
    },
    ...(communicationService
      ? {
          sendSMS: {
            description: 'Send an SMS to a phone number',
            parameters: z.object({
              phoneNumber: z.string().describe('The phone number to send SMS to'),
              message: z.string().describe('The SMS message'),
            }),
            execute: async ({ phoneNumber, message }) => {
              return communicationService!.sendSMS(phoneNumber, message);
            },
          },
          makeCall: {
            description: 'Make a voice call to a phone number',
            parameters: z.object({
              phoneNumber: z.string().describe('The phone number to call'),
              message: z.string().describe('The message to say'),
            }),
            execute: async ({ phoneNumber, message }) => {
              return communicationService!.makeCall(phoneNumber, message);
            },
          },
        }
      : {}),
  };
}

/**
 * Create a voice pipeline agent based on user premium status
 * @param vad Voice Activity Detection instance
 * @param isPremium Whether the user has premium status
 * @param initialContext Initial chat context
 * @param fncCtx Function context
 * @returns Configured VoicePipelineAgent
 */
function createVoicePipelineAgent(
  vad: silero.VAD,
  isPremium: string | undefined,
  initialContext: llm.ChatContext,
  fncCtx: llm.FunctionContext,
): pipeline.VoicePipelineAgent {
  if (isPremium === 'true') {
    return new pipeline.VoicePipelineAgent(
      vad,
      new deepgram.STT(),
      new openai.LLM(),
      new elevenlabs.TTS(),
      { chatCtx: initialContext, fncCtx },
    );
  } else {
    return new pipeline.VoicePipelineAgent(
      vad,
      // new deepgram.STT(),
      // new openai.LLM({
      //   apiKey: config.googleGemini.apiKey,
      //   baseURL: config.openRouter.baseURL,
      //   model: config.openRouter.llmModel,
      // }),
      // new openai.TTS({
      //   apiKey: 'not-needed',
      //   baseURL: config.openai.baseURL,
      //   voice: 'am_adam' as openai.TTSVoices,
      // }),
      new deepgram.STT(),
      new openai.LLM(),
      // new elevenlabs.TTS(),
      new openai.TTS({
        apiKey: config.tts.apiKey,
        baseURL: config.tts.baseURL,
        voice: 'am_adam' as openai.TTSVoices,
      }),
      { chatCtx: initialContext, fncCtx },
    );
  }
}

/**
 * Main agent definition
 */
export default defineAgent({
  prewarm: async (proc: JobProcess) => {
    proc.userData.vad = await silero.VAD.load();
  },
  entry: async (ctx: JobContext) => {
    try {
      // Ensure VAD is loaded
      if (!ctx.proc.userData.vad) {
        console.error('VAD not loaded in prewarm phase');
        throw new Error('Voice Activity Detection not initialized');
      }

      const vad = ctx.proc.userData.vad as silero.VAD;

      try {
        await ctx.connect();
      } catch (connectError) {
        console.error('Failed to connect to room:', connectError);
        throw new Error('Failed to connect to LiveKit room');
      }

      let participant;
      try {
        participant = await ctx.waitForParticipant();
        console.log(`Participant joined: ${participant.identity}`);
      } catch (participantError) {
        console.error('Failed to get participant:', participantError);
        throw new Error('Failed to get participant from room');
      }

      // Create initial context with system prompt
      const initialContext = new llm.ChatContext().append({
        role: llm.ChatRole.SYSTEM,
        text:
          participant.attributes.systemPrompt ||
          'You are a voice assistant created by LiveKit. Your interface with users will be voice. ' +
            'You should use short and concise responses, and avoiding usage of unpronounceable ' +
            'punctuation.',
      });

      // Decrypt Cal.com API key from participant attributes with error handling
      let calAPIKey = '';
      if (participant.attributes.userCalId) {
        try {
          calAPIKey = await decrypt(participant.attributes.userCalId, secretKey);
        } catch (decryptError) {
          console.error('Failed to decrypt Cal.com API key:', decryptError);
          // Continue without Cal.com integration
        }
      }

      console.log(`Starting assistant for participant: ${participant.identity}`);

      // Create function context with available functions
      const fncCtx = createFunctionContext(participant, calAPIKey);

      // Create agent based on premium status
      const isPremium = participant.attributes.isPremium;
      const agent = createVoicePipelineAgent(vad, isPremium, initialContext, fncCtx);

      try {
        // Start the agent
        agent.start(ctx.room, participant);

        // Use custom welcome message if available
        const customMessage =
          participant.attributes.firstMessage || "Hello, I'm your voice assistant.";
        await agent.say(customMessage, true);
      } catch (agentError) {
        console.error('Error starting agent or saying welcome message:', agentError);
        // Try to recover by sending a message about the error
        try {
          await agent.say(
            "I'm experiencing some technical difficulties. Please bear with me.",
            true,
          );
        } catch (recoveryError) {
          console.error('Failed to send recovery message:', recoveryError);
          throw new Error('Agent failed to start and recovery failed');
        }
      }
    } catch (error) {
      console.error('Fatal error in agent entry point:', error);
      // Report the error to monitoring system if available
      // This is a fatal error, so we'll let the process crash and restart
      throw error;
    }
  },
});

// Run the application
cli.runApp(new WorkerOptions({ agent: fileURLToPath(import.meta.url) }));
