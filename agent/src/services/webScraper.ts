// SPDX-FileCopyrightText: 2024 LiveKit, Inc.
//
// SPDX-License-Identifier: Apache-2.0
import * as cheerio from 'cheerio';
import { chromium } from 'playwright';
import { Pinecone } from '@pinecone-database/pinecone';

/**
 * Service for scraping website content and storing it in Pinecone
 */
export class WebScraperService {
  private index: any;

  /**
   * Creates a new WebScraperService
   * @param pineconeClient Initialized Pinecone client
   * @param indexName Name of the Pinecone index to use
   */
  constructor(pineconeClient: Pinecone, indexName: string) {
    this.index = pineconeClient.index(indexName);
  }

  /**
   * Scrapes a website and stores the content in Pinecone
   * @param url URL of the website to scrape
   * @returns A message indicating the result of the operation
   */
  async scrapeWebsite(url: string): Promise<string> {
    const browser = await chromium.launch();
    const page = await browser.newPage();
    
    try {
      await page.goto(url);
      const content = await page.content();
      
      const $ = cheerio.load(content);
      const textData = $('body').text().replace(/\s+/g, ' ').trim();

      // Store in Pinecone
      await this.index.upsert([
        { 
          id: url, 
          values: textData.split(' ').map((w: string) => w.length), 
          metadata: { url } 
        },
      ]);

      return `Scraped and stored data from ${url}`;
    } catch (error) {
      console.error('Error scraping website:', error);
      throw new Error(`Failed to scrape website: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      await browser.close();
    }
  }
}
