// SPDX-FileCopyrightText: 2024 LiveKit, Inc.
//
// SPDX-License-Identifier: Apache-2.0
import twilio from 'twilio';

/**
 * Service for handling SMS and voice call communications
 */
export class CommunicationService {
  private twilioClient: twilio.Twilio;
  private fromPhoneNumber: string;

  /**
   * Creates a new CommunicationService
   * @param twilioSid Twilio account SID
   * @param twilioAuthToken Twilio auth token
   * @param fromPhoneNumber Phone number to send messages from
   */
  constructor(twilioSid: string, twilioAuthToken: string, fromPhoneNumber: string) {
    this.twilioClient = twilio(twilioSid, twilioAuthToken);
    this.fromPhoneNumber = fromPhoneNumber;
  }

  /**
   * Sends an SMS message
   * @param phoneNumber Recipient phone number
   * @param message Message content
   * @returns A message indicating the result of the operation
   */
  async sendSMS(phoneNumber: string, message: string): Promise<string> {
    const cleanPhoneNumber = this.cleanPhoneNumber(phoneNumber);
    
    console.log(`Sending SMS to cleaned number: ${cleanPhoneNumber}`);
    console.log(`Original number was: ${phoneNumber}`);

    try {
      await this.twilioClient.messages.create({
        body: message,
        from: this.fromPhoneNumber,
        to: cleanPhoneNumber,
      });
      return `SMS sent to ${cleanPhoneNumber}`;
    } catch (error) {
      console.error('Error sending SMS:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return `Failed to send SMS: ${errorMessage}. Please check the phone number format and try again.`;
    }
  }

  /**
   * Makes a voice call
   * @param phoneNumber Recipient phone number
   * @param message Message to say during the call
   * @returns A message indicating the result of the operation
   */
  async makeCall(phoneNumber: string, message: string): Promise<string> {
    const cleanPhoneNumber = this.cleanPhoneNumber(phoneNumber);
    
    console.log(`Making call to cleaned number: ${cleanPhoneNumber}`);
    console.log(`Original number was: ${phoneNumber}`);

    try {
      await this.twilioClient.calls.create({
        twiml: `<Response><Say>${message}</Say></Response>`,
        from: this.fromPhoneNumber,
        to: cleanPhoneNumber,
      });
      return `Call initiated to ${cleanPhoneNumber}`;
    } catch (error) {
      console.error('Error making call:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return `Failed to make call: ${errorMessage}. Please check the phone number format and try again.`;
    }
  }

  /**
   * Cleans a phone number from speech recognition
   * @param phoneNumber Raw phone number
   * @returns Cleaned phone number
   */
  private cleanPhoneNumber(phoneNumber: string): string {
    let cleanPhoneNumber = phoneNumber
      .toLowerCase()
      .replace(/\s+/g, '') // Remove spaces
      .replace(/zero/gi, '0')
      .replace(/one/gi, '1')
      .replace(/two/gi, '2')
      .replace(/three/gi, '3')
      .replace(/four/gi, '4')
      .replace(/five/gi, '5')
      .replace(/six/gi, '6')
      .replace(/seven/gi, '7')
      .replace(/eight/gi, '8')
      .replace(/nine/gi, '9')
      .replace(/[^0-9+]/g, ''); // Remove any non-numeric characters except +

    // Add country code if missing
    if (cleanPhoneNumber.startsWith('0')) {
      cleanPhoneNumber = '+880' + cleanPhoneNumber.substring(1); // Bangladesh country code
    } else if (!cleanPhoneNumber.startsWith('+')) {
      cleanPhoneNumber = '+1' + cleanPhoneNumber; // Default to US
    }

    return cleanPhoneNumber;
  }
}
