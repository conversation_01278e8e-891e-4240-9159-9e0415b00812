// SPDX-FileCopyrightText: 2024 LiveKit, Inc.
//
// SPDX-License-Identifier: Apache-2.0

/**
 * Service for fetching weather information
 */
export class WeatherService {
  /**
   * Gets the current weather for a location
   * @param location The location to get weather for
   * @returns A formatted weather report
   */
  async getWeather(location: string): Promise<string> {
    console.debug(`Executing weather function for ${location}`);
    
    try {
      const response = await fetch(`https://wttr.in/${encodeURIComponent(location)}?format=%C+%t`);
      
      if (!response.ok) {
        throw new Error(`Weather API returned status: ${response.status}`);
      }
      
      const weather = await response.text();
      return `The weather in ${location} right now is ${weather}.`;
    } catch (error) {
      console.error('Error fetching weather:', error);
      throw new Error(`Failed to get weather: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
