// SPDX-FileCopyrightText: 2024 LiveKit, Inc.
//
// SPDX-License-Identifier: Apache-2.0

/**
 * Interface for Cal.com booking parameters
 */
export interface BookingParams {
  date: string;
  name: string;
  email: string;
  notes?: string;
  eventTypeId: number;
  organizationSlug?: string;
}

/**
 * Service for handling Cal.com calendar bookings
 */
export class CalendarService {
  private apiKey: string;

  /**
   * Creates a new CalendarService
   * @param apiKey Cal.com API key
   */
  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  /**
   * Books an appointment using Cal.com API
   * @param params Booking parameters
   * @returns A message indicating the result of the booking
   */
  /**
   * Fetches data with a timeout
   * @param url URL to fetch
   * @param options Fetch options
   * @param timeoutMs Timeout in milliseconds
   * @returns Response from fetch
   */
  private async fetchWithTimeout(
    url: string,
    options: RequestInit,
    timeoutMs: number = 10000,
  ): Promise<Response> {
    const controller = new AbortController();
    const { signal } = controller;

    // Create a timeout that will abort the fetch if it takes too long
    const timeout = setTimeout(() => {
      controller.abort();
    }, timeoutMs);

    try {
      const response = await fetch(url, {
        ...options,
        signal,
      });
      return response;
    } finally {
      clearTimeout(timeout);
    }
  }

  async bookAppointment(params: BookingParams): Promise<string> {
    try {
      // Validate API key
      if (!this.apiKey) {
        console.error('Cal.com API key is missing');
        return "I'm unable to book appointments at the moment due to a configuration issue. Please try again later or contact support.";
      }

      // Validate required parameters
      if (!params.email || !params.name || !params.date || !params.eventTypeId) {
        console.error('Missing required booking parameters', { params });
        return "I couldn't process your booking request because some required information is missing. Please provide your name, email, and the date you'd like to book.";
      }

      // Clean up email address from speech recognition
      const cleanEmail = this.cleanEmail(params.email);

      try {
        // Parse and validate the date
        const dateObj = this.parseDate(params.date);
        const dateTime = dateObj.toISOString();

        // Validate the appointment time is in the future
        if (dateObj.getTime() <= Date.now()) {
          throw new Error('Appointment time must be in the future');
        }

        console.debug(`Booking Cal.com appointment for ${params.name} at ${dateTime}`);
        console.debug(`Using email: ${cleanEmail}`);

        try {
          // Make API request with timeout
          const response = await this.fetchWithTimeout(
            'https://api.cal.com/v2/bookings',
            {
              method: 'POST',
              headers: {
                'cal-api-version': '2024-08-13',
                'Content-Type': 'application/json',
                Authorization: `Bearer ${this.apiKey}`,
              },
              body: JSON.stringify({
                attendee: {
                  language: 'en',
                  name: params.name,
                  email: cleanEmail,
                  timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                  ...(params.notes && { notes: params.notes }),
                },
                start: dateTime,
                eventTypeId: params.eventTypeId,
                // Include organizationSlug if available
                ...(params.organizationSlug && {
                  organizationSlug: params.organizationSlug,
                }),
              }),
            },
            15000, // 15 second timeout
          );

          if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            console.error('Cal.com API error:', JSON.stringify(errorData, null, 2));
            throw new Error(
              `Booking failed: ${response.status} - ${errorData?.error?.message || 'Unknown error'}`,
            );
          }

          const booking = await response.json().catch((err) => {
            console.error('Error parsing booking response:', err);
            throw new Error('Failed to parse booking response');
          });

          console.log('Cal.com booking response:', booking);

          // Format the response date for better readability
          const formattedDate = dateObj.toLocaleString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric',
            timeZoneName: 'short',
          });

          return `I've booked your ${params.eventTypeId} for ${formattedDate}. A confirmation email will be sent to ${cleanEmail}. Please let me know if you need to make any changes.`;
        } catch (apiError) {
          console.error('API request error:', apiError);
          if (apiError instanceof Error && apiError.name === 'AbortError') {
            return "I couldn't complete the booking because the request timed out. Please try again later.";
          }
          throw apiError; // Re-throw to be handled by the outer catch
        }
      } catch (dateError) {
        console.error('Date parsing error:', dateError);
        if (dateError instanceof Error) {
          if (dateError.message.includes('date format')) {
            return "I couldn't understand the date and time you provided. Could you please specify it differently? For example, 'tomorrow at 3pm' or 'next Monday at 2pm'.";
          } else if (dateError.message.includes('future')) {
            return 'The appointment time needs to be in the future. Could you please provide a different time?';
          }
        }
        throw dateError; // Re-throw to be handled by the outer catch
      }
    } catch (error) {
      console.error('Error booking Cal.com appointment:', error);

      if (error instanceof Error) {
        // Handle specific error messages
        if (
          error.message.includes('already has booking') ||
          error.message.includes('not available')
        ) {
          return "I'm sorry, but that time slot is not available. The person you're trying to book with either already has another appointment at that time or has marked themselves as unavailable. Could you please try a different time? I recommend checking for availability in the next few days.";
        }
      }

      return "I couldn't book the appointment. Please make sure the email address and date/time are correct and try again.";
    }
  }

  /**
   * Cleans an email address from speech recognition
   * @param email Raw email address
   * @returns Cleaned email address
   */
  private cleanEmail(email: string): string {
    return email
      .toLowerCase()
      .replace(/\s+/g, '') // Remove spaces
      .replace(/dot\s*/gi, '.') // Replace "dot" with .
      .replace(/at\s*/gi, '@') // Replace "at" with @
      .replace(/gmail\s*dot\s*com/gi, 'gmail.com')
      .replace(/yahoo\s*dot\s*com/gi, 'yahoo.com')
      .replace(/hotmail\s*dot\s*com/gi, 'hotmail.com');
  }

  /**
   * Parses a date string from various formats
   * @param dateStr Date string to parse
   * @returns Date object
   */
  private parseDate(dateStr: string): Date {
    // Normalize date input
    const cleanDate = dateStr.toLowerCase().replace(/\s+/g, ' ').trim();

    // Handle common speech-to-text date patterns
    let dateObj: Date;

    if (cleanDate.includes('today')) {
      dateObj = new Date();
      // Extract time if provided
      const timeMatch = cleanDate.match(/(\d{1,2})(?::\d{2})?\s*(am|pm)/i);
      if (timeMatch) {
        const [, hour, meridiem] = timeMatch;
        let hours = parseInt(hour);
        if (meridiem?.toLowerCase() === 'pm' && hours !== 12) hours += 12;
        if (meridiem?.toLowerCase() === 'am' && hours === 12) hours = 0;
        dateObj.setHours(hours, 0, 0, 0);
      }
    } else if (cleanDate.includes('tomorrow')) {
      dateObj = new Date();
      dateObj.setDate(dateObj.getDate() + 1);
      // Extract time if provided
      const timeMatch = cleanDate.match(/(\d{1,2})(?::\d{2})?\s*(am|pm)/i);
      if (timeMatch) {
        const [, hour, meridiem] = timeMatch;
        let hours = parseInt(hour);
        if (meridiem?.toLowerCase() === 'pm' && hours !== 12) hours += 12;
        if (meridiem?.toLowerCase() === 'am' && hours === 12) hours = 0;
        dateObj.setHours(hours, 0, 0, 0);
      }
    } else {
      // Try to parse various date formats
      const parsedDate = new Date(cleanDate);
      if (isNaN(parsedDate.getTime())) {
        throw new Error('Could not understand the date format');
      }
      dateObj = parsedDate;
    }

    return dateObj;
  }
}
