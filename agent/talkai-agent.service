[Unit]
Description=tAlkai Agent Service
After=network.target

[Service]
Type=simple
User=rakib
WorkingDirectory=/home/<USER>/Codes/noelstrachan/tAlkai247/agent
ExecStart=/bin/bash -c './start-agent.sh start'
Restart=always
RestartSec=10
# Increase memory limit to 8GB
MemoryLimit=8G
# Environment variables
Environment=NODE_OPTIONS="--max-old-space-size=4096 --expose-gc"

[Install]
WantedBy=multi-user.target
