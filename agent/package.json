{"name": "voice-pipeline-agent-node", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "tsc", "lint": "eslint -f unix \"**/*.ts\""}, "packageManager": "pnpm@10.6.5", "devDependencies": {"@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.22.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/node": "^22.13.10", "@typescript-eslint/eslint-plugin": "^8.27.0", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.3", "typescript": "^5.8.2"}, "dependencies": {"@livekit/agents": "^0.7.1", "@livekit/agents-plugin-deepgram": "^0.5.4", "@livekit/agents-plugin-elevenlabs": "^0.6.2", "@livekit/agents-plugin-openai": "^0.9.0", "@livekit/agents-plugin-silero": "^0.5.5", "@livekit/rtc-node": "^0.13.8", "@pinecone-database/pinecone": "^5.1.1", "cheerio": "^1.0.0", "dotenv": "^16.4.7", "playwright": "^1.51.1", "twilio": "^5.5.1", "zod": "^3.24.2"}}