{"_events": {"volume": [null, null], "error": [null, null, null], "disconnect": [null, null]}, "_eventsCount": 7, "parameters": {"CallSid": "CAc528385005450bbdfe8049d80c8e419d"}, "_inputVolumeStreak": 0, "_isAnswered": true, "_isCancelled": false, "_isRejected": false, "_latestInputVolume": 0.3879901960784314, "_latestOutputVolume": 0, "_log": {"_log": {"name": "@twilio/voice-sdk", "levels": {"TRACE": 0, "DEBUG": 1, "INFO": 2, "WARN": 3, "ERROR": 4, "SILENT": 5}}, "_prefix": "[<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>][Call]"}, "_mediaStatus": "open", "_messages": {}, "_metricsSamples": [], "_options": {"enableImprovedSignalingErrorPrecision": false, "offerSdp": null, "codecPreferences": ["pcmu", "opus"], "customSounds": {}, "dialtonePlayer": {"_context": {}, "_gainNodes": [{}, {}, {}, {}]}, "dscp": true, "forceAggressiveIceNomination": false, "preflight": false, "twimlParams": {"contactId": "cma0axdh800019rk6lvl0jrio", "To": "+8801983904080"}}, "_outputVolumeStreak": 0, "_shouldSendHangup": true, "_signalingStatus": "open", "_soundcache": {}, "_status": "closed", "_wasConnected": true, "_isUnifiedPlanDefault": true, "customParameters": {}, "_direction": "OUTGOING", "callerInfo": null, "_mediaReconnectBackoff": {"_events": {}, "_eventsCount": 1}, "_codec": "PCMU"}