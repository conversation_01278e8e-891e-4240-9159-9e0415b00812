# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Environment variables
.env
server/.env
./**/.env

# Dependencies
node_modules/

# Build files
dist/
dist-ssr/

# Local development files
.env
node_modules
dist
dist-ssr
*.local
server/.env
cartesia-js/
sample.mp3

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Specific files/folders
cartesia-js/
sample.mp3
ca-certificate.crt