{"name": "talkai247", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:server": "tsc -p tsconfig.server.json", "build:agent": "cd agent && pnpm run build", "start:server": "node server/index.js", "start:agent": "node agent/dist/agent.js start", "dev:server": "nodemon --watch 'src/**/*.ts' --exec 'npm run build:server && npm run start:server'", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@deepgram/sdk": "^3.0.0", "@livekit/components-react": "^2.8.1", "@livekit/components-styles": "^1.1.4", "@livekit/krisp-noise-filter": "^0.2.16", "@prisma/client": "^6.0.1", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@twilio/voice-sdk": "^2.12.4", "axios": "^1.7.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^1.0.4", "date-fns": "^3.3.1", "framer-motion": "^12.5.0", "gsap": "^3.12.7", "livekit-client": "^2.9.5", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-day-picker": "^8.10.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.2", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "three": "^0.175.0", "zod": "^3.24.1"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20.17.10", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@types/three": "^0.175.0", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "nodemon": "^2.0.22", "postcss": "^8.4.35", "prisma": "^6.0.1", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.7.2", "vite": "^5.1.4"}, "prisma": {"seed": "tsx prisma/seed.ts"}}