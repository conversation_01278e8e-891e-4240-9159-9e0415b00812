import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Eye,
  Edit,
  Trash2,
  Loader2,
  BarChart,
  Activity,
  Settings,
  FileText,
  MessageSquare,
  Users,
} from "lucide-react";
import { useCampaigns } from "@/hooks/useCampaigns";
import { Campaign, Contact, CampaignGoal } from "@/types/schema";
import { toast } from "@/components/ui/use-toast";
import { format } from "date-fns";

// Import components from the campaigns directory
import { ContactSelector } from "@/components/campaigns/ContactSelector";
import { CampaignMetrics } from "@/components/campaigns/CampaignMetrics";
import { GoalManager } from "@/components/campaigns/GoalManager";
import { CsvImportExport } from "@/components/campaigns/CsvImportExport";
import { CampaignAnalytics } from "@/components/campaigns/CampaignAnalytics";
import { CampaignAutomation } from "@/components/campaigns/CampaignAutomation";
import { CampaignTemplates } from "@/components/campaigns/CampaignTemplates";
import { MultiChannelSettings } from "@/components/campaigns/MultiChannelSettings";
import { TeamCollaboration } from "@/components/campaigns/TeamCollaboration";

interface CampaignFormData {
  id?: string;
  name: string;
  startDate: string;
  startTime: string;
  phoneNumber: string;
  assistant: string;
  description: string;
  selectedContacts: string[];
  status: string;
}

export default function Campaigns() {
  // Use the custom hook for campaign management
  const {
    campaigns,
    selectedCampaign,
    setSelectedCampaign,
    isLoading,
    createCampaign,
    updateCampaign,
    deleteCampaign,
    fetchCampaigns,
    updateCampaignGoals,
    updateChannelSettings,
  } = useCampaigns();

  const [isCreating, setIsCreating] = useState(true);
  const [showDetails, setShowDetails] = useState(false);
  const [formData, setFormData] = useState<CampaignFormData>({
    name: "",
    startDate: "",
    startTime: "",
    phoneNumber: "",
    assistant: "",
    description: "",
    selectedContacts: [],
    status: "DRAFT",
  });

  // State for campaign form and contacts
  const [selectedContactIds, setSelectedContactIds] = useState<string[]>([]);
  const [campaignGoals, setCampaignGoals] = useState<CampaignGoal[]>([]);

  // Reset form when switching between create and edit modes
  useEffect(() => {
    if (isCreating) {
      setFormData({
        name: "",
        startDate: "",
        startTime: "",
        phoneNumber: "",
        assistant: "",
        description: "",
        selectedContacts: [],
        status: "DRAFT",
      });
      setSelectedContactIds([]);
      setCampaignGoals([]);
    } else if (selectedCampaign) {
      // Format the date for the form
      const campaignDate =
        selectedCampaign.startDate instanceof Date
          ? selectedCampaign.startDate
          : new Date(selectedCampaign.startDate);

      // Get campaign data and goals
      const goals = selectedCampaign.goals || [];

      setFormData({
        id: selectedCampaign.id,
        name: selectedCampaign.name || "",
        startDate: format(campaignDate, "yyyy-MM-dd"),
        startTime: format(campaignDate, "HH:mm"),
        phoneNumber: selectedCampaign.phoneNumberId || "",
        assistant: selectedCampaign.assistantId || "",
        description: selectedCampaign.description || "",
        selectedContacts: [],
        status: selectedCampaign.status || "DRAFT",
      });

      // Set campaign goals
      setCampaignGoals(goals);

      // Set selected contacts
      setSelectedContactIds(
        Array.isArray(selectedCampaign.contacts)
          ? selectedCampaign.contacts.map((c: Contact) => c.id)
          : []
      );
    }
  }, [isCreating, selectedCampaign]);

  // Refresh campaigns when component mounts
  useEffect(() => {
    fetchCampaigns();
  }, [fetchCampaigns]);

  const handleCreateCampaign = async () => {
    if (!formData.name || !formData.startDate) {
      toast({
        title: "Validation Error",
        description: "Name and start date are required",
        variant: "destructive",
      });
      return;
    }

    try {
      // Prepare campaign data
      const campaignData = {
        userId: "current-user-id", // This would come from auth context in a real app
        name: formData.name,
        description: formData.description,
        startDate: new Date(
          `${formData.startDate}T${formData.startTime || "00:00"}`
        ),
        status: formData.status.toUpperCase() as
          | "DRAFT"
          | "SCHEDULED"
          | "ACTIVE"
          | "COMPLETED"
          | "CANCELLED"
          | "PAUSED",
        contacts: selectedContactIds,
        goals: campaignGoals,
        metrics: {
          totalCalls: 0,
          successfulCalls: 0,
          failedCalls: 0,
          averageDuration: 0,
          averageSentiment: 0,
        },
        automationSettings: null,
        callScript: "",
        assistantId: formData.assistant || undefined,
        phoneNumberId: formData.phoneNumber || undefined,
        channelSettings: {
          voice: { enabled: true, message: "" },
          sms: { enabled: false },
          email: { enabled: false },
        },
      };

      // Create campaign
      const result = await createCampaign(campaignData);

      if (result) {
        setIsCreating(false);
        setSelectedCampaign(result);

        // Reset form
        setFormData({
          name: "",
          startDate: "",
          startTime: "",
          phoneNumber: "",
          assistant: "",
          description: "",
          selectedContacts: [],
          status: "DRAFT",
        });
        setSelectedContactIds([]);
        setCampaignGoals([]);

        toast({
          title: "Success",
          description: "Campaign created successfully",
        });
      }
    } catch (err) {
      console.error("Error creating campaign:", err);
      toast({
        title: "Error",
        description: "Failed to create campaign",
        variant: "destructive",
      });
    }
  };

  const handleUpdateCampaign = async () => {
    if (!selectedCampaign || !formData.id) {
      toast({
        title: "Error",
        description: "No campaign selected for update",
        variant: "destructive",
      });
      return;
    }

    try {
      // Prepare campaign data
      const campaignData = {
        name: formData.name,
        description: formData.description,
        startDate: new Date(
          `${formData.startDate}T${formData.startTime || "00:00"}`
        ),
        status: formData.status.toUpperCase() as
          | "DRAFT"
          | "SCHEDULED"
          | "ACTIVE"
          | "COMPLETED"
          | "CANCELLED"
          | "PAUSED",
        contacts: selectedContactIds,
        goals: campaignGoals,
        assistantId: formData.assistant || undefined,
        phoneNumberId: formData.phoneNumber || undefined,
      };

      // Update campaign
      const result = await updateCampaign(formData.id, campaignData);

      if (result) {
        toast({
          title: "Success",
          description: "Campaign updated successfully",
        });
      }
    } catch (err) {
      console.error("Error updating campaign:", err);
      toast({
        title: "Error",
        description: "Failed to update campaign",
        variant: "destructive",
      });
    }
  };

  const handleDeleteCampaign = async (id: string) => {
    if (confirm("Are you sure you want to delete this campaign?")) {
      try {
        await deleteCampaign(id);

        if (selectedCampaign?.id === id) {
          setSelectedCampaign(null);
          setIsCreating(true);
        }

        toast({
          title: "Success",
          description: "Campaign deleted successfully",
        });
      } catch (error) {
        console.error("Error deleting campaign:", error);
        toast({
          title: "Error",
          description: "Failed to delete campaign",
          variant: "destructive",
        });
      }
    }
  };

  const handleSelectCampaign = (campaign: Campaign) => {
    setSelectedCampaign(campaign);
    setIsCreating(false);
  };

  // Handle contacts selection from ContactSelector component
  const handleContactsSelected = useCallback((contactIds: string[]) => {
    setSelectedContactIds(contactIds);
  }, []);

  // Handle imported contacts from CSV
  const handleContactsImported = (contacts: Partial<Contact>[]) => {
    toast({
      title: "Contacts Imported",
      description: `${contacts.length} contacts imported successfully.`,
    });
  };

  // Handle campaign goals update
  const handleGoalsUpdate = useCallback(
    (goals: CampaignGoal[]) => {
      setCampaignGoals(goals);

      // If editing an existing campaign, update goals in the backend
      if (!isCreating && selectedCampaign && selectedCampaign.id) {
        updateCampaignGoals(selectedCampaign.id, goals);
      }
    },
    [isCreating, selectedCampaign, updateCampaignGoals]
  );

  // Handle channel settings update
  const handleChannelSettingsUpdate = useCallback(
    (settings: any) => {
      if (!isCreating && selectedCampaign && selectedCampaign.id) {
        updateChannelSettings(selectedCampaign.id, settings);
      }
    },
    [isCreating, selectedCampaign, updateChannelSettings]
  );

  return (
    <div className="p-8 bg-gray-900 text-white">
      <h2 className="text-3xl font-bold mb-6">Outbound Campaigns</h2>
      {isLoading && !campaigns.length && (
        <div className="flex justify-center items-center h-40">
          <Loader2 className="h-8 w-8 animate-spin text-teal-500" />
          <span className="ml-2 text-teal-500">Loading campaigns...</span>
        </div>
      )}

      {(!isLoading || campaigns.length > 0) && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="bg-gray-800 border-gray-700 col-span-1">
              <CardHeader>
                <CardTitle className="text-white">Campaign List</CardTitle>
              </CardHeader>
              <CardContent>
                <Button
                  className="w-full mb-4 bg-teal-600 hover:bg-teal-700 text-white"
                  onClick={() => {
                    setIsCreating(true);
                    setSelectedCampaign(null);
                  }}
                >
                  + New Campaign
                </Button>
                <ScrollArea className="h-[calc(100vh-300px)]">
                  {campaigns.length === 0 ? (
                    <div className="text-center p-4 text-gray-400">
                      No campaigns found. Create your first campaign!
                    </div>
                  ) : (
                    campaigns.map((campaign) => (
                      <div
                        key={campaign.id}
                        className={`flex items-center justify-between p-2 mb-2 ${
                          selectedCampaign?.id === campaign.id
                            ? "bg-gray-600"
                            : "bg-gray-700"
                        }  rounded cursor-pointer transition-colors hover:bg-gray-600`}
                        onClick={() => handleSelectCampaign(campaign)}
                      >
                        <div className="flex flex-col">
                          <span className="text-white font-medium">
                            {campaign.name}
                          </span>
                          <span className="text-xs text-gray-400">
                            {campaign.startDate instanceof Date
                              ? format(campaign.startDate, "MMM dd, yyyy")
                              : format(
                                  new Date(campaign.startDate),
                                  "MMM dd, yyyy"
                                )}
                            {" • "}
                            {campaign.status}
                          </span>
                        </div>
                        <div>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleSelectCampaign(campaign);
                              setShowDetails(true);
                            }}
                          >
                            <Eye className="h-4 w-4 text-teal-400" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleSelectCampaign(campaign);
                            }}
                          >
                            <Edit className="h-4 w-4 text-teal-400" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteCampaign(campaign.id);
                            }}
                          >
                            <Trash2 className="h-4 w-4 text-red-400" />
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                </ScrollArea>
              </CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700 col-span-2">
              <CardHeader>
                <CardTitle className="text-white">
                  {isCreating ? "Create New Campaign" : "Edit Campaign"}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="details" className="w-full">
                  <TabsList className="bg-gray-700 mb-4">
                    <TabsTrigger
                      value="details"
                      className="data-[state=active]:bg-gray-600"
                    >
                      Details
                    </TabsTrigger>
                    <TabsTrigger
                      value="contacts"
                      className="data-[state=active]:bg-gray-600"
                    >
                      Contacts
                    </TabsTrigger>
                    <TabsTrigger
                      value="goals"
                      className="data-[state=active]:bg-gray-600"
                    >
                      Goals
                    </TabsTrigger>
                    <TabsTrigger
                      value="templates"
                      className="data-[state=active]:bg-gray-600"
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Templates
                    </TabsTrigger>
                    <TabsTrigger
                      value="channels"
                      className="data-[state=active]:bg-gray-600"
                    >
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Channels
                    </TabsTrigger>
                    <TabsTrigger
                      value="team"
                      className="data-[state=active]:bg-gray-600"
                    >
                      <Users className="h-4 w-4 mr-2" />
                      Team
                    </TabsTrigger>
                    {selectedCampaign && (
                      <>
                        <TabsTrigger
                          value="metrics"
                          className="data-[state=active]:bg-gray-600"
                        >
                          <BarChart className="h-4 w-4 mr-2" />
                          Metrics
                        </TabsTrigger>
                        <TabsTrigger
                          value="analytics"
                          className="data-[state=active]:bg-gray-600"
                        >
                          <Activity className="h-4 w-4 mr-2" />
                          Analytics
                        </TabsTrigger>
                        <TabsTrigger
                          value="automation"
                          className="data-[state=active]:bg-gray-600"
                        >
                          <Settings className="h-4 w-4 mr-2" />
                          Automation
                        </TabsTrigger>
                      </>
                    )}
                  </TabsList>

                  <TabsContent value="details" className="space-y-4">
                    <div>
                      <Label htmlFor="campaign-name" className="text-white">
                        Campaign Name:
                      </Label>
                      <Input
                        id="campaign-name"
                        className="bg-gray-700 text-white border-gray-600"
                        placeholder="Enter campaign name"
                        value={formData.name}
                        onChange={(e) =>
                          setFormData({ ...formData, name: e.target.value })
                        }
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="status" className="text-white">
                          Status:
                        </Label>
                        <Select
                          value={formData.status}
                          onValueChange={(value) =>
                            setFormData({ ...formData, status: value })
                          }
                        >
                          <SelectTrigger
                            id="status"
                            className="bg-gray-700 text-white border-gray-600"
                          >
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="DRAFT">Draft</SelectItem>
                            <SelectItem value="SCHEDULED">Scheduled</SelectItem>
                            <SelectItem value="ACTIVE">Active</SelectItem>
                            <SelectItem value="PAUSED">Paused</SelectItem>
                            <SelectItem value="COMPLETED">Completed</SelectItem>
                            <SelectItem value="CANCELLED">Cancelled</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="assistant" className="text-white">
                          Assistant:
                        </Label>
                        <Select
                          value={formData.assistant}
                          onValueChange={(value) =>
                            setFormData({ ...formData, assistant: value })
                          }
                        >
                          <SelectTrigger
                            id="assistant"
                            className="bg-gray-700 text-white border-gray-600"
                          >
                            <SelectValue placeholder="Select assistant" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="assistant1">
                              Assistant 1
                            </SelectItem>
                            <SelectItem value="assistant2">
                              Assistant 2
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="set-date" className="text-white">
                          Start Date:
                        </Label>
                        <Input
                          id="set-date"
                          type="date"
                          className="bg-gray-700 text-white border-gray-600"
                          value={formData.startDate}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              startDate: e.target.value,
                            })
                          }
                        />
                      </div>
                      <div>
                        <Label htmlFor="set-time" className="text-white">
                          Start Time:
                        </Label>
                        <Input
                          id="set-time"
                          type="time"
                          className="bg-gray-700 text-white border-gray-600"
                          value={formData.startTime}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              startTime: e.target.value,
                            })
                          }
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="outbound-phone" className="text-white">
                        Outbound Phone:
                      </Label>
                      <Select
                        value={formData.phoneNumber}
                        onValueChange={(value) =>
                          setFormData({ ...formData, phoneNumber: value })
                        }
                      >
                        <SelectTrigger
                          id="outbound-phone"
                          className="bg-gray-700 text-white border-gray-600"
                        >
                          <SelectValue placeholder="Select outbound phone" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="phone1">
                            +****************
                          </SelectItem>
                          <SelectItem value="phone2">
                            +****************
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="description" className="text-white">
                        Description:
                      </Label>
                      <Textarea
                        id="description"
                        className="bg-gray-700 text-white border-gray-600"
                        placeholder="Enter campaign description"
                        value={formData.description}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            description: e.target.value,
                          })
                        }
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="contacts" className="space-y-4">
                    <ContactSelector
                      selectedContactIds={selectedContactIds}
                      onContactsSelected={handleContactsSelected}
                      campaignId={formData.id}
                    />

                    <div className="mt-6">
                      <h3 className="text-lg font-medium text-white mb-2">
                        Import/Export Contacts
                      </h3>
                      <CsvImportExport
                        onContactsImported={handleContactsImported}
                      />
                    </div>
                  </TabsContent>

                  <TabsContent value="goals" className="space-y-4">
                    <GoalManager
                      goals={campaignGoals}
                      onChange={handleGoalsUpdate}
                    />
                  </TabsContent>

                  <TabsContent value="templates" className="space-y-4">
                    <CampaignTemplates
                      onSelectTemplate={(template) => {
                        // Apply template to current campaign
                        const templateData = template.campaignData;
                        setFormData({
                          ...formData,
                          name: formData.name || `${template.name} Campaign`,
                          description:
                            formData.description || template.description || "",
                          status: "DRAFT",
                        });
                        setCampaignGoals(templateData.goals || []);
                        toast({
                          title: "Template Applied",
                          description: `Template "${template.name}" has been applied to the campaign.`,
                        });
                      }}
                      currentCampaign={{
                        name: formData.name,
                        description: formData.description,
                        startDate: formData.startDate
                          ? new Date(formData.startDate)
                          : new Date(),
                        status: formData.status as any,
                        goals: campaignGoals,
                      }}
                    />
                  </TabsContent>

                  <TabsContent value="channels" className="space-y-4">
                    <MultiChannelSettings
                      onSave={handleChannelSettingsUpdate}
                      initialConfig={
                        selectedCampaign?.channelSettings
                          ? {
                              voice: selectedCampaign.channelSettings.voice || {
                                enabled: true,
                              },
                              sms: selectedCampaign.channelSettings.sms || {
                                enabled: false,
                              },
                              email: selectedCampaign.channelSettings.email || {
                                enabled: false,
                              },
                            }
                          : undefined
                      }
                    />
                  </TabsContent>

                  <TabsContent value="team" className="space-y-4">
                    <TeamCollaboration campaignId={formData.id || "new"} />
                  </TabsContent>

                  {selectedCampaign && (
                    <>
                      <TabsContent value="metrics">
                        <CampaignMetrics campaign={selectedCampaign} />
                      </TabsContent>

                      <TabsContent value="analytics">
                        <CampaignAnalytics campaign={selectedCampaign} />
                      </TabsContent>

                      <TabsContent value="automation">
                        <CampaignAutomation
                          campaign={selectedCampaign}
                          onUpdate={updateCampaign}
                        />
                      </TabsContent>
                    </>
                  )}

                  <div className="mt-6">
                    <Button
                      className="w-full bg-orange-600 hover:bg-orange-700 text-white"
                      onClick={
                        isCreating ? handleCreateCampaign : handleUpdateCampaign
                      }
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {isCreating ? "Creating..." : "Updating..."}
                        </>
                      ) : isCreating ? (
                        "Create Campaign"
                      ) : (
                        "Update Campaign"
                      )}
                    </Button>
                  </div>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          <Dialog open={showDetails} onOpenChange={setShowDetails}>
            <DialogContent className="bg-gray-900 text-white">
              <DialogHeader>
                <DialogTitle>Campaign Details</DialogTitle>
              </DialogHeader>
              {selectedCampaign && (
                <CampaignAnalytics campaign={selectedCampaign} />
              )}
              <Button
                className="w-full bg-orange-600 hover:bg-orange-700 text-white mt-4"
                onClick={() => setShowDetails(false)}
              >
                Back to Campaigns
              </Button>
            </DialogContent>
          </Dialog>
        </>
      )}
    </div>
  );
}
