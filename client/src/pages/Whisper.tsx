"use client";

import { Card } from "@/components/ui/card";
import { TooltipProvider } from "@/components/ui/tooltip";
import { CallInterface } from "@/components/Whisper/CallInterface";
import { WhisperTemplates } from "@/components/Whisper/WhisperTemplates";
import { ContactSelector, Contact } from "@/components/Whisper/ContactSelector";
import { useWhisperState } from "@/components/Whisper/hooks/useWhisperState";
import { GoalProgress } from "@/components/Whisper/GoalProgress";
import { WhisperControls } from "@/components/Whisper/WhisperControls";
import { WhisperModeSelector } from "@/components/Whisper/WhisperModeSelector";
import { WhisperInteractionPanel } from "@/components/Whisper/WhisperInteractionPanel";

export default function WhisperTab() {
  const {
    state,
    set,
    handleStartCall,
    handleEndCall,
    handleSendMessage,
    handleVoiceInput,
    setWhisperMode,
    triggerAiAgent,
    getRoomState,
  } = useWhisperState();

  const handleSelectContact = (contact: Contact) => {
    set("selectedContact", contact);
  };

  const handleToggleMute = () => {
    set("micMuted", !state.micMuted);
  };

  const handleVolumeChange = (volume: number) => {
    set("volume", volume);
  };

  const handleSendWhisper = async (message: string) => {
    // Add whisper interaction to transcript
    const whisperInteraction = {
      type: "user" as const,
      message: `[Whisper to AI] ${message}`,
      timestamp: new Date().toISOString(),
    };

    set("callTranscript", [...state.callTranscript, whisperInteraction]);

    // In a real implementation, this would send the whisper to the AI agent
    // For now, we'll simulate an AI response
    setTimeout(() => {
      const aiResponse = {
        type: "ai" as const,
        message: "I understand. I'll help guide the conversation accordingly.",
        timestamp: new Date().toISOString(),
      };
      set("callTranscript", [
        ...state.callTranscript,
        whisperInteraction,
        aiResponse,
      ]);
    }, 1000);
  };

  // Convert call transcript to whisper interactions format
  const whisperInteractions = state.callTranscript
    .filter(
      (entry) => entry.message.includes("[Whisper") || entry.type === "ai"
    )
    .map((entry, index) => ({
      id: `interaction-${index}`,
      type: entry.message.includes("[Whisper")
        ? ("user-to-ai" as const)
        : ("ai-to-user" as const),
      message: entry.message.replace("[Whisper to AI] ", ""),
      timestamp: entry.timestamp ?? "",
    }));

  return (
    <TooltipProvider>
      <div className="container mx-auto p-6 bg-gray-900 text-gray-100">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-teal-400">
            Whisper Management
          </h1>
          <p className="text-gray-400">
            Manage AI whisper suggestions during calls
          </p>
        </div>

        <div
          className={`grid ${
            state.activeCall ? "grid-cols-1 lg:grid-cols-4" : "grid-cols-3"
          } gap-6`}
        >
          {!state.activeCall && (
            <div className="col-span-1">
              <ContactSelector
                contacts={state.contacts}
                selectedContact={state.selectedContact}
                onSelectContact={handleSelectContact}
              />
            </div>
          )}

          <div
            className={
              state.activeCall ? "col-span-1 lg:col-span-2" : "col-span-2"
            }
          >
            <Card className="bg-gray-800 border-gray-700">
              <CallInterface
                state={state}
                onStartCall={handleStartCall}
                onEndCall={handleEndCall}
                onSendMessage={handleSendMessage}
                onVoiceInput={handleVoiceInput}
                onVolumeChange={(value) => set("volume", value)}
                onMessageChange={(value) => set("userMessage", value)}
              />
            </Card>
          </div>

          {state.activeCall && (
            <>
              {/* Whisper Controls Column */}
              <div className="col-span-1 space-y-6">
                <WhisperControls
                  state={state}
                  onSetWhisperMode={setWhisperMode}
                  onTriggerAiAgent={triggerAiAgent}
                  onGetRoomState={getRoomState}
                  onToggleMute={handleToggleMute}
                  onVolumeChange={handleVolumeChange}
                />
                <WhisperModeSelector
                  currentMode={state.whisperMode}
                  onModeChange={setWhisperMode}
                  disabled={state.loading}
                  aiAgentStatus={state.aiAgentStatus}
                />
              </div>

              {/* Whisper Interactions Column */}
              <div className="col-span-1 space-y-6">
                <WhisperInteractionPanel
                  whisperMode={state.whisperMode}
                  interactions={whisperInteractions}
                  onSendWhisper={handleSendWhisper}
                  isListening={state.speechRecognitionActive}
                  canSendWhispers={state.aiAgentStatus === "connected"}
                />
                <GoalProgress goals={state.goals} />
              </div>
            </>
          )}

          {!state.activeCall && (
            <div className="col-span-full">
              <WhisperTemplates />
            </div>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
}
