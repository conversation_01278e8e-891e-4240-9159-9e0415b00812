import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
// import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, Ta<PERSON>Content } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { assistantApi } from "@/services/api";
import EmbedCodeGenerator from "@/components/Embed/EmbedCodeGenerator";
import { Spinner } from "@/components/ui/spinner";

const EmbedPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();
  const [assistant, setAssistant] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchAssistant = async () => {
      if (!id) return;

      try {
        setLoading(true);
        const response = await assistantApi.getById(id);

        if (response.success && response.data) {
          setAssistant(response.data);
        } else {
          throw new Error(
            response.error?.message || "Failed to fetch assistant"
          );
        }
      } catch (error) {
        console.error("Error fetching assistant:", error);
        toast({
          title: "Error",
          description: "Failed to fetch assistant details",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchAssistant();
  }, [id, toast]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-200px)]">
        <Spinner className="h-12 w-12" size={48} />
      </div>
    );
  }

  if (!assistant) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-4">Assistant Not Found</h2>
              <p className="text-gray-500 mb-6">
                The assistant you're looking for doesn't exist or you don't have
                access to it.
              </p>
              <Button variant="default" onClick={() => window.history.back()}>
                Go Back
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">{assistant.name}</h1>
        <p className="text-gray-500">
          Generate embed code to add this assistant to your website
        </p>
      </div>

      <div className="grid grid-cols-1 gap-8">
        <EmbedCodeGenerator assistantId={assistant.id} />

        <Card>
          <CardHeader>
            <CardTitle>How It Works</CardTitle>
            <CardDescription>
              Learn how to integrate your assistant on any website
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">
                  1. Generate Your Embed Code
                </h3>
                <p className="text-gray-600">
                  Use the settings above to customize how your chat widget will
                  look and behave. Once you're happy with the settings, copy the
                  generated code.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">
                  2. Add the Code to Your Website
                </h3>
                <p className="text-gray-600">
                  Paste the embed code just before the closing{" "}
                  <code className="bg-gray-100 px-1 rounded">
                    &lt;/body&gt;
                  </code>{" "}
                  tag of your website. The chat widget will automatically appear
                  on your site.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">
                  3. Test Your Integration
                </h3>
                <p className="text-gray-600">
                  Visit your website and test the chat widget to make sure it's
                  working correctly. Your visitors will now be able to interact
                  with your assistant directly on your site.
                </p>
              </div>

              <div className="bg-teal-600 p-4 rounded-md">
                <h3 className="text-lg font-semibold mb-2">Important Notes</h3>
                <ul className="list-disc list-inside space-y-2 text-gray-200">
                  <li>
                    Your API key is included in the embed code. Keep it secure
                    and don't share it publicly.
                  </li>
                  <li>
                    The chat widget uses your account's API quota. Monitor your
                    usage to avoid unexpected charges.
                  </li>
                  <li>
                    You can update your assistant's settings at any time, but
                    you'll need to update the embed code on your website.
                  </li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EmbedPage;
