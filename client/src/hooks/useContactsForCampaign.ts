import { useState, useEffect, useCallback } from "react";
import { contact<PERSON><PERSON> } from "@/services/contactApi";
import { Contact } from "@/types/schema";
import { toast } from "@/components/ui/use-toast";

export function useContactsForCampaign(
  campaignId?: string,
  initialSelectedIds?: string[]
) {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [selectedContactIds, setSelectedContactIds] = useState<string[]>(
    initialSelectedIds || []
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<string | null>(null);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);

  // Fetch all contacts
  const fetchContacts = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      let response;

      if (campaignId) {
        // If campaignId is provided, fetch contacts for that campaign
        response = await contactApi.getByCampaign(campaignId);
      } else {
        // Otherwise, fetch all contacts
        response = await contactApi.getAll();
      }

      if (response.success && response.data) {
        setContacts(response.data);
      } else {
        setError(response.error?.message || "Failed to fetch contacts");
        toast({
          title: "Error",
          description: response.error?.message || "Failed to fetch contacts",
          variant: "destructive",
        });
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [campaignId]);

  // Search contacts
  const searchContacts = useCallback(async (query: string, type?: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await contactApi.search(query, type);

      if (response.success && response.data) {
        setContacts(response.data);
      } else {
        setError(response.error?.message || "Failed to search contacts");
        toast({
          title: "Error",
          description: response.error?.message || "Failed to search contacts",
          variant: "destructive",
        });
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Toggle contact selection
  const toggleContactSelection = useCallback((contactId: string) => {
    setSelectedContactIds((prev) =>
      prev.includes(contactId)
        ? prev.filter((id) => id !== contactId)
        : [...prev, contactId]
    );
  }, []);

  // Select all contacts
  const selectAllContacts = useCallback(() => {
    setSelectedContactIds(filteredContacts.map((contact) => contact.id));
  }, [filteredContacts]);

  // Clear selected contacts
  const clearSelectedContacts = useCallback(() => {
    setSelectedContactIds([]);
  }, []);

  // Filter contacts based on search query and filter type
  useEffect(() => {
    let result = [...contacts];

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        (contact) =>
          contact.name?.toLowerCase().includes(query) ||
          contact.email?.toLowerCase().includes(query) ||
          contact.phone?.toLowerCase().includes(query)
      );
    }

    // Apply type filter
    if (filterType) {
      result = result.filter(
        (contact) => contact.type?.toLowerCase() === filterType.toLowerCase()
      );
    }

    setFilteredContacts(result);
  }, [contacts, searchQuery, filterType]);

  // Load contacts on component mount
  useEffect(() => {
    fetchContacts();
  }, [fetchContacts]);

  return {
    contacts: filteredContacts,
    allContacts: contacts,
    selectedContactIds,
    isLoading,
    error,
    searchQuery,
    filterType,
    fetchContacts,
    searchContacts,
    toggleContactSelection,
    selectAllContacts,
    clearSelectedContacts,
    setSearchQuery,
    setFilterType,
  };
}
