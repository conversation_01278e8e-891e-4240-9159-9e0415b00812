import { useState, useEffect, useCallback } from "react";
import { contact<PERSON><PERSON> } from "@/services/contactApi";
import { Contact } from "@/types/schema";
import { toast } from "@/components/ui/use-toast";

export function useContacts() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch all contacts
  const fetchContacts = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await contactApi.getAll();

      if (response.success && response.data) {
        setContacts(response.data);
      } else {
        setError(response.error?.message || "Failed to fetch contacts");
        toast({
          title: "Error",
          description: response.error?.message || "Failed to fetch contacts",
          variant: "destructive",
        });
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch contacts by campaign
  const fetchContactsByCampaign = useCallback(async (campaignId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await contactApi.getByCampaign(campaignId);

      if (response.success && response.data) {
        setContacts(response.data);
      } else {
        setError(
          response.error?.message || "Failed to fetch campaign contacts"
        );
        toast({
          title: "Error",
          description:
            response.error?.message || "Failed to fetch campaign contacts",
          variant: "destructive",
        });
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Search contacts
  const searchContacts = useCallback(async (query: string, type?: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await contactApi.search(query, type);

      if (response.success && response.data) {
        setContacts(response.data);
      } else {
        setError(response.error?.message || "Failed to search contacts");
        toast({
          title: "Error",
          description: response.error?.message || "Failed to search contacts",
          variant: "destructive",
        });
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Create a new contact
  const createContact = useCallback(async (contactData: Partial<Contact>) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await contactApi.create(contactData as any);

      if (response.success && response.data) {
        setContacts((prev) => [...prev, response.data!]);
        toast({
          title: "Success",
          description: "Contact created successfully",
        });
        return response.data;
      } else {
        setError(response.error?.message || "Failed to create contact");
        toast({
          title: "Error",
          description: response.error?.message || "Failed to create contact",
          variant: "destructive",
        });
        return null;
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update a contact
  const updateContact = useCallback(
    async (id: string, contactData: Partial<Contact>) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await contactApi.update(id, contactData);

        if (response.success && response.data) {
          setContacts((prev) =>
            prev.map((contact) =>
              contact.id === id ? response.data! : contact
            )
          );
          toast({
            title: "Success",
            description: "Contact updated successfully",
          });
          return response.data;
        } else {
          setError(response.error?.message || "Failed to update contact");
          toast({
            title: "Error",
            description: response.error?.message || "Failed to update contact",
            variant: "destructive",
          });
          return null;
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "An unknown error occurred";
        setError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  // Delete a contact
  const deleteContact = useCallback(async (id: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await contactApi.delete(id);

      if (response.success) {
        setContacts((prev) => prev.filter((contact) => contact.id !== id));
        toast({
          title: "Success",
          description: "Contact deleted successfully",
        });
        return true;
      } else {
        setError(response.error?.message || "Failed to delete contact");
        toast({
          title: "Error",
          description: response.error?.message || "Failed to delete contact",
          variant: "destructive",
        });
        return false;
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load contacts on component mount
  useEffect(() => {
    fetchContacts();
  }, [fetchContacts]);

  return {
    contacts,
    isLoading,
    error,
    fetchContacts,
    fetchContactsByCampaign,
    searchContacts,
    createContact,
    updateContact,
    deleteContact,
  };
}
