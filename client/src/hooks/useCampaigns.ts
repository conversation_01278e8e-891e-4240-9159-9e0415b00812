import { useState, useEffect, useCallback } from "react";
import { campaignApi } from "@/services/campaignApi";
import {
  Campaign,
  CampaignGoal,
  CampaignTemplate,
  ChannelSettings,
} from "@/types/schema";
import { toast } from "@/components/ui/use-toast";

export function useCampaigns() {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(
    null
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [templates, setTemplates] = useState<CampaignTemplate[]>([]);
  const [campaignGoals, setCampaignGoals] = useState<CampaignGoal[]>([]);

  // Fetch all campaigns
  const fetchCampaigns = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await campaignApi.getAll();

      if (response.success && response.data) {
        setCampaigns(response.data);
      } else {
        setError(response.error?.message || "Failed to fetch campaigns");
        toast({
          title: "Error",
          description: response.error?.message || "Failed to fetch campaigns",
          variant: "destructive",
        });
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch a single campaign
  const fetchCampaign = useCallback(async (id: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await campaignApi.getById(id);

      if (response.success && response.data) {
        setSelectedCampaign(response.data);
        return response.data;
      } else {
        setError(response.error?.message || "Failed to fetch campaign");
        toast({
          title: "Error",
          description: response.error?.message || "Failed to fetch campaign",
          variant: "destructive",
        });
        return null;
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Create a new campaign
  const createCampaign = useCallback(async (campaignData: any) => {
    setIsLoading(true);
    setError(null);

    try {
      // Convert string contact IDs to the format expected by the API
      const campaign = {
        ...campaignData,
        contacts: Array.isArray(campaignData.contacts)
          ? { connect: campaignData.contacts.map((id: string) => ({ id })) }
          : undefined,
      };

      const response = await campaignApi.create(campaign);

      if (response.success && response.data) {
        setCampaigns((prev) => [...prev, response.data!]);
        toast({
          title: "Success",
          description: "Campaign created successfully",
        });
        return response.data;
      } else {
        setError(response.error?.message || "Failed to create campaign");
        toast({
          title: "Error",
          description: response.error?.message || "Failed to create campaign",
          variant: "destructive",
        });
        return null;
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update an existing campaign
  const updateCampaign = useCallback(
    async (id: string, campaignData: any) => {
      setIsLoading(true);
      setError(null);

      try {
        // Convert string contact IDs to the format expected by the API
        const campaign = {
          ...campaignData,
          contacts: Array.isArray(campaignData.contacts)
            ? { connect: campaignData.contacts.map((id: string) => ({ id })) }
            : undefined,
        };

        const response = await campaignApi.update(id, campaign);

        if (response.success && response.data) {
          setCampaigns((prev) =>
            prev.map((campaign) =>
              campaign.id === id ? response.data! : campaign
            )
          );

          if (selectedCampaign?.id === id) {
            setSelectedCampaign(response.data);
          }

          toast({
            title: "Success",
            description: "Campaign updated successfully",
          });

          return response.data;
        } else {
          setError(response.error?.message || "Failed to update campaign");
          toast({
            title: "Error",
            description: response.error?.message || "Failed to update campaign",
            variant: "destructive",
          });
          return null;
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "An unknown error occurred";
        setError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [selectedCampaign]
  );

  // Delete a campaign
  const deleteCampaign = useCallback(
    async (id: string) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await campaignApi.delete(id);

        if (response.success) {
          setCampaigns((prev) => prev.filter((campaign) => campaign.id !== id));

          if (selectedCampaign?.id === id) {
            setSelectedCampaign(null);
          }

          toast({
            title: "Success",
            description: "Campaign deleted successfully",
          });

          return true;
        } else {
          setError(response.error?.message || "Failed to delete campaign");
          toast({
            title: "Error",
            description: response.error?.message || "Failed to delete campaign",
            variant: "destructive",
          });
          return false;
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "An unknown error occurred";
        setError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [selectedCampaign]
  );

  // Load campaigns on component mount
  useEffect(() => {
    fetchCampaigns();
  }, [fetchCampaigns]);

  // Update campaign goals
  const updateCampaignGoals = useCallback(
    async (id: string, goals: CampaignGoal[]) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await campaignApi.updateGoals(id, goals);

        if (response.success && response.data) {
          setCampaignGoals(response.data);

          // Update the selected campaign if it's the one being modified
          if (selectedCampaign?.id === id) {
            setSelectedCampaign((prev) => {
              if (!prev) return null;
              return {
                ...prev,
                goals: response.data || [],
              };
            });
          }

          // Update the campaign in the list
          setCampaigns((prev) =>
            prev.map((campaign) => {
              if (campaign.id === id) {
                return {
                  ...campaign,
                  goals: response.data || [],
                };
              }
              return campaign;
            })
          );

          toast({
            title: "Success",
            description: "Campaign goals updated successfully",
          });

          return response.data;
        } else {
          setError(
            response.error?.message || "Failed to update campaign goals"
          );
          toast({
            title: "Error",
            description:
              response.error?.message || "Failed to update campaign goals",
            variant: "destructive",
          });
          return null;
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "An unknown error occurred";
        setError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [selectedCampaign]
  );

  // Add team member to campaign
  const addTeamMember = useCallback(
    async (id: string, email: string, role: string) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await campaignApi.addTeamMember(id, email, role);

        if (response.success && response.data) {
          // Update the selected campaign if it's the one being modified
          if (selectedCampaign?.id === id) {
            const updatedTeamMembers = [
              ...(selectedCampaign.teamMembers || []),
              response.data,
            ];
            setSelectedCampaign((prev) =>
              prev ? { ...prev, teamMembers: updatedTeamMembers } : null
            );
          }

          toast({
            title: "Success",
            description: "Team member added successfully",
          });

          return response.data;
        } else {
          setError(response.error?.message || "Failed to add team member");
          toast({
            title: "Error",
            description: response.error?.message || "Failed to add team member",
            variant: "destructive",
          });
          return null;
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "An unknown error occurred";
        setError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [selectedCampaign]
  );

  // Remove team member from campaign
  const removeTeamMember = useCallback(
    async (id: string, memberId: string) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await campaignApi.removeTeamMember(id, memberId);

        if (response.success) {
          // Update the selected campaign if it's the one being modified
          if (selectedCampaign?.id === id && selectedCampaign.teamMembers) {
            const updatedTeamMembers = selectedCampaign.teamMembers.filter(
              (member) => member.id !== memberId
            );
            setSelectedCampaign((prev) =>
              prev ? { ...prev, teamMembers: updatedTeamMembers } : null
            );
          }

          toast({
            title: "Success",
            description: "Team member removed successfully",
          });

          return true;
        } else {
          setError(response.error?.message || "Failed to remove team member");
          toast({
            title: "Error",
            description:
              response.error?.message || "Failed to remove team member",
            variant: "destructive",
          });
          return false;
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "An unknown error occurred";
        setError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [selectedCampaign]
  );

  // Add comment to campaign
  const addComment = useCallback(
    async (id: string, content: string, attachments?: string[]) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await campaignApi.addComment(id, content, attachments);

        if (response.success && response.data) {
          // Update the selected campaign if it's the one being modified
          if (selectedCampaign?.id === id) {
            const updatedComments = [
              ...(selectedCampaign.comments || []),
              response.data,
            ];
            setSelectedCampaign((prev) =>
              prev ? { ...prev, comments: updatedComments } : null
            );
          }

          toast({
            title: "Success",
            description: "Comment added successfully",
          });

          return response.data;
        } else {
          setError(response.error?.message || "Failed to add comment");
          toast({
            title: "Error",
            description: response.error?.message || "Failed to add comment",
            variant: "destructive",
          });
          return null;
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "An unknown error occurred";
        setError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [selectedCampaign]
  );

  // Update channel settings
  const updateChannelSettings = useCallback(
    async (id: string, settings: Partial<ChannelSettings>) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await campaignApi.updateChannelSettings(id, settings);

        if (response.success && response.data) {
          // Update the selected campaign if it's the one being modified
          if (selectedCampaign?.id === id) {
            setSelectedCampaign((prev) =>
              prev ? { ...prev, channelSettings: response.data } : null
            );
          }

          // Update the campaign in the list
          setCampaigns((prev) =>
            prev.map((campaign) =>
              campaign.id === id
                ? { ...campaign, channelSettings: response.data }
                : campaign
            )
          );

          toast({
            title: "Success",
            description: "Channel settings updated successfully",
          });

          return response.data;
        } else {
          setError(
            response.error?.message || "Failed to update channel settings"
          );
          toast({
            title: "Error",
            description:
              response.error?.message || "Failed to update channel settings",
            variant: "destructive",
          });
          return null;
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "An unknown error occurred";
        setError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [selectedCampaign]
  );

  // Get campaign templates
  const fetchTemplates = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await campaignApi.getTemplates();

      if (response.success && response.data) {
        setTemplates(response.data);
        return response.data;
      } else {
        setError(response.error?.message || "Failed to fetch templates");
        toast({
          title: "Error",
          description: response.error?.message || "Failed to fetch templates",
          variant: "destructive",
        });
        return null;
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Create campaign template
  const createTemplate = useCallback(
    async (
      template: Omit<CampaignTemplate, "id" | "createdAt" | "updatedAt">
    ) => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await campaignApi.createTemplate(template);

        if (response.success && response.data) {
          setTemplates((prev) => [...prev, response.data!]);
          toast({
            title: "Success",
            description: "Template created successfully",
          });
          return response.data;
        } else {
          setError(response.error?.message || "Failed to create template");
          toast({
            title: "Error",
            description: response.error?.message || "Failed to create template",
            variant: "destructive",
          });
          return null;
        }
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "An unknown error occurred";
        setError(errorMessage);
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  // Delete campaign template
  const deleteTemplate = useCallback(async (id: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await campaignApi.deleteTemplate(id);

      if (response.success) {
        setTemplates((prev) => prev.filter((template) => template.id !== id));
        toast({
          title: "Success",
          description: "Template deleted successfully",
        });
        return true;
      } else {
        setError(response.error?.message || "Failed to delete template");
        toast({
          title: "Error",
          description: response.error?.message || "Failed to delete template",
          variant: "destructive",
        });
        return false;
      }
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : "An unknown error occurred";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load campaigns and templates on component mount
  useEffect(() => {
    fetchCampaigns();
    fetchTemplates();
  }, [fetchCampaigns, fetchTemplates]);

  return {
    campaigns,
    selectedCampaign,
    setSelectedCampaign,
    templates,
    campaignGoals,
    setCampaignGoals,
    isLoading,
    error,
    fetchCampaigns,
    fetchCampaign,
    createCampaign,
    updateCampaign,
    deleteCampaign,
    updateCampaignGoals,
    addTeamMember,
    removeTeamMember,
    addComment,
    updateChannelSettings,
    fetchTemplates,
    createTemplate,
    deleteTemplate,
  };
}
