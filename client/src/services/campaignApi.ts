import { AxiosError } from "axios";
import { apiClient, handleApiError } from "./api";
import {
  Campaign,
  CampaignGoal,
  CampaignTemplate,
  CampaignTeamMember,
  CampaignComment,
  ChannelSettings,
} from "@/types/schema";

// Define response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: string;
  };
}

// Helper functions for handling API responses
const handleApiResponse = <T>(response: any): ApiResponse<T> => {
  return {
    success: true,
    data: response.data.data || response.data,
  };
};

// const handleApiError = (error: AxiosError): ApiResponse<any> => {
//   if (axios.isCancel(error)) {
//     return {
//       success: false,
//       error: {
//         code: "REQUEST_CANCELLED",
//         message: "Request was cancelled",
//       },
//     };
//   }

//   const errorResponse = error.response?.data as any;
//   return {
//     success: false,
//     error: {
//       code: errorResponse?.error?.code || "UNKNOWN_ERROR",
//       message: errorResponse?.error?.message || "An unknown error occurred",
//       details: errorResponse?.error?.details,
//     },
//   };
// };

// Campaign API endpoints
export const campaignApi = {
  // Get all campaigns
  getAll: async (): Promise<ApiResponse<Campaign[]>> => {
    try {
      const response = await apiClient.get("/campaigns");
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get a single campaign
  getById: async (id: string): Promise<ApiResponse<Campaign>> => {
    try {
      const response = await apiClient.get(`/campaigns/${id}`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Create a new campaign
  create: async (
    campaign: Omit<Campaign, "id" | "createdAt" | "updatedAt">
  ): Promise<ApiResponse<Campaign>> => {
    try {
      const response = await apiClient.post("/campaigns", campaign);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update a campaign
  update: async (
    id: string,
    campaign: Partial<Campaign>
  ): Promise<ApiResponse<Campaign>> => {
    try {
      const response = await apiClient.put(`/campaigns/${id}`, campaign);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Delete a campaign
  delete: async (id: string): Promise<ApiResponse<void>> => {
    try {
      const response = await apiClient.delete(`/campaigns/${id}`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update campaign goals
  updateGoals: async (
    id: string,
    goals: CampaignGoal[]
  ): Promise<ApiResponse<CampaignGoal[]>> => {
    try {
      const response = await apiClient.put(`/campaigns/${id}/goals`, { goals });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Add team member to campaign
  addTeamMember: async (
    id: string,
    email: string,
    role: string
  ): Promise<ApiResponse<CampaignTeamMember>> => {
    try {
      const response = await apiClient.post(`/campaigns/${id}/team`, {
        email,
        role,
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Remove team member from campaign
  removeTeamMember: async (
    id: string,
    memberId: string
  ): Promise<ApiResponse<void>> => {
    try {
      const response = await apiClient.delete(
        `/campaigns/${id}/team/${memberId}`
      );
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Add comment to campaign
  addComment: async (
    id: string,
    content: string,
    attachments?: string[]
  ): Promise<ApiResponse<CampaignComment>> => {
    try {
      const response = await apiClient.post(`/campaigns/${id}/comments`, {
        content,
        attachments,
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update channel settings
  updateChannelSettings: async (
    id: string,
    settings: Partial<ChannelSettings>
  ): Promise<ApiResponse<ChannelSettings>> => {
    try {
      const response = await apiClient.put(
        `/campaigns/${id}/channels`,
        settings
      );
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get campaign templates
  getTemplates: async (): Promise<ApiResponse<CampaignTemplate[]>> => {
    try {
      const response = await apiClient.get("/campaigns/templates");
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Create campaign template
  createTemplate: async (
    template: Omit<CampaignTemplate, "id" | "createdAt" | "updatedAt">
  ): Promise<ApiResponse<CampaignTemplate>> => {
    try {
      const response = await apiClient.post("/campaigns/templates", template);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Delete campaign template
  deleteTemplate: async (id: string): Promise<ApiResponse<void>> => {
    try {
      const response = await apiClient.delete(`/campaigns/templates/${id}`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};
