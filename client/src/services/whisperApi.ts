import { AxiosError } from "axios";
import {
  apiClient,
  ApiResponse,
  handleApiResponse,
  handleApiError,
} from "./api";
import { Goal, CallTranscriptEntry } from "@/components/Whisper/types";

// Whisper Template types
export interface WhisperTemplate {
  id: string;
  userId: string;
  name: string;
  type: "BUSINESS" | "PERSONAL";
  systemPrompt: string;
  editablePrompt: string;
  isSystem: boolean;
  isHidden: boolean;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

// Participant details for three-participant architecture
export interface ParticipantDetails {
  token: string;
  identity: string;
  type: "user" | "caller" | "ai_assistant";
}

// Connection details types for enhanced whisper functionality
export interface ConnectionDetails {
  serverUrl: string;
  roomName: string;
  // Legacy fields for backward compatibility
  participantToken?: string;
  participantName?: string;
  // New three-participant architecture
  userParticipant: ParticipantDetails;
  aiParticipant: ParticipantDetails;
  callerParticipant: Omit<ParticipantDetails, "token">;
  callId: string;
  whisperConfig: {
    mode: "ai-to-user" | "user-to-ai" | "normal";
    goals: any[];
  };
  agentInstructions: {
    systemPrompt: string;
    contactInfo: {
      name: string;
      type: string;
      phone?: string;
      email?: string;
    };
  };
  twilioSid?: string;
  twilioStatus?: string;
}

// Call types
export interface Call {
  id: string;
  userId: string;
  contactId: string;
  assistantId: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  status: "SCHEDULED" | "IN_PROGRESS" | "COMPLETED" | "FAILED";
  transcript: CallTranscriptEntry[];
  goals: Goal[];
  metrics: {
    averageSentiment: number;
    sentimentTimeline: { timestamp: string; score: number }[];
    whisperEffectiveness: number;
    goalCompletion: number;
  };
}

// Whisper API endpoints
export const whisperApi = {
  // Templates
  getTemplates: async (): Promise<ApiResponse<WhisperTemplate[]>> => {
    try {
      const response = await apiClient.get("/whisper/templates");
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  createTemplate: async (
    template: Omit<
      WhisperTemplate,
      "id" | "userId" | "createdAt" | "updatedAt" | "isSystem" | "isHidden"
    >
  ): Promise<ApiResponse<WhisperTemplate>> => {
    try {
      const response = await apiClient.post("/whisper/templates", template);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  updateTemplate: async (
    id: string,
    template: Partial<WhisperTemplate>
  ): Promise<ApiResponse<WhisperTemplate>> => {
    try {
      const response = await apiClient.put(
        `/whisper/templates/${id}`,
        template
      );
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  deleteTemplate: async (id: string): Promise<ApiResponse<void>> => {
    try {
      const response = await apiClient.delete(`/whisper/templates/${id}`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Call functionality
  getConnectionDetails: async (
    contactId: string
  ): Promise<ApiResponse<ConnectionDetails>> => {
    try {
      const response = await apiClient.get(
        `/whisper/connection-details?contactId=${contactId}`
      );
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  endCall: async (
    callId: string
  ): Promise<
    ApiResponse<{ callId: string; duration: number; status: string }>
  > => {
    try {
      const response = await apiClient.post(`/whisper/calls/${callId}/end`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Goals
  getContactGoals: async (contactId: string): Promise<ApiResponse<Goal[]>> => {
    try {
      const response = await apiClient.get(
        `/whisper/contacts/${contactId}/goals`
      );
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Transcript
  updateCallTranscript: async (
    callId: string,
    data: { transcript?: CallTranscriptEntry[]; goals?: Goal[] }
  ): Promise<
    ApiResponse<{
      callId: string;
      transcript: CallTranscriptEntry[];
      goals: Goal[];
    }>
  > => {
    try {
      const response = await apiClient.put(
        `/whisper/calls/${callId}/transcript`,
        data
      );
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Initiate an outbound call using Twilio
  initiateOutboundCall: async (
    contactId: string
  ): Promise<ApiResponse<ConnectionDetails>> => {
    try {
      const response = await apiClient.post("/twilio/call", { contactId });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Enhanced whisper functionality
  setWhisperMode: async (
    callId: string,
    data: {
      mode: "ai-to-user" | "user-to-ai" | "normal";
      roomName: string;
      participants: {
        userIdentity: string;
        callerIdentity: string;
        aiIdentity: string;
      };
    }
  ): Promise<
    ApiResponse<{
      callId: string;
      mode: string;
      roomName: string;
      audioRouting: any;
    }>
  > => {
    try {
      const response = await apiClient.post(
        `/whisper/calls/${callId}/mode`,
        data
      );
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  getRoomState: async (
    roomName: string
  ): Promise<
    ApiResponse<{
      roomName: string;
      participantCount: number;
      participants: any[];
    }>
  > => {
    try {
      const response = await apiClient.get(`/whisper/rooms/${roomName}/state`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  triggerAiAgent: async (data: {
    callId: string;
    roomName: string;
    agentInstructions?: any;
  }): Promise<
    ApiResponse<{
      callId: string;
      roomName: string;
      message: string;
      agentStatus: string;
    }>
  > => {
    try {
      const response = await apiClient.post("/whisper/agent/trigger", data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // SIP Configuration (admin only)
  initializeSipConfiguration: async (): Promise<
    ApiResponse<{
      message: string;
      twilioTrunk: any;
      livekitConfig: any;
    }>
  > => {
    try {
      const response = await apiClient.post("/whisper/sip/initialize");
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  getSipConfigurationStatus: async (): Promise<
    ApiResponse<{
      twilioSipTrunks: any[];
      activeWhisperRooms: number;
      whisperRooms: any[];
    }>
  > => {
    try {
      const response = await apiClient.get("/whisper/sip/status");
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};
