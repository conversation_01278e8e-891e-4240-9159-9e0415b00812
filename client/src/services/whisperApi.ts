import { AxiosError } from "axios";
import {
  apiClient,
  ApiResponse,
  handleApiResponse,
  handleApiError,
} from "./api";
import { Goal, CallTranscriptEntry } from "@/components/Whisper/types";

// Whisper Template types
export interface WhisperTemplate {
  id: string;
  userId: string;
  name: string;
  type: "BUSINESS" | "PERSONAL";
  systemPrompt: string;
  editablePrompt: string;
  isSystem: boolean;
  isHidden: boolean;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

// Connection details types
export interface ConnectionDetails {
  serverUrl: string;
  roomName: string;
  participantToken: string;
  participantName: string;
  callId: string;
  twilioSid?: string;
  twilioStatus?: string;
}

// Call types
export interface Call {
  id: string;
  userId: string;
  contactId: string;
  assistantId: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  status: "SCHEDULED" | "IN_PROGRESS" | "COMPLETED" | "FAILED";
  transcript: CallTranscriptEntry[];
  goals: Goal[];
  metrics: {
    averageSentiment: number;
    sentimentTimeline: { timestamp: string; score: number }[];
    whisperEffectiveness: number;
    goalCompletion: number;
  };
}

// Whisper API endpoints
export const whisperApi = {
  // Templates
  getTemplates: async (): Promise<ApiResponse<WhisperTemplate[]>> => {
    try {
      const response = await apiClient.get("/whisper/templates");
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  createTemplate: async (
    template: Omit<
      WhisperTemplate,
      "id" | "userId" | "createdAt" | "updatedAt" | "isSystem" | "isHidden"
    >
  ): Promise<ApiResponse<WhisperTemplate>> => {
    try {
      const response = await apiClient.post("/whisper/templates", template);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  updateTemplate: async (
    id: string,
    template: Partial<WhisperTemplate>
  ): Promise<ApiResponse<WhisperTemplate>> => {
    try {
      const response = await apiClient.put(
        `/whisper/templates/${id}`,
        template
      );
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  deleteTemplate: async (id: string): Promise<ApiResponse<void>> => {
    try {
      const response = await apiClient.delete(`/whisper/templates/${id}`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Call functionality
  getConnectionDetails: async (
    contactId: string
  ): Promise<ApiResponse<ConnectionDetails>> => {
    try {
      const response = await apiClient.get(
        `/whisper/connection-details?contactId=${contactId}`
      );
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  endCall: async (
    callId: string
  ): Promise<
    ApiResponse<{ callId: string; duration: number; status: string }>
  > => {
    try {
      const response = await apiClient.post(`/whisper/calls/${callId}/end`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Goals
  getContactGoals: async (contactId: string): Promise<ApiResponse<Goal[]>> => {
    try {
      const response = await apiClient.get(
        `/whisper/contacts/${contactId}/goals`
      );
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Transcript
  updateCallTranscript: async (
    callId: string,
    data: { transcript?: CallTranscriptEntry[]; goals?: Goal[] }
  ): Promise<
    ApiResponse<{
      callId: string;
      transcript: CallTranscriptEntry[];
      goals: Goal[];
    }>
  > => {
    try {
      const response = await apiClient.put(
        `/whisper/calls/${callId}/transcript`,
        data
      );
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Initiate an outbound call using Twilio
  initiateOutboundCall: async (
    contactId: string
  ): Promise<ApiResponse<ConnectionDetails>> => {
    try {
      const response = await apiClient.post("/twilio/call", { contactId });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};
