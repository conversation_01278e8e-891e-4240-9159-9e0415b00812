import { AxiosError } from "axios";
import { apiClient, handleApiError } from "./api";
import { Contact } from "@/types/schema";

// Define response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: string;
  };
}

// Helper functions for handling API responses
const handleApiResponse = <T>(response: any): ApiResponse<T> => {
  return {
    success: true,
    data: response.data,
  };
};

// const handleApiError = (error: AxiosError): ApiResponse<any> => {
//   if (axios.isCancel(error)) {
//     return {
//       success: false,
//       error: {
//         code: "REQUEST_CANCELLED",
//         message: "Request was cancelled",
//       },
//     };
//   }

//   const errorResponse = error.response?.data as any;
//   return {
//     success: false,
//     error: {
//       code: errorResponse?.error?.code || "UNKNOWN_ERROR",
//       message: errorResponse?.error?.message || "An unknown error occurred",
//       details: errorResponse?.error?.details,
//     },
//   };
// };

// Contact API endpoints
export const contactApi = {
  // Get all contacts
  getAll: async (): Promise<ApiResponse<Contact[]>> => {
    try {
      const response = await apiClient.get("/contacts");
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Search contacts
  search: async (
    query: string,
    type?: string,
    subcategory?: string
  ): Promise<ApiResponse<Contact[]>> => {
    try {
      let url = `/contacts/search?query=${encodeURIComponent(query)}`;
      if (type) url += `&type=${encodeURIComponent(type)}`;
      if (subcategory) url += `&subcategory=${encodeURIComponent(subcategory)}`;

      const response = await apiClient.get(url);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get a single contact
  getById: async (id: string): Promise<ApiResponse<Contact>> => {
    try {
      const response = await apiClient.get(`/contacts/${id}`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Create a new contact
  create: async (
    contact: Omit<Contact, "id" | "createdAt" | "updatedAt">
  ): Promise<ApiResponse<Contact>> => {
    try {
      const response = await apiClient.post("/contacts", contact);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update a contact
  update: async (
    id: string,
    contact: Partial<Contact>
  ): Promise<ApiResponse<Contact>> => {
    try {
      const response = await apiClient.put(`/contacts/${id}`, contact);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Delete a contact
  delete: async (id: string): Promise<ApiResponse<void>> => {
    try {
      const response = await apiClient.delete(`/contacts/${id}`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Bulk delete contacts
  bulkDelete: async (ids: string[]): Promise<ApiResponse<void>> => {
    try {
      const response = await apiClient.post("/contacts/bulk-delete", {
        contactIds: ids,
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Merge contacts
  merge: async (
    primaryId: string,
    secondaryIds: string[]
  ): Promise<ApiResponse<Contact>> => {
    try {
      const response = await apiClient.post("/contacts/merge", {
        primaryContactId: primaryId,
        secondaryContactIds: secondaryIds,
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get contacts by campaign
  getByCampaign: async (
    campaignId: string
  ): Promise<ApiResponse<Contact[]>> => {
    try {
      const response = await apiClient.get(
        `/contacts?campaignId=${campaignId}`
      );
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};
