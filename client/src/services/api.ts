import axios, {
  AxiosError,
  AxiosInstance,
  AxiosResponse,
  InternalAxiosRequestConfig,
  isAxiosError,
  CanceledError,
} from "axios";
import { AssistantTool } from "@/types/schema";
import { toast } from "@/components/ui/use-toast";

export const API_BASE_URL =
  import.meta.env.VITE_API_URL || "http://localhost:3030/api/v1";

// Create a custom Axios instance with JWT handling
const createApiClient = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000,
    headers: {
      "Content-Type": "application/json",
    },
    withCredentials: true, // Important for refresh token cookie
  });
  // Request interceptor to add auth token
  instance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      const token = localStorage.getItem("token");

      if (token) {
        config.headers = config.headers || {};
        config.headers["Authorization"] = `Bearer ${token}`;
      }

      return config;
    },
    (error: AxiosError) => {
      return Promise.reject(error);
    }
  );
  // First response interceptor removed to avoid conflicts with the token refresh interceptor
  // Response interceptor to handle errors and token refresh
  instance.interceptors.response.use(
    (response: AxiosResponse) => response,
    async (error: unknown) => {
      // Check if it's an Axios error
      if (isAxiosError(error)) {
        // Handle token refresh for 401 errors
        if (error.response && error.config) {
          const originalRequest = error.config as InternalAxiosRequestConfig & {
            _retry?: boolean;
          };

          // Handle 401 Unauthorized (token expired) or specific TOKEN_EXPIRED code
          const isTokenExpired =
            error.response.status === 401 &&
            (error.response.data?.code === "TOKEN_EXPIRED" ||
              !originalRequest._retry);

          if (isTokenExpired && !originalRequest._retry) {
            console.log("Token expired, attempting to refresh...");
            originalRequest._retry = true;

            try {
              // Attempt to refresh token
              const refreshResponse = await axios.post(
                `${API_BASE_URL}/auth/refresh-token`,
                {},
                {
                  withCredentials: true,
                }
              );

              console.log("Token refresh successful");
              const newAccessToken = refreshResponse.data.accessToken;
              localStorage.setItem("token", newAccessToken);

              // Update the original request header
              originalRequest.headers = originalRequest.headers || {};
              originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;

              // Retry the original request
              return instance(originalRequest);
            } catch (refreshError) {
              console.error("Token refresh failed:", refreshError);
              // Refresh token failed - logout user
              localStorage.removeItem("token");
              localStorage.removeItem("user");

              toast({
                title: "Session Expired",
                description: "Please login again",
                variant: "destructive",
              });

              // Delay redirect to allow toast to be seen
              setTimeout(() => {
                window.location.href = "/login";
              }, 1500);

              return Promise.reject(refreshError);
            }
          }

          // Handle other errors with response
          const errorData = error.response.data as
            | { message?: string }
            | undefined;
          const errorMessage =
            errorData?.message || error.message || "An error occurred";

          // Don't show toast for cancelled requests or 401 errors (handled above)
          if (
            !(error instanceof CanceledError) &&
            error.response.status !== 401
          ) {
            toast({
              title: "Error",
              description: errorMessage,
              variant: "destructive",
            });
          }
        } else {
          // Handle Axios errors without response
          if (!(error instanceof CanceledError)) {
            toast({
              title: "Error",
              description: error.message || "Network error occurred",
              variant: "destructive",
            });
          }
        }
      } else if (error instanceof Error) {
        // Handle non-Axios errors
        toast({
          title: "Error",
          description: error.message || "An unexpected error occurred",
          variant: "destructive",
        });
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// Create the API client instance
export const apiClient = createApiClient();

// Assistant types (unchanged)
export interface Assistant {
  id: string;
  userId: string;
  name: string;
  firstMessage: string;
  systemPrompt: string;
  provider: string;
  model: string;
  tools: AssistantTool[];
  voice: {
    provider: string;
    voiceId: string;
    settings: {
      speed: number;
      pitch: number;
      stability: number;
      volume: number;
    };
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  modes: ("web" | "voice")[];
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// Generic API response handler (unchanged)
export const handleApiResponse = <T>(
  response: AxiosResponse<ApiResponse<T>>
): ApiResponse<T> => {
  return response.data;
};

// Generic API error handler (updated to handle refresh token flow)
export const handleApiError = (error: AxiosError): ApiResponse<never> => {
  if (error.response) {
    // If we get a 401 after retry, it means refresh token failed
    if (error.response.status === 401 && (error.config as any)?._retry) {
      return {
        success: false,
        error: {
          code: "SESSION_EXPIRED",
          message: "Your session has expired. Please login again.",
        },
      };
    }
    return error.response.data as ApiResponse<never>;
  }
  return {
    success: false,
    error: {
      code: "NETWORK_ERROR",
      message: error.message || "Network error occurred",
    },
  };
};

// Assistant API endpoints (unchanged)
export const assistantApi = {
  getAll: async (): Promise<ApiResponse<Assistant[]>> => {
    try {
      const response = await apiClient.get("/assistants");
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  getById: async (id: string): Promise<ApiResponse<Assistant>> => {
    try {
      const response = await apiClient.get(`/assistants/${id}`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  create: async (
    assistant: Omit<Assistant, "id" | "createdAt" | "updatedAt">
  ): Promise<ApiResponse<Assistant>> => {
    try {
      const response = await apiClient.post("/assistants", assistant);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  update: async (
    id: string,
    assistant: Partial<Assistant>
  ): Promise<ApiResponse<Assistant>> => {
    try {
      const response = await apiClient.put(`/assistants/${id}`, assistant);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  delete: async (id: string): Promise<ApiResponse<void>> => {
    try {
      const response = await apiClient.delete(`/assistants/${id}`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};

// Export the raw apiClient for other API modules to use
export const getApiClient = () => apiClient;

// import axios, {
//   AxiosError,
//   AxiosInstance,
//   AxiosResponse,
//   InternalAxiosRequestConfig,
// } from "axios";
// import { AssistantTool } from "@/types/schema";
// import { toast } from "@/components/ui/use-toast";

// export const API_BASE_URL =
//   import.meta.env.VITE_API_URL || "http://localhost:3030/api/v1";

// // Create a custom Axios instance with JWT handling
// const createApiClient = (): AxiosInstance => {
//   const instance = axios.create({
//     baseURL: API_BASE_URL,
//     timeout: 10000,
//     headers: {
//       "Content-Type": "application/json",
//     },
//   });

//   // Request interceptor to add auth token
//   instance.interceptors.request.use(
//     (config: InternalAxiosRequestConfig) => {
//       const token = localStorage.getItem("token");

//       if (token) {
//         config.headers = config.headers || {}; // Ensure headers exist
//         config.headers["Authorization"] = `Bearer ${token}`;
//       }

//       return config;
//     },
//     (error: AxiosError) => {
//       return Promise.reject(error);
//     }
//   );

//   // Response interceptor to handle errors
//   instance.interceptors.response.use(
//     (response: AxiosResponse) => response,
//     (error: AxiosError | Error | unknown) => {
//       if (axios.isAxiosError(error)) {
//         if (error.response?.status === 401) {
//           // Handle unauthorized (token expired or invalid)
//           toast({
//             title: "Session Expired",
//             description: "Please login again",
//             variant: "destructive",
//           });
//           localStorage.removeItem("token");
//           window.location.href = "/login";
//         }
//         const errorMessage =
//           error.response?.data?.message || error.message || "An error occurred";

//         toast({
//           title: "Error",
//           description: errorMessage,
//           variant: "destructive",
//         });
//       }
//       return Promise.reject(error);
//     }
//   );

//   return instance;
// };

// // Create the API client instance
// export const apiClient = createApiClient();

// // Assistant types
// export interface Assistant {
//   id: string;
//   userId: string;
//   name: string;
//   firstMessage: string;
//   systemPrompt: string;
//   provider: string;
//   model: string;
//   tools: AssistantTool[];
//   voice: {
//     provider: string;
//     voiceId: string;
//     settings: {
//       speed: number;
//       pitch: number;
//       stability: number;
//       volume: number;
//     };
//   };
//   isActive: boolean;
//   createdAt: Date;
//   updatedAt: Date;
//   modes: ("web" | "voice")[];
// }

// export interface ApiResponse<T> {
//   success: boolean;
//   data?: T;
//   error?: {
//     code: string;
//     message: string;
//     details?: any;
//   };
// }

// // Generic API response handler
// const handleApiResponse = <T>(
//   response: AxiosResponse<ApiResponse<T>>
// ): ApiResponse<T> => {
//   return response.data;
// };

// // Generic API error handler
// const handleApiError = (error: AxiosError): ApiResponse<never> => {
//   if (error.response) {
//     return error.response.data as ApiResponse<never>;
//   }
//   return {
//     success: false,
//     error: {
//       code: "NETWORK_ERROR",
//       message: error.message || "Network error occurred",
//     },
//   };
// };

// // Assistant API endpoints
// export const assistantApi = {
//   // Get all assistants
//   getAll: async (): Promise<ApiResponse<Assistant[]>> => {
//     try {
//       const response = await apiClient.get("/assistants");
//       return handleApiResponse(response);
//     } catch (error) {
//       return handleApiError(error as AxiosError);
//     }
//   },

//   // Get single assistant
//   getById: async (id: string): Promise<ApiResponse<Assistant>> => {
//     try {
//       const response = await apiClient.get(`/assistants/${id}`);
//       return handleApiResponse(response);
//     } catch (error) {
//       return handleApiError(error as AxiosError);
//     }
//   },

//   // Create assistant
//   create: async (
//     assistant: Omit<Assistant, "id" | "createdAt" | "updatedAt">
//   ): Promise<ApiResponse<Assistant>> => {
//     try {
//       const response = await apiClient.post("/assistants", assistant);
//       return handleApiResponse(response);
//     } catch (error) {
//       return handleApiError(error as AxiosError);
//     }
//   },

//   // Update assistant
//   update: async (
//     id: string,
//     assistant: Partial<Assistant>
//   ): Promise<ApiResponse<Assistant>> => {
//     try {
//       const response = await apiClient.put(`/assistants/${id}`, assistant);
//       return handleApiResponse(response);
//     } catch (error) {
//       return handleApiError(error as AxiosError);
//     }
//   },

//   // Delete assistant
//   delete: async (id: string): Promise<ApiResponse<void>> => {
//     try {
//       const response = await apiClient.delete(`/assistants/${id}`);
//       return handleApiResponse(response);
//     } catch (error) {
//       return handleApiError(error as AxiosError);
//     }
//   },
// };

// // Export the raw apiClient for other API modules to use
// export const getApiClient = () => apiClient;
