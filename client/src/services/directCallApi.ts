import { AxiosError } from "axios";
import {
  apiClient,
  ApiResponse,
  handleApiResponse,
  handleApiError,
} from "./api";

// Add to your interfaces
export interface LiveKitInfo {
  url: string;
  room: string;
  token: string;
}

// Direct call types
export interface DirectCallDetails {
  callId: string;
  twilioSid: string;
  twilioStatus: string;
  contactName: string;
  contactPhone: string;
  liveKit?: LiveKitInfo;
}

export interface ClientToken {
  token: string;
  identity: string;
}

// Direct call API endpoints
export const directCallApi = {
  // Start a direct call
  initiateCall: async (
    contactId: string,
    callSid?: any
  ): Promise<ApiResponse<DirectCallDetails>> => {
    try {
      const response = await apiClient.post("/direct-call", {
        contactId,
        callSid, // Optional parameter for client-initiated calls
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
  // Add this new method to directCallApi
  getLiveKitInfo: async (
    callSid: string
  ): Promise<ApiResponse<{ liveKit: LiveKitInfo }>> => {
    try {
      const response = await apiClient.get(
        `/livekit/whisper-connection-details?${callSid}`
      );
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
  // End a direct call
  endCall: async (): Promise<
    ApiResponse<{ callId: string; duration: number; status: string }>
  > => {
    try {
      const response = await apiClient.post("/direct-call/end");
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get a Twilio client token
  getClientToken: async (): Promise<ApiResponse<ClientToken>> => {
    try {
      const response = await apiClient.get("/direct-call/token");
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};
