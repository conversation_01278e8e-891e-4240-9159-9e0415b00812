import { useState, useEffect, useCallback } from "react";
import {
  AuthUser,
  LoginCredentials,
  RegisterData,
  AuthResponse,
} from "../types";
import { API_BASE_URL, apiClient } from "@/services/api";
import axios from "axios";
export function useAuth() {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const token = localStorage.getItem("token");
    if (token) {
      fetchUser(token);
    } else {
      setLoading(false);
    }
  }, []);

  const fetchUser = async (token: string) => {
    try {
      const response = await apiClient.get(`/auth/me`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      setUser(response.data.user);
    } catch (err) {
      localStorage.removeItem("token");
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials: LoginCredentials) => {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/auth/login`,
        credentials,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      // Ensure these state updates complete before proceeding
      await Promise.all([
        new Promise((resolve) => {
          setUser(response.data.user);
          resolve(null);
        }),
        new Promise((resolve) => {
          localStorage.setItem("user", JSON.stringify(response.data.user));
          localStorage.setItem("token", response.data.accessToken);
          resolve(null);
        }),
      ]);

      return response.data;
    } catch (err) {
      let errorMessage = "Failed to login";
      if (axios.isAxiosError(err)) {
        errorMessage =
          err.response?.data?.message || err.message || errorMessage;
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      setError(errorMessage);
      throw new Error(errorMessage); // Re-throw for handleSubmit to catch
    }
  };

  const register = async (data: RegisterData) => {
    try {
      setError(null);
      const response = await axios.post(`${API_BASE_URL}/auth/register`, data, {
        headers: {
          "Content-Type": "application/json",
        },
      });

      const responseData: AuthResponse = response.data;
      return responseData;
    } catch (err) {
      let errorMessage = "Registration failed";
      if (axios.isAxiosError(err)) {
        errorMessage =
          err.response?.data?.message || err.message || errorMessage;
      } else if (err instanceof Error) {
        errorMessage = err.message;
      }
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };
  const logout = useCallback(() => {
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    setUser(null);
  }, []);

  const updateUser = useCallback((updates: Partial<AuthUser>) => {
    setUser((prev) => (prev ? { ...prev, ...updates } : null));
  }, []);

  return {
    user,
    loading,
    error,
    login,
    register,
    logout,
    updateUser,
    setUser,
    setError,
  };
}
