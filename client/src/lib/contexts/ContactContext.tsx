import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { Contact } from "@/types/contact";
import { toast } from "@/components/ui/use-toast";
import { apiClient } from "@/services/api";

interface ContactContextType {
  contacts: Contact[];
  selectedContacts: string[];
  filteredContacts: Contact[];
  isLoading: boolean;
  error: string | null;
  showAddContactModal: boolean;
  editingContact: Contact | null;
  searchQuery: string;
  filterType: string | null;
  filterSubcategory: string | null;
  setShowAddContactModal: (show: boolean) => void;
  setEditingContact: (contact: Contact | null) => void;
  fetchContacts: () => Promise<void>;
  createContact: (contact: Omit<Contact, "id">) => Promise<Contact | null>;
  updateContact: (
    id: string,
    contact: Partial<Contact>
  ) => Promise<Contact | null>;
  deleteContact: (id: string) => Promise<boolean>;
  bulkDeleteContacts: (ids: string[]) => Promise<boolean>;
  mergeContacts: (
    primaryId: string,
    secondaryIds: string[]
  ) => Promise<Contact | null>;
  toggleSelectContact: (id: string) => void;
  selectAllContacts: () => void;
  clearSelectedContacts: () => void;
  setSearchQuery: (query: string) => void;
  setFilterType: (type: string | null) => void;
  setFilterSubcategory: (subcategory: string | null) => void;
}

const ContactContext = createContext<ContactContextType | undefined>(undefined);

export function ContactProvider({ children }: { children: React.ReactNode }) {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [selectedContacts, setSelectedContacts] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAddContactModal, setShowAddContactModal] = useState(false);
  const [editingContact, setEditingContact] = useState<Contact | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<string | null>(null);
  const [filterSubcategory, setFilterSubcategory] = useState<string | null>(
    null
  );

  // Fetch all contacts
  const fetchContacts = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiClient.get("/contacts");
      setContacts(response.data);
    } catch (err) {
      console.error("Error fetching contacts:", err);
      setError("Failed to fetch contacts");
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to fetch contacts. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Create a new contact
  const createContact = async (contactData: Omit<Contact, "id">) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiClient.post("/contacts", contactData);
      setContacts((prev) => [response.data, ...prev]);
      toast({
        title: "Success",
        description: "Contact created successfully",
      });
      return response.data;
    } catch (err) {
      console.error("Error creating contact:", err);
      setError("Failed to create contact");
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create contact. Please try again.",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Update an existing contact
  const updateContact = async (id: string, contactData: Partial<Contact>) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiClient.put(`/contacts/${id}`, contactData);
      setContacts((prev) =>
        prev.map((contact) => (contact.id === id ? response.data : contact))
      );
      toast({
        title: "Success",
        description: "Contact updated successfully",
      });
      return response.data;
    } catch (err) {
      console.error("Error updating contact:", err);
      setError("Failed to update contact");
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update contact. Please try again.",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Delete a contact
  const deleteContact = async (id: string) => {
    setIsLoading(true);
    setError(null);
    try {
      await apiClient.delete(`/contacts/${id}`);
      setContacts((prev) => prev.filter((contact) => contact.id !== id));
      setSelectedContacts((prev) =>
        prev.filter((contactId) => contactId !== id)
      );
      toast({
        title: "Success",
        description: "Contact deleted successfully",
      });
      return true;
    } catch (err) {
      console.error("Error deleting contact:", err);
      setError("Failed to delete contact");
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete contact. Please try again.",
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Bulk delete contacts
  const bulkDeleteContacts = async (ids: string[]) => {
    if (ids.length === 0) return false;

    setIsLoading(true);
    setError(null);
    try {
      await apiClient.post("/contacts/bulk-delete", { contactIds: ids });
      setContacts((prev) =>
        prev.filter((contact) => !ids.includes(contact.id))
      );
      setSelectedContacts([]);
      toast({
        title: "Success",
        description: `${ids.length} contacts deleted successfully`,
      });
      return true;
    } catch (err) {
      console.error("Error bulk deleting contacts:", err);
      setError("Failed to delete contacts");
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete contacts. Please try again.",
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Merge contacts
  const mergeContacts = async (primaryId: string, secondaryIds: string[]) => {
    if (secondaryIds.length === 0) return null;

    setIsLoading(true);
    setError(null);
    try {
      const response = await apiClient.post("/contacts/merge", {
        primaryContactId: primaryId,
        secondaryContactIds: secondaryIds,
      });

      // Update contacts list
      setContacts((prev) => {
        const filtered = prev.filter(
          (contact) =>
            !secondaryIds.includes(contact.id) && contact.id !== primaryId
        );
        return [response.data.contact, ...filtered];
      });

      // Clear selected contacts
      setSelectedContacts([]);

      toast({
        title: "Success",
        description: "Contacts merged successfully",
      });

      return response.data.contact;
    } catch (err) {
      console.error("Error merging contacts:", err);
      setError("Failed to merge contacts");
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to merge contacts. Please try again.",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Toggle select contact
  const toggleSelectContact = (id: string) => {
    setSelectedContacts((prev) =>
      prev.includes(id)
        ? prev.filter((contactId) => contactId !== id)
        : [...prev, id]
    );
  };

  // Select all contacts
  const selectAllContacts = () => {
    if (selectedContacts.length === filteredContacts.length) {
      setSelectedContacts([]);
    } else {
      setSelectedContacts(filteredContacts.map((contact) => contact.id));
    }
  };

  // Clear selected contacts
  const clearSelectedContacts = () => {
    setSelectedContacts([]);
  };

  // Filter contacts based on search query and filters
  useEffect(() => {
    let result = [...contacts];

    // Apply search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        (contact) =>
          contact.name.toLowerCase().includes(query) ||
          contact.email.toLowerCase().includes(query) ||
          contact.phone.toLowerCase().includes(query)
      );
    }

    // Apply type filter
    if (filterType) {
      result = result.filter(
        (contact) => contact.type.toLowerCase() === filterType.toLowerCase()
      );
    }

    // Apply subcategory filter
    if (filterSubcategory) {
      result = result.filter(
        (contact) => contact.subcategory === filterSubcategory
      );
    }

    setFilteredContacts(result);
  }, [contacts, searchQuery, filterType, filterSubcategory]);

  // Fetch contacts on initial load
  useEffect(() => {
    fetchContacts();
  }, [fetchContacts]);

  return (
    <ContactContext.Provider
      value={{
        contacts,
        selectedContacts,
        filteredContacts,
        isLoading,
        error,
        showAddContactModal,
        editingContact,
        searchQuery,
        filterType,
        filterSubcategory,
        setShowAddContactModal,
        setEditingContact,
        fetchContacts,
        createContact,
        updateContact,
        deleteContact,
        bulkDeleteContacts,
        mergeContacts,
        toggleSelectContact,
        selectAllContacts,
        clearSelectedContacts,
        setSearchQuery,
        setFilterType,
        setFilterSubcategory,
      }}
    >
      {children}
    </ContactContext.Provider>
  );
}

export function useContactContext() {
  const context = useContext(ContactContext);
  if (context === undefined) {
    throw new Error("useContactContext must be used within a ContactProvider");
  }
  return context;
}
