import React, { useEffect, useState } from "react";
import { Calendar, Globe, MessageSquare } from "lucide-react";
import { apiClient } from "@/services/api";
import { User } from "@/types/schema";
import { useToast } from "@/components/ui/use-toast";

// Encryption function using Web Crypto API
// async function encryptData(data: string) {
//   const encoder = new TextEncoder();
//   const keyMaterial = await crypto.subtle.importKey(
//     "raw",
//     encoder.encode(
//       import.meta.env.VITE_ENCRYPTION_KEY || "default_32_byte_key"
//     ),
//     { name: "AES-GCM" },
//     false,
//     ["encrypt"]
//   );

//   const iv = crypto.getRandomValues(new Uint8Array(12));
//   const encryptedData = await crypto.subtle.encrypt(
//     { name: "AES-GCM", iv },
//     keyMaterial,
//     encoder.encode(data)
//   );

//   return {
//     iv: Array.from(iv),
//     encrypted: Array.from(new Uint8Array(encryptedData)),
//   };
// }

interface Tool {
  id: string;
  name: string;
  icon: React.ElementType;
  // configFields: string[];
  isSensitive?: boolean;
  type: "calendar" | "scraping" | "sms" | "email";
  config: Record<string, any>;
  isEnabled: boolean;
}

interface ToolConfig {
  [key: string]: {
    [key: string]: string;
  };
}

interface ConfigureToolsProps {
  onNext: (
    tools: Array<{
      id: string;
      type: string;
      name: string;
      isEnabled: boolean;
      // config: Record<string, string | number | boolean>;
      config: Record<string, any>;
    }>
  ) => void;
  onBack: () => void;
}

export default function ConfigureTools({
  onNext,
  onBack,
}: ConfigureToolsProps) {
  const { toast } = useToast();
  const [selectedTools, setSelectedTools] = React.useState<
    ("calendar" | "scraping" | "sms" | "email")[]
  >([]);
  const [user, setUser] = useState<User | null>(null);
  const [toolConfigs, setToolConfigs] = React.useState<ToolConfig>({
    calendar: { provider: "Cal.com", apiKey: "Add API Key from Account Tab" },
    scraping: { url: "" },
    sms: { fromNumber: "" },
  });

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const response = await apiClient.get(`/user/profile`);
        console.log("response", response);
        setUser(response?.data);
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to fetch user data",
          variant: "destructive",
        });
      }
    };

    fetchUserData();
  }, []);

  const tools: Tool[] = [
    {
      id: crypto.randomUUID(),
      type: "calendar",
      name: "Calendar Integration",
      icon: Calendar,
      // configFields: ["provider", "apiKey"],
      // isSensitive: false,
      isEnabled: true,
      config: { provider: "Cal.com" },
    },
    {
      id: crypto.randomUUID(),
      type: "scraping",
      name: "Scraping Tool",
      icon: Globe,
      // configFields: ["url"],
      config: { url: "" },
      isEnabled: true,
    },
    {
      id: crypto.randomUUID(),
      type: "sms",
      name: "Send SMS",
      icon: MessageSquare,
      // configFields: ["fromNumber"],
      isEnabled: true,
      config: { fromNumber: "" },
    },
  ];

  const toggleTool = (toolType: "calendar" | "scraping" | "sms" | "email") => {
    console.log("toolType", toolType);
    setSelectedTools((prev) =>
      prev.includes(toolType)
        ? prev.filter((type) => type !== toolType)
        : [...prev, toolType]
    );
  };

  const handleNext = async () => {
    const configuredTools = (
      await Promise.all(
        selectedTools.map(async (toolType) => {
          const tool = tools.find((t) => t.type === toolType);
          if (!tool) return null;

          let config: Record<string, string | number | boolean> = {
            ...toolConfigs[tool.type],
          };

          return {
            id: tool.id,
            type: tool.type,
            name: tool.name,
            config,
            isEnabled: tool.isEnabled,
          };
        })
      )
    ).filter(
      (
        tool
      ): tool is {
        id: string;
        type: "calendar" | "scraping" | "sms" | "email";
        name: string;
        isEnabled: boolean;
        config: Record<string, any>;
      } => tool !== null
    );

    onNext(configuredTools);
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-3 gap-6">
        {tools.map((tool) => {
          const Icon = tool.icon;

          return (
            <div
              key={tool.id}
              className={`p-6 rounded-lg cursor-pointer border-2 transition-colors ${
                selectedTools.includes(tool.type)
                  ? "border-teal-600 bg-gray-700"
                  : "border-transparent bg-gray-700 hover:border-gray-600"
              }`}
              onClick={() => toggleTool(tool.type)}
            >
              <div className="flex items-center space-x-3">
                <Icon className="text-teal-400" size={24} />

                <span className="text-white font-medium">{tool.name}</span>
              </div>
            </div>
          );
        })}
      </div>

      {selectedTools.map((toolType) => {
        const tool = tools.find((t) => t.type === toolType);
        console.log("tool", tool);
        if (!tool) return null;
        return (
          <div key={toolType} className="mt-6 p-6 bg-gray-700 rounded-lg">
            <h3 className="text-white font-medium mb-4">
              {tool.name} Configuration
            </h3>
            {Object.entries(tool.config).map(([field]) => (
              <div key={field} className="mb-4">
                <label className="block text-gray-400 mb-2 capitalize">
                  {tool.type !== "calendar" &&
                    field.replace(/([A-Z])/g, " $1").trim()}
                </label>
                {tool.type === "calendar" && (
                  <p className="text-gray-400 mb-2">
                    {user?.calApiKey
                      ? "API Key is automatically fetched from your account settings. If you need to change it, go to the account tab and update it there."
                      : "Please add your Cal.com API key in the account tab to enable this feature."}
                  </p>
                )}
                {tool.type !== "calendar" && (
                  <input
                    type={
                      tool.isSensitive && field === "apiKey"
                        ? "password"
                        : "text"
                    }
                    className="w-full px-4 py-2 bg-gray-800 text-white rounded-lg border border-gray-600"
                    value={toolConfigs[tool.type]?.[field] || ""}
                    onChange={(e) =>
                      setToolConfigs((prev) => ({
                        ...prev,
                        [tool.type]: {
                          ...prev[tool.type],
                          [field]: e.target.value,
                        },
                      }))
                    }
                    readOnly={false}
                  />
                )}
              </div>
            ))}
          </div>
        );
      })}

      <div className="flex justify-between mt-8">
        <button
          onClick={onBack}
          className="px-4 py-2 text-gray-400 hover:text-white"
        >
          Back
        </button>
        <button
          onClick={handleNext}
          className="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700"
        >
          Next
        </button>
      </div>
    </div>
  );
}
