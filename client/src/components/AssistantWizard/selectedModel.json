[{"id": "openai/gpt-4.5-preview", "name": "OpenAI: GPT-4.5 (Preview)", "created": 1740687810, "description": "GPT-4.5 (Preview) is a research preview of OpenAI’s latest language model, designed to advance capabilities in reasoning, creativity, and multi-turn conversation. It builds on previous iterations with improvements in world knowledge, contextual coherence, and the ability to follow user intent more effectively.\n\nThe model demonstrates enhanced performance in tasks that require open-ended thinking, problem-solving, and communication. Early testing suggests it is better at generating nuanced responses, maintaining long-context coherence, and reducing hallucinations compared to earlier versions.\n\nThis research preview is intended to help evaluate GPT-4.5’s strengths and limitations in real-world use cases as OpenAI continues to refine and develop future models. Read more at the [blog post here.](https://openai.com/index/introducing-gpt-4-5/)", "context_length": 128000, "architecture": {"modality": "text+image->text", "tokenizer": "GPT", "instruct_type": null}, "pricing": {"prompt": "0.000075", "completion": "0.00015", "image": "0.108375", "request": "0", "input_cache_read": "0", "input_cache_write": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 128000, "max_completion_tokens": 16384, "is_moderated": true}, "per_request_limits": null}, {"id": "google/gemini-2.5-pro-exp-03-25:free", "name": "Google: Gemini Pro 2.5 Experimental (free)", "created": **********, "description": "Gemini 2.5 Pro is Google’s state-of-the-art AI model designed for advanced reasoning, coding, mathematics, and scientific tasks. It employs “thinking” capabilities, enabling it to reason through responses with enhanced accuracy and nuanced context handling. Gemini 2.5 Pro achieves top-tier performance on multiple benchmarks, including first-place positioning on the LMArena leaderboard, reflecting superior human-preference alignment and complex problem-solving abilities.", "context_length": 1000000, "architecture": {"modality": "text+image->text", "tokenizer": "Gemini", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0", "input_cache_read": "0", "input_cache_write": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 1000000, "max_completion_tokens": 65536, "is_moderated": false}, "per_request_limits": null}, {"id": "deepseek/deepseek-chat-v3-0324:free", "name": "DeepSeek: DeepSeek V3 0324 (free)", "created": **********, "description": "DeepSeek V3, a 685B-parameter, mixture-of-experts model, is the latest iteration of the flagship chat model family from the DeepSeek team.\n\nIt succeeds the [DeepSeek V3](/deepseek/deepseek-chat-v3) model and performs really well on a variety of tasks.", "context_length": 131072, "architecture": {"modality": "text->text", "tokenizer": "DeepSeek", "instruct_type": null}, "pricing": {"prompt": "0", "completion": "0", "image": "0", "request": "0", "input_cache_read": "0", "input_cache_write": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "deepseek/deepseek-chat-v3-0324", "name": "DeepSeek: DeepSeek V3 0324", "created": **********, "description": "DeepSeek V3, a 685B-parameter, mixture-of-experts model, is the latest iteration of the flagship chat model family from the DeepSeek team.\n\nIt succeeds the [DeepSeek V3](/deepseek/deepseek-chat-v3) model and performs really well on a variety of tasks.", "context_length": 64000, "architecture": {"modality": "text->text", "tokenizer": "DeepSeek", "instruct_type": null}, "pricing": {"prompt": "0.00000027", "completion": "0.0000011", "image": "0", "request": "0", "input_cache_read": "0", "input_cache_write": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 64000, "max_completion_tokens": 8192, "is_moderated": false}, "per_request_limits": null}, {"id": "deepseek/deepseek-r1", "name": "DeepSeek: R1", "created": **********, "description": "DeepSeek R1 is here: Performance on par with [OpenAI o1](/openai/o1), but open-sourced and with fully open reasoning tokens. It's 671B parameters in size, with 37B active in an inference pass.\n\nFully open-source model & [technical report](https://api-docs.deepseek.com/news/news250120).\n\nMIT licensed: Distill & commercialize freely!", "context_length": 163840, "architecture": {"modality": "text->text", "tokenizer": "DeepSeek", "instruct_type": "deepseek-r1"}, "pricing": {"prompt": "0.00000055", "completion": "0.00000219", "image": "0", "request": "0", "input_cache_read": "0", "input_cache_write": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 163840, "max_completion_tokens": 163840, "is_moderated": false}, "per_request_limits": null}, {"id": "x-ai/grok-2-vision-1212", "name": "xAI: Grok 2 Vision 1212", "created": **********, "description": "Grok 2 Vision 1212 advances image-based AI with stronger visual comprehension, refined instruction-following, and multilingual support. From object recognition to style analysis, it empowers developers to build more intuitive, visually aware applications. Its enhanced steerability and reasoning establish a robust foundation for next-generation image solutions.\n\nTo read more about this model, check out [xAI's announcement](https://x.ai/blog/grok-1212).", "context_length": 32768, "architecture": {"modality": "text+image->text", "tokenizer": "Grok", "instruct_type": null}, "pricing": {"prompt": "0.000002", "completion": "0.00001", "image": "0.0036", "request": "0", "input_cache_read": "0", "input_cache_write": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 32768, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}, {"id": "x-ai/grok-2-1212", "name": "xAI: Grok 2 1212", "created": **********, "description": "Grok 2 1212 introduces significant enhancements to accuracy, instruction adherence, and multilingual support, making it a powerful and flexible choice for developers seeking a highly steerable, intelligent model.", "context_length": 131072, "architecture": {"modality": "text->text", "tokenizer": "Grok", "instruct_type": null}, "pricing": {"prompt": "0.000002", "completion": "0.00001", "image": "0", "request": "0", "input_cache_read": "0", "input_cache_write": "0", "web_search": "0", "internal_reasoning": "0"}, "top_provider": {"context_length": 131072, "max_completion_tokens": null, "is_moderated": false}, "per_request_limits": null}]