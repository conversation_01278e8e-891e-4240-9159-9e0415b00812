import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Bot,
  Mic,
  Send,
  Volume2,
  MessageSquare,
  Clock,
  Zap,
} from "lucide-react";

interface WhisperInteraction {
  id: string;
  type: "ai-to-user" | "user-to-ai" | "system";
  message: string;
  timestamp: string;
  confidence?: number;
  responseTime?: number;
}

interface WhisperInteractionPanelProps {
  whisperMode: "ai-to-user" | "user-to-ai" | "normal";
  interactions: WhisperInteraction[];
  onSendWhisper?: (message: string) => Promise<void>;
  isListening?: boolean;
  canSendWhispers?: boolean;
}

export const WhisperInteractionPanel: React.FC<
  WhisperInteractionPanelProps
> = ({
  whisperMode,
  interactions,
  onSendWhisper,
  isListening = false,
  canSendWhispers = false,
}) => {
  const [whisperMessage, setWhisperMessage] = useState("");
  const [isSending, setIsSending] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new interactions arrive
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [interactions]);

  const handleSendWhisper = async () => {
    if (!whisperMessage.trim() || !onSendWhisper || isSending) return;

    setIsSending(true);
    try {
      await onSendWhisper(whisperMessage);
      setWhisperMessage("");
    } catch (error) {
      console.error("Failed to send whisper:", error);
    } finally {
      setIsSending(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendWhisper();
    }
  };

  const getInteractionIcon = (type: string) => {
    switch (type) {
      case "ai-to-user":
        return <Bot className="h-4 w-4 text-blue-600" />;
      case "user-to-ai":
        return <Mic className="h-4 w-4 text-green-600" />;
      case "system":
        return <MessageSquare className="h-4 w-4 text-gray-600" />;
      default:
        return <MessageSquare className="h-4 w-4" />;
    }
  };

  const getInteractionBadge = (type: string) => {
    switch (type) {
      case "ai-to-user":
        return (
          <Badge variant="secondary" className="text-xs">
            AI Suggestion
          </Badge>
        );
      case "user-to-ai":
        return (
          <Badge variant="outline" className="text-xs">
            Your Whisper
          </Badge>
        );
      case "system":
        return (
          <Badge variant="default" className="text-xs">
            System
          </Badge>
        );
      default:
        return null;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  };

  const getModeDescription = () => {
    switch (whisperMode) {
      case "ai-to-user":
        return "AI is providing whisper suggestions to you";
      case "user-to-ai":
        return "You can whisper instructions to the AI";
      case "normal":
        return "Normal conference mode - no whispers active";
      default:
        return "Unknown mode";
    }
  };

  return (
    <Card className="w-full h-full flex flex-col">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Zap className="h-5 w-5" />
          Whisper Interactions
          {isListening && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                    <Volume2 className="h-4 w-4 text-red-500" />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>AI is listening</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </CardTitle>
        <p className="text-sm text-muted-foreground">{getModeDescription()}</p>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col space-y-4">
        {/* Interactions List */}
        <ScrollArea className="flex-1 h-64" ref={scrollAreaRef}>
          <div className="space-y-3">
            {interactions.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No whisper interactions yet</p>
                <p className="text-xs">
                  {whisperMode === "ai-to-user" &&
                    "AI will provide suggestions as the conversation progresses"}
                  {whisperMode === "user-to-ai" &&
                    "Type a whisper below to instruct the AI"}
                  {whisperMode === "normal" &&
                    "Switch to a whisper mode to see interactions"}
                </p>
              </div>
            ) : (
              interactions.map((interaction) => (
                <div
                  key={interaction.id}
                  className={`p-3 rounded-lg border ${
                    interaction.type === "ai-to-user"
                      ? "bg-blue-50 border-blue-200"
                      : interaction.type === "user-to-ai"
                      ? "bg-green-50 border-green-200"
                      : "bg-gray-50 border-gray-200"
                  }`}
                >
                  <div className="flex items-start gap-2 mb-2">
                    {getInteractionIcon(interaction.type)}
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        {getInteractionBadge(interaction.type)}
                        <span className="text-xs text-muted-foreground flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {formatTimestamp(interaction.timestamp)}
                        </span>
                        {interaction.responseTime && (
                          <span className="text-xs text-muted-foreground">
                            ({interaction.responseTime}ms)
                          </span>
                        )}
                      </div>
                      <p className="text-sm">{interaction.message}</p>
                      {interaction.confidence && (
                        <div className="mt-1">
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-muted-foreground">
                              Confidence:
                            </span>
                            <div className="flex-1 bg-gray-200 rounded-full h-1">
                              <div
                                className="bg-blue-500 h-1 rounded-full"
                                style={{
                                  width: `${interaction.confidence * 100}%`,
                                }}
                              />
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {Math.round(interaction.confidence * 100)}%
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>

        {/* Whisper Input (for user-to-ai mode) */}
        {whisperMode === "user-to-ai" && canSendWhispers && (
          <>
            <Separator />
            <div className="space-y-2">
              <label className="text-sm font-medium">Whisper to AI</label>
              <div className="flex gap-2">
                <Input
                  placeholder="Type your whisper to the AI..."
                  value={whisperMessage}
                  onChange={(e) => setWhisperMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  disabled={isSending}
                  className="flex-1"
                />
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="sm"
                        onClick={handleSendWhisper}
                        disabled={!whisperMessage.trim() || isSending}
                      >
                        <Send className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Send whisper to AI</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
              <p className="text-xs text-muted-foreground">
                Examples: "What should I ask next?", "Focus on pricing", "Help
                me close this deal"
              </p>
            </div>
          </>
        )}

        {/* Mode-specific tips */}
        {whisperMode !== "normal" && (
          <div className="p-2 bg-muted rounded-lg">
            <p className="text-xs text-muted-foreground">
              {whisperMode === "ai-to-user" && (
                <>
                  💡 <strong>Tip:</strong> The AI will automatically provide
                  suggestions based on the conversation and your goals.
                </>
              )}
              {whisperMode === "user-to-ai" && (
                <>
                  💡 <strong>Tip:</strong> Whisper instructions like "suggest a
                  follow-up question" or "help me handle this objection".
                </>
              )}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
