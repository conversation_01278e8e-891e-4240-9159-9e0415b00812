import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Mi<PERSON>,
  MicOff,
  Bot,
  Users,
  Phone,
  Settings,
  Volume2,
  VolumeX,
  Activity,
} from "lucide-react";
import { WhisperState } from "./types";

interface WhisperControlsProps {
  state: WhisperState;
  onSetWhisperMode: (mode: "ai-to-user" | "user-to-ai" | "normal") => Promise<boolean>;
  onTriggerAiAgent: () => Promise<boolean>;
  onGetRoomState: () => Promise<any>;
  onToggleMute: () => void;
  onVolumeChange: (volume: number) => void;
}

export const WhisperControls: React.FC<WhisperControlsProps> = ({
  state,
  onSetWhisperMode,
  onTriggerAiAgent,
  onGetRoomState,
  onToggleMute,
  onVolumeChange,
}) => {
  const handleModeChange = async (mode: string) => {
    const whisperMode = mode as "ai-to-user" | "user-to-ai" | "normal";
    await onSetWhisperMode(whisperMode);
  };

  const handleTriggerAi = async () => {
    await onTriggerAiAgent();
  };

  const handleRefreshRoom = async () => {
    await onGetRoomState();
  };

  const getModeDescription = (mode: string) => {
    switch (mode) {
      case "ai-to-user":
        return "AI provides whisper suggestions to you";
      case "user-to-ai":
        return "You can whisper instructions to the AI";
      case "normal":
        return "Normal conference mode - everyone hears everyone";
      default:
        return "Unknown mode";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "connected":
        return "bg-green-500";
      case "connecting":
        return "bg-yellow-500";
      case "error":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "connected":
        return "Connected";
      case "connecting":
        return "Connecting";
      case "error":
        return "Error";
      default:
        return "Disconnected";
    }
  };

  if (!state.activeCall) {
    return null;
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Settings className="h-5 w-5" />
          Whisper Controls
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Connection Status */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">LiveKit Status</span>
            <Badge variant="outline" className="flex items-center gap-1">
              <div className={`w-2 h-2 rounded-full ${getStatusColor(state.livekitState)}`} />
              {getStatusText(state.livekitState)}
            </Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">AI Assistant</span>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${getStatusColor(state.aiAgentStatus)}`} />
                {getStatusText(state.aiAgentStatus)}
              </Badge>
              {state.aiAgentStatus === "disconnected" && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleTriggerAi}
                        disabled={state.loading}
                      >
                        <Bot className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Connect AI Assistant</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </div>
        </div>

        <Separator />

        {/* Whisper Mode Control */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Whisper Mode</label>
          <Select
            value={state.whisperMode}
            onValueChange={handleModeChange}
            disabled={state.loading || state.aiAgentStatus !== "connected"}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select whisper mode" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ai-to-user">
                <div className="flex items-center gap-2">
                  <Bot className="h-4 w-4" />
                  AI to User
                </div>
              </SelectItem>
              <SelectItem value="user-to-ai">
                <div className="flex items-center gap-2">
                  <Mic className="h-4 w-4" />
                  User to AI
                </div>
              </SelectItem>
              <SelectItem value="normal">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Normal Conference
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground">
            {getModeDescription(state.whisperMode)}
          </p>
        </div>

        <Separator />

        {/* Audio Controls */}
        <div className="space-y-3">
          <label className="text-sm font-medium">Audio Controls</label>
          
          {/* Microphone Toggle */}
          <div className="flex items-center justify-between">
            <span className="text-sm">Microphone</span>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    size="sm"
                    variant={state.micMuted ? "destructive" : "default"}
                    onClick={onToggleMute}
                  >
                    {state.micMuted ? (
                      <MicOff className="h-4 w-4" />
                    ) : (
                      <Mic className="h-4 w-4" />
                    )}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{state.micMuted ? "Unmute" : "Mute"} microphone</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          {/* Volume Control */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm">Volume</span>
              <span className="text-xs text-muted-foreground">{state.volume}%</span>
            </div>
            <div className="flex items-center gap-2">
              <VolumeX className="h-4 w-4 text-muted-foreground" />
              <input
                type="range"
                min="0"
                max="100"
                value={state.volume}
                onChange={(e) => onVolumeChange(parseInt(e.target.value))}
                className="flex-1"
              />
              <Volume2 className="h-4 w-4 text-muted-foreground" />
            </div>
          </div>
        </div>

        <Separator />

        {/* Room Information */}
        {state.roomState && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Room Info</span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleRefreshRoom}
                    >
                      <Activity className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Refresh room state</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            <div className="text-xs text-muted-foreground">
              <div>Participants: {state.roomState.participantCount}</div>
              {state.connectionDetails && (
                <div>Room: {state.connectionDetails.roomName}</div>
              )}
            </div>
          </div>
        )}

        {/* Contact Information */}
        {state.selectedContact && (
          <div className="space-y-2">
            <span className="text-sm font-medium">Contact</span>
            <div className="text-xs text-muted-foreground">
              <div className="flex items-center gap-2">
                <Phone className="h-3 w-3" />
                {state.selectedContact.name}
              </div>
              <div>{state.selectedContact.phone}</div>
              <Badge variant="secondary" className="text-xs">
                {state.selectedContact.type}
              </Badge>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
