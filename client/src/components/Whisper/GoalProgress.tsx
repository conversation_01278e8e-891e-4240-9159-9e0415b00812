import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Goal } from "./types";
import { CheckCircle2 } from "lucide-react";

interface GoalProgressProps {
  goals: Goal[];
}

export function GoalProgress({ goals }: GoalProgressProps) {
  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader>
        <CardTitle className="text-white flex justify-between items-center">
          <span>Call Goals</span>
          <span className="text-sm text-gray-400">
            {goals.filter(g => g.completed).length}/{goals.length} Completed
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {goals.length === 0 ? (
            <div className="text-center py-4 text-gray-400">
              No goals set for this call
            </div>
          ) : (
            goals.map((goal) => (
              <div key={goal.id} className="space-y-2">
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    {goal.completed && (
                      <CheckCircle2 className="h-4 w-4 text-green-500 mr-2" />
                    )}
                    <span className={`font-medium ${goal.completed ? 'text-green-400' : 'text-white'}`}>
                      {goal.title}
                    </span>
                  </div>
                  <span className="text-sm text-gray-400">
                    {Math.round(goal.progress * 100)}%
                  </span>
                </div>
                <Progress 
                  value={goal.progress * 100} 
                  className="h-2 bg-gray-700"
                  indicatorClassName={goal.completed ? "bg-green-500" : "bg-teal-600"}
                />
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}
