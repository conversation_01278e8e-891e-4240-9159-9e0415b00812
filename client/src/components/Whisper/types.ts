export interface Contact {
  id: string;
  name: string;
  phone: string; // This is 'number' in our local interface but 'phone' in the backend
  email: string;
  type: "business" | "personal";
  transparencyLevel?: "FULL" | "PARTIAL" | "NONE";
  subcategory?: string;
  customSubcategory?: string;
  campaignId?: string;
  tags?: string[];
  notes?: string;
}

export interface CallTranscriptEntry {
  type: "ai" | "user";
  message: string;
  timestamp?: string;
}

export interface ConnectionDetails {
  serverUrl: string;
  roomName: string;
  participantName: string;
  participantToken: string;
  twilioSid?: string;
  twilioStatus?: string;
}

export interface Goal {
  id: string;
  title: string;
  aiPrompt: string;
  completed: boolean;
  progress: number;
}

export interface WhisperState {
  activeCall: boolean;
  selectedContact: Contact | null;
  goals: Goal[];
  contacts: Contact[];
  showContactDialog: boolean;
  showWhisperSetupDialog: boolean;
  newContact: {
    name: string;
    phone: string;
    email: string;
    type: "business" | "personal";
  };
  contactSearch: string;
  showContactsSheet: boolean;
  whisperEnabled: boolean;
  micMuted: boolean;
  volume: number;
  callTranscript: CallTranscriptEntry[];
  userMessage: string;
  isListening: boolean;
  connectionDetails?: ConnectionDetails;
  livekitState: "disconnected" | "connecting" | "connected" | "error";
  transcribing: boolean;
  speechRecognitionActive: boolean;
  currentCallId?: string;
  loading: boolean;
  error: string | null;
}

export interface WhisperTemplate {
  name: string;
  systemPrompt: string;
  editablePrompt: string;
}
