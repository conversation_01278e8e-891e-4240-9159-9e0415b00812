export interface Contact {
  id: string;
  name: string;
  phone: string; // This is 'number' in our local interface but 'phone' in the backend
  email: string;
  type: "business" | "personal";
  transparencyLevel?: "FULL" | "PARTIAL" | "NONE";
  subcategory?: string;
  customSubcategory?: string;
  campaignId?: string;
  tags?: string[];
  notes?: string;
}

export interface CallTranscriptEntry {
  type: "ai" | "user" | "system";
  message: string;
  timestamp?: string;
}

// Participant details for three-participant architecture
export interface ParticipantDetails {
  token: string;
  identity: string;
  type: "user" | "caller" | "ai_assistant";
}

// Connection details types for enhanced whisper functionality
export interface ConnectionDetails {
  serverUrl: string;
  roomName: string;
  // Legacy fields for backward compatibility
  participantToken?: string;
  participantName?: string;
  // New three-participant architecture
  userParticipant: ParticipantDetails;
  aiParticipant: ParticipantDetails;
  callerParticipant: Omit<ParticipantDetails, "token">;
  callId: string;
  whisperConfig: {
    mode: "ai-to-user" | "user-to-ai" | "normal";
    goals: any[];
  };
  agentInstructions: {
    systemPrompt: string;
    contactInfo: {
      name: string;
      type: string;
      phone?: string;
      email?: string;
    };
  };
  twilioSid?: string;
  twilioStatus?: string;
}

export interface Goal {
  id: string;
  title: string;
  aiPrompt: string;
  completed: boolean;
  progress: number;
}

export interface WhisperState {
  activeCall: boolean;
  selectedContact: Contact | null;
  goals: Goal[];
  contacts: Contact[];
  showContactDialog: boolean;
  showWhisperSetupDialog: boolean;
  newContact: {
    name: string;
    phone: string;
    email: string;
    type: "business" | "personal";
  };
  contactSearch: string;
  showContactsSheet: boolean;
  whisperEnabled: boolean;
  micMuted: boolean;
  volume: number;
  callTranscript: CallTranscriptEntry[];
  userMessage: string;
  isListening: boolean;
  connectionDetails?: ConnectionDetails;
  livekitState: "disconnected" | "connecting" | "connected" | "error";
  transcribing: boolean;
  speechRecognitionActive: boolean;
  currentCallId?: string;
  loading: boolean;
  error: string | null;
  // Enhanced whisper functionality
  whisperMode: "ai-to-user" | "user-to-ai" | "normal";
  aiAgentStatus: "disconnected" | "connecting" | "connected" | "error";
  participantIdentities?: {
    user: string;
    caller: string;
    ai: string;
  };
  roomState?: {
    participantCount: number;
    participants: any[];
  };
}

export interface WhisperTemplate {
  name: string;
  systemPrompt: string;
  editablePrompt: string;
}
