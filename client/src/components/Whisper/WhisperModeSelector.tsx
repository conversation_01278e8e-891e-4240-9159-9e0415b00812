import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Bot,
  Mic,
  Users,
  ArrowRight,
  ArrowLeft,
  ArrowUpDown,
  Info,
  Zap,
} from "lucide-react";

interface WhisperModeSelectorProps {
  currentMode: "ai-to-user" | "user-to-ai" | "normal";
  onModeChange: (
    mode: "ai-to-user" | "user-to-ai" | "normal"
  ) => Promise<boolean>;
  disabled?: boolean;
  aiAgentStatus: "disconnected" | "connecting" | "connected" | "error";
}

export const WhisperModeSelector: React.FC<WhisperModeSelectorProps> = ({
  currentMode,
  onModeChange,
  disabled = false,
  aiAgentStatus,
}) => {
  const [isChanging, setIsChanging] = useState(false);
  const [showModeInfo, setShowModeInfo] = useState(false);

  const handleModeChange = async (
    newMode: "ai-to-user" | "user-to-ai" | "normal"
  ) => {
    if (newMode === currentMode || isChanging) return;

    setIsChanging(true);
    try {
      const success = await onModeChange(newMode);
      if (!success) {
        // Handle error - mode change failed
        console.error("Failed to change whisper mode");
      }
    } finally {
      setIsChanging(false);
    }
  };

  const getModeConfig = (mode: string) => {
    switch (mode) {
      case "ai-to-user":
        return {
          title: "AI → User",
          description: "AI provides whisper suggestions to you",
          icon: <ArrowRight className="h-4 w-4" />,
          color: "bg-blue-500",
          textColor: "text-blue-700",
          bgColor: "bg-blue-50",
          details:
            "The AI listens to the conversation and provides helpful suggestions that only you can hear.",
        };
      case "user-to-ai":
        return {
          title: "User → AI",
          description: "You can whisper instructions to the AI",
          icon: <ArrowLeft className="h-4 w-4" />,
          color: "bg-green-500",
          textColor: "text-green-700",
          bgColor: "bg-green-50",
          details:
            "You can whisper instructions to the AI, and it will acknowledge and adjust its behavior accordingly.",
        };
      case "normal":
        return {
          title: "Normal",
          description: "Standard conference mode",
          icon: <Users className="h-4 w-4" />,
          color: "bg-gray-500",
          textColor: "text-gray-700",
          bgColor: "bg-gray-50",
          details:
            "Everyone in the call can hear each other normally. No whisper functionality is active.",
        };
      default:
        return {
          title: "Unknown",
          description: "Unknown mode",
          icon: <ArrowUpDown className="h-4 w-4" />,
          color: "bg-gray-500",
          textColor: "text-gray-700",
          bgColor: "bg-gray-50",
          details: "Unknown whisper mode.",
        };
    }
  };

  const currentConfig = getModeConfig(currentMode);
  const isAiRequired = currentMode !== "normal";
  const canUseMode = !isAiRequired || aiAgentStatus === "connected";
  console.log("canUseMode", canUseMode);
  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Zap className="h-5 w-5" />
          Whisper Mode
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowModeInfo(true)}
                >
                  <Info className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Learn about whisper modes</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Mode Display */}
        <div className={`p-3 rounded-lg ${currentConfig.bgColor}`}>
          <div className="flex items-center gap-2 mb-2">
            <div className={`p-1 rounded ${currentConfig.color} text-white`}>
              {currentConfig.icon}
            </div>
            <span className={`font-medium ${currentConfig.textColor}`}>
              {currentConfig.title}
            </span>
            <Badge variant="outline" className="ml-auto">
              Active
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground">
            {currentConfig.description}
          </p>
        </div>

        <Separator />

        {/* Mode Selection Buttons */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Switch Mode</label>
          <div className="grid grid-cols-1 gap-2">
            {/* AI to User Mode */}
            <Button
              variant={currentMode === "ai-to-user" ? "default" : "outline"}
              className="justify-start h-auto p-3"
              onClick={() => handleModeChange("ai-to-user")}
              disabled={disabled || isChanging || aiAgentStatus !== "connected"}
            >
              <div className="flex items-center gap-3 w-full">
                <div className="flex items-center gap-2">
                  <Bot className="h-4 w-4" />
                  <ArrowRight className="h-4 w-4" />
                  <Mic className="h-4 w-4" />
                </div>
                <div className="text-left flex-1">
                  <div className="font-medium">AI → User</div>
                  <div className="text-xs text-muted-foreground">
                    AI suggests to you
                  </div>
                </div>
              </div>
            </Button>

            {/* User to AI Mode */}
            <Button
              variant={currentMode === "user-to-ai" ? "default" : "outline"}
              className="justify-start h-auto p-3"
              onClick={() => handleModeChange("user-to-ai")}
              disabled={disabled || isChanging || aiAgentStatus !== "connected"}
            >
              <div className="flex items-center gap-3 w-full">
                <div className="flex items-center gap-2">
                  <Mic className="h-4 w-4" />
                  <ArrowRight className="h-4 w-4" />
                  <Bot className="h-4 w-4" />
                </div>
                <div className="text-left flex-1">
                  <div className="font-medium">User → AI</div>
                  <div className="text-xs text-muted-foreground">
                    You instruct the AI
                  </div>
                </div>
              </div>
            </Button>

            {/* Normal Mode */}
            <Button
              variant={currentMode === "normal" ? "default" : "outline"}
              className="justify-start h-auto p-3"
              onClick={() => handleModeChange("normal")}
              disabled={disabled || isChanging}
            >
              <div className="flex items-center gap-3 w-full">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                </div>
                <div className="text-left flex-1">
                  <div className="font-medium">Normal Conference</div>
                  <div className="text-xs text-muted-foreground">
                    Everyone hears everyone
                  </div>
                </div>
              </div>
            </Button>
          </div>
        </div>

        {/* AI Agent Status Warning */}
        {isAiRequired && aiAgentStatus !== "connected" && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center gap-2 text-yellow-800">
              <Info className="h-4 w-4" />
              <span className="text-sm font-medium">AI Agent Required</span>
            </div>
            <p className="text-xs text-yellow-700 mt-1">
              Connect the AI agent to use whisper modes.
            </p>
          </div>
        )}
      </CardContent>

      {/* Mode Information Dialog */}
      <Dialog open={showModeInfo} onOpenChange={setShowModeInfo}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Whisper Modes Explained</DialogTitle>
            <DialogDescription>
              Understanding how each whisper mode works
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {["ai-to-user", "user-to-ai", "normal"].map((mode) => {
              const config = getModeConfig(mode);
              return (
                <div key={mode} className={`p-3 rounded-lg ${config.bgColor}`}>
                  <div className="flex items-center gap-2 mb-2">
                    <div className={`p-1 rounded ${config.color} text-white`}>
                      {config.icon}
                    </div>
                    <span className={`font-medium ${config.textColor}`}>
                      {config.title}
                    </span>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {config.details}
                  </p>
                </div>
              );
            })}
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
};
