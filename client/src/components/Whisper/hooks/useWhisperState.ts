import { useState, useCallback, useEffect, useRef } from "react";
import { useToast } from "@/components/ui/use-toast";
import type {
  WhisperState,
  Contact,
  CallTranscriptEntry,
  ConnectionDetails,
  Goal,
} from "../types";
import { whisperApi } from "@/services/whisperApi";
import { contactApi } from "@/services/contactApi";
import { apiClient } from "@/services/api";
import {
  Room,
  RoomEvent,
  LocalTrack,
  Track,
  createLocalAudioTrack,
} from "livekit-client";

// Initial empty goals array - will be populated based on selected contact
const initialGoals: Goal[] = [];

const initialState: WhisperState = {
  activeCall: false,
  selectedContact: null,
  goals: initialGoals,
  contacts: [], // Will be populated from the API
  showContactDialog: false,
  showWhisperSetupDialog: false,
  newContact: { name: "", phone: "", email: "", type: "personal" },
  contactSearch: "",
  showContactsSheet: false,
  whisperEnabled: false,
  micMuted: false,
  volume: 50,
  callTranscript: [],
  userMessage: "",
  isListening: false,
  connectionDetails: undefined,
  livekitState: "disconnected",
  transcribing: false,
  speechRecognitionActive: false,
  currentCallId: undefined,
  loading: false,
  error: null,
  // Enhanced whisper functionality
  whisperMode: "ai-to-user",
  aiAgentStatus: "disconnected",
  participantIdentities: undefined,
  roomState: undefined,
};

// Add type definitions for SpeechRecognition
interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  maxAlternatives: number;
  onaudioend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onaudiostart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onerror: ((this: SpeechRecognition, ev: any) => any) | null;
  onnomatch: ((this: SpeechRecognition, ev: Event) => any) | null;
  onresult: ((this: SpeechRecognition, ev: any) => any) | null;
  onsoundend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onsoundstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onspeechend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onspeechstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  start(): void;
  stop(): void;
  abort(): void;
}

interface SpeechRecognitionConstructor {
  new (): SpeechRecognition;
  prototype: SpeechRecognition;
}

declare global {
  interface Window {
    SpeechRecognition: SpeechRecognitionConstructor;
    webkitSpeechRecognition: SpeechRecognitionConstructor;
  }
}

interface SpeechRecognitionResultList {
  [index: number]: SpeechRecognitionResult;
  length: number;
  item(index: number): SpeechRecognitionResult;
}

interface SpeechRecognitionResult {
  [index: number]: SpeechRecognitionAlternative;
  length: number;
  item(index: number): SpeechRecognitionResult;
  isFinal: boolean;
}

interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

export function useWhisperState() {
  const [state, setState] = useState<WhisperState>(initialState);
  const { toast } = useToast();
  const roomRef = useRef<Room | null>(null);
  const localTrackRef = useRef<LocalTrack | null>(null);
  const speechRecognitionRef = useRef<any | null>(null);

  const set = useCallback((field: keyof WhisperState, value: any) => {
    setState((prev) => ({ ...prev, [field]: value }));
  }, []);

  // Function to fetch contacts from the API
  const fetchContacts = useCallback(async () => {
    try {
      set("loading", true);
      set("error", null);

      const response = await contactApi.getAll();

      if (response.success && response.data) {
        // Map the contacts to our local interface
        const mappedContacts = response.data.map((contact) => ({
          id: contact.id,
          name: contact.name,
          phone: contact.phone,
          email: contact.email,
          type: contact.type.toLowerCase() as "business" | "personal",
          transparencyLevel: contact.transparencyLevel,
          subcategory: contact.subcategory,
          customSubcategory: contact.customSubcategory,
          campaignId: contact.campaignId,
          tags: contact.tags,
          notes: contact.notes,
        }));

        set("contacts", mappedContacts);
      } else {
        throw new Error(response.error?.message || "Failed to fetch contacts");
      }
    } catch (error) {
      console.error("Error fetching contacts:", error);
      set("error", "Failed to fetch contacts. Please try again.");
      toast({
        title: "Error",
        description: "Failed to fetch contacts. Please try again.",
        variant: "destructive",
      });
    } finally {
      set("loading", false);
    }
  }, [set, toast]);

  // Function to connect to LiveKit room
  const connectToLiveKit = useCallback(
    async (connectionDetails: ConnectionDetails) => {
      try {
        console.log("Starting LiveKit connection process");

        // Create a new room if it doesn't exist
        if (!roomRef.current) {
          console.log("Creating new LiveKit room instance");
          roomRef.current = new Room({
            adaptiveStream: true,
            dynacast: true,
          });

          // Set up event listeners
          roomRef.current.on(RoomEvent.Connected, () => {
            console.log("LiveKit room connected event fired");
            set("livekitState", "connected");
            toast({
              title: "Call Connected",
              description: "You are now connected to the call.",
            });
          });

          roomRef.current.on(RoomEvent.Disconnected, () => {
            console.log("LiveKit room disconnected event fired");
            set("livekitState", "disconnected");
            set("activeCall", false);
            toast({
              title: "Call Disconnected",
              description: "The call has been disconnected.",
            });
          });

          roomRef.current.on(
            RoomEvent.TrackSubscribed,
            (track, _publication, participant) => {
              console.log("Track subscribed event:", {
                kind: track.kind,
                name: track.mediaStreamTrack?.label || "unnamed track",
                participant: participant.identity,
                metadata: participant.metadata,
              });

              if (track.kind === Track.Kind.Audio) {
                // Check if this is from a SIP participant (the called person)
                const isSipParticipant =
                  participant.identity.startsWith("sip_");

                // Create a unique ID for this audio element based on participant identity
                const audioElementId = `audio-${participant.identity}-${track.sid}`;
                console.log(
                  `Creating audio element with ID: ${audioElementId}`
                );

                // Handle incoming audio
                console.log("Attaching audio track to audio element");
                const audioElement = document.createElement("audio");
                audioElement.id = audioElementId;
                audioElement.autoplay = true;
                document.body.appendChild(audioElement);
                track.attach(audioElement);

                // Set volume based on current state
                audioElement.volume = state.volume / 100;
                console.log(
                  `Set audio element volume to ${audioElement.volume}`
                );

                // Add transcript entry for SIP participant connection
                if (isSipParticipant) {
                  set("callTranscript", [
                    ...state.callTranscript,
                    {
                      type: "ai",
                      message:
                        "Call connected. The other person can now hear you.",
                      timestamp: new Date().toISOString(),
                    },
                  ]);
                }
              }
            }
          );

          // Add more event listeners for debugging
          roomRef.current.on(RoomEvent.ParticipantConnected, (participant) => {
            console.log("Participant connected:", {
              identity: participant.identity,
              metadata: participant.metadata,
              name: participant.name,
              kind: participant.kind,
            });

            // Check if this is a SIP participant (the called person)
            const isSipParticipant = participant.identity.startsWith("sip_");

            if (isSipParticipant) {
              // Add transcript entry for SIP participant connection
              set("callTranscript", [
                ...state.callTranscript,
                {
                  type: "ai",
                  message:
                    "Call participant joined. Waiting for audio connection...",
                  timestamp: new Date().toISOString(),
                },
              ]);
            }
          });

          roomRef.current.on(
            RoomEvent.ParticipantDisconnected,
            (participant) => {
              console.log("Participant disconnected:", {
                identity: participant.identity,
                metadata: participant.metadata,
                name: participant.name,
                kind: participant.kind,
              });

              // Check if this is a SIP participant (the called person)
              const isSipParticipant = participant.identity.startsWith("sip_");

              if (isSipParticipant) {
                // Add transcript entry for SIP participant disconnection
                set("callTranscript", [
                  ...state.callTranscript,
                  {
                    type: "ai",
                    message: "The other person has disconnected from the call.",
                    timestamp: new Date().toISOString(),
                  },
                ]);
              }
            }
          );

          roomRef.current.on(RoomEvent.ConnectionStateChanged, (state) => {
            console.log("Connection state changed:", state);
          });

          roomRef.current.on(RoomEvent.MediaDevicesError, (error) => {
            console.error("Media devices error:", error);
          });
        }

        // Connect to the room
        console.log("Connecting to LiveKit room:", {
          serverUrl: connectionDetails.serverUrl,
          roomName: connectionDetails.roomName,
          participantName: connectionDetails.participantName,
        });

        // Use the user participant token for connection
        const userToken =
          connectionDetails.userParticipant?.token ||
          connectionDetails.participantToken;
        if (!userToken) {
          throw new Error("No valid participant token found");
        }

        await roomRef.current.connect(connectionDetails.serverUrl, userToken);

        // Store participant identities for whisper mode control
        if (
          connectionDetails.userParticipant &&
          connectionDetails.callerParticipant &&
          connectionDetails.aiParticipant
        ) {
          set("participantIdentities", {
            user: connectionDetails.userParticipant.identity,
            caller: connectionDetails.callerParticipant.identity,
            ai: connectionDetails.aiParticipant.identity,
          });
        }

        console.log("Connected to LiveKit room successfully");

        // Create and publish local audio track
        console.log("Creating local audio track");
        const localTrack = await createLocalAudioTrack({
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        });

        console.log(
          "Local audio track created:",
          localTrack.mediaStreamTrack.id
        );
        localTrackRef.current = localTrack;

        console.log("Publishing local audio track");
        await roomRef.current.localParticipant.publishTrack(localTrack);
        console.log("Local audio track published successfully");

        // Add Twilio call information to transcript if available
        if (connectionDetails.twilioSid) {
          set("callTranscript", [
            ...state.callTranscript,
            {
              type: "ai",
              message:
                "LiveKit connection established. Waiting for Twilio call to connect...",
              timestamp: new Date().toISOString(),
            },
          ]);
        }

        return true;
      } catch (error) {
        console.error("Error connecting to LiveKit:", error);
        set("livekitState", "error");
        toast({
          title: "Connection Error",
          description: "Failed to establish call connection.",
          variant: "destructive",
        });
        return false;
      }
    },
    [set, toast, state.volume, state.callTranscript]
  );

  // Function to set whisper mode
  const setWhisperMode = useCallback(
    async (mode: "ai-to-user" | "user-to-ai" | "normal") => {
      if (
        !state.currentCallId ||
        !state.connectionDetails ||
        !state.participantIdentities
      ) {
        console.error(
          "Cannot set whisper mode: missing call or participant information"
        );
        return false;
      }

      try {
        set("loading", true);

        const response = await whisperApi.setWhisperMode(state.currentCallId, {
          mode,
          roomName: state.connectionDetails.roomName,
          participants: {
            userIdentity: state.participantIdentities.user,
            callerIdentity: state.participantIdentities.caller,
            aiIdentity: state.participantIdentities.ai,
          },
        });

        if (response.success) {
          set("whisperMode", mode);

          // Add transcript entry about mode change
          const modeDescriptions = {
            "ai-to-user": "AI will provide whisper suggestions to you",
            "user-to-ai": "You can whisper instructions to the AI",
            normal: "Normal conference mode - everyone hears everyone",
          };

          set("callTranscript", [
            ...state.callTranscript,
            {
              type: "system",
              message: `Whisper mode changed to: ${modeDescriptions[mode]}`,
              timestamp: new Date().toISOString(),
            },
          ]);

          toast({
            title: "Whisper Mode Updated",
            description: modeDescriptions[mode],
          });

          return true;
        } else {
          throw new Error(
            response.error?.message || "Failed to set whisper mode"
          );
        }
      } catch (error) {
        console.error("Error setting whisper mode:", error);
        toast({
          title: "Error",
          description: "Failed to change whisper mode",
          variant: "destructive",
        });
        return false;
      } finally {
        set("loading", false);
      }
    },
    [
      state.currentCallId,
      state.connectionDetails,
      state.participantIdentities,
      state.callTranscript,
      set,
      toast,
    ]
  );

  // Function to trigger AI agent
  const triggerAiAgent = useCallback(async () => {
    if (!state.currentCallId || !state.connectionDetails) {
      console.error("Cannot trigger AI agent: missing call information");
      return false;
    }

    try {
      set("aiAgentStatus", "connecting");

      const response = await whisperApi.triggerAiAgent({
        callId: state.currentCallId,
        roomName: state.connectionDetails.roomName,
        agentInstructions: state.connectionDetails.agentInstructions,
      });

      if (response.success) {
        set("aiAgentStatus", "connected");

        // Add transcript entry about AI agent joining
        set("callTranscript", [
          ...state.callTranscript,
          {
            type: "system",
            message:
              "AI Assistant has joined the call and is ready to provide whisper suggestions",
            timestamp: new Date().toISOString(),
          },
        ]);

        toast({
          title: "AI Assistant Connected",
          description: "Your AI assistant is now listening and ready to help",
        });

        return true;
      } else {
        throw new Error(
          response.error?.message || "Failed to trigger AI agent"
        );
      }
    } catch (error) {
      console.error("Error triggering AI agent:", error);
      set("aiAgentStatus", "error");
      toast({
        title: "Error",
        description: "Failed to connect AI assistant",
        variant: "destructive",
      });
      return false;
    }
  }, [
    state.currentCallId,
    state.connectionDetails,
    state.callTranscript,
    set,
    toast,
  ]);

  // Function to get room state
  const getRoomState = useCallback(async () => {
    if (!state.connectionDetails) {
      return null;
    }

    try {
      const response = await whisperApi.getRoomState(
        state.connectionDetails.roomName
      );

      if (response.success && response.data) {
        set("roomState", {
          participantCount: response.data.participantCount,
          participants: response.data.participants,
        });
        return response.data;
      }
    } catch (error) {
      console.error("Error getting room state:", error);
    }

    return null;
  }, [state.connectionDetails, set]);

  // Function to disconnect from LiveKit
  const disconnectFromLiveKit = useCallback(() => {
    console.log("Disconnecting from LiveKit");

    // Remove all audio elements created for the call
    // Find all audio elements that start with "audio-"
    const audioElements = document.querySelectorAll('audio[id^="audio-"]');
    console.log(`Found ${audioElements.length} audio elements to remove`);

    audioElements.forEach((element) => {
      console.log(`Removing audio element: ${element.id}`);
      element.remove();
    });

    if (roomRef.current) {
      console.log("Disconnecting from LiveKit room");
      roomRef.current.disconnect();
      roomRef.current = null;
    } else {
      console.log("No active LiveKit room to disconnect from");
    }

    if (localTrackRef.current) {
      console.log("Stopping local audio track");
      localTrackRef.current.stop();
      localTrackRef.current = null;
    } else {
      console.log("No active local audio track to stop");
    }

    set("connectionDetails", undefined);
    set("livekitState", "disconnected");
    console.log("LiveKit disconnection complete");
  }, [set]);

  // Function to process transcription and generate AI advice
  const processTranscriptionForAdvice = useCallback(
    async (text: string) => {
      // Add the user's message to the transcript
      const userEntry: CallTranscriptEntry = {
        type: "user",
        message: text,
        timestamp: new Date().toISOString(),
      };

      const newTranscript = [...state.callTranscript, userEntry];
      set("callTranscript", newTranscript);

      // Only process AI advice for business contacts with whisper enabled
      if (state.whisperEnabled && state.selectedContact?.type === "business") {
        // Check if the text matches any of our goals
        const relevantGoals = state.goals.filter((goal) => {
          // Simple check if the text contains keywords from the goal's prompt
          return text.toLowerCase().includes(goal.aiPrompt.toLowerCase());
        });

        if (relevantGoals.length > 0) {
          // Generate advice based on the first matching goal
          const goal = relevantGoals[0];

          // Simulate AI processing delay
          setTimeout(async () => {
            const aiResponse: CallTranscriptEntry = {
              type: "ai",
              message: `Based on your goal "${goal.title}": I suggest responding with more details about our premium features and how they can benefit them specifically.`,
              timestamp: new Date().toISOString(),
            };

            // Update transcript with AI response
            const updatedTranscript = [...newTranscript, aiResponse];
            set("callTranscript", updatedTranscript);

            // Update goal progress
            const updatedGoals = state.goals.map((g) => {
              if (g.id === goal.id) {
                return {
                  ...g,
                  progress: Math.min(g.progress + 0.25, 1),
                  completed: g.progress + 0.25 >= 1,
                };
              }
              return g;
            });
            set("goals", updatedGoals);

            // Update the call transcript and goals on the server
            if (state.currentCallId) {
              try {
                await whisperApi.updateCallTranscript(state.currentCallId, {
                  transcript: updatedTranscript,
                  goals: updatedGoals,
                });
              } catch (error) {
                console.error("Error updating call transcript:", error);
              }
            }
          }, 1500);
        }
      } else {
        // For personal contacts, just update the transcript on the server
        if (state.currentCallId) {
          try {
            await whisperApi.updateCallTranscript(state.currentCallId, {
              transcript: newTranscript,
              goals: state.goals,
            });
          } catch (error) {
            console.error("Error updating call transcript:", error);
          }
        }
      }
    },
    [
      state.goals,
      state.callTranscript,
      state.currentCallId,
      state.whisperEnabled,
      state.selectedContact,
      set,
    ]
  );

  // Function to start speech recognition
  const startSpeechRecognition = useCallback(() => {
    if (
      !("SpeechRecognition" in window) &&
      !("webkitSpeechRecognition" in window)
    ) {
      toast({
        title: "Speech Recognition Not Supported",
        description: "Your browser does not support speech recognition.",
        variant: "destructive",
      });
      return;
    }

    const SpeechRecognition =
      window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();
    recognition.continuous = true;
    recognition.interimResults = true;
    recognition.lang = "en-US";

    recognition.onstart = () => {
      set("speechRecognitionActive", true);
    };

    recognition.onresult = (event) => {
      // Type assertion to handle the event results
      const results = event.results as SpeechRecognitionResultList;
      const transcript = Array.from(results)
        .map((result) => result[0])
        .map((result) => result.transcript)
        .join("");

      // Only process final results
      if (results[0]?.isFinal) {
        // Process for AI advice
        processTranscriptionForAdvice(transcript);
      }
    };

    recognition.onerror = (event) => {
      console.error("Speech recognition error", event.error);
      set("speechRecognitionActive", false);
    };

    recognition.onend = () => {
      set("speechRecognitionActive", false);
    };

    recognition.start();
    speechRecognitionRef.current = recognition;

    toast({
      title: "Speech Recognition Active",
      description: "Now listening to your voice...",
    });
  }, [set, toast, processTranscriptionForAdvice]);

  // Function to stop speech recognition
  const stopSpeechRecognition = useCallback(() => {
    if (speechRecognitionRef.current) {
      speechRecognitionRef.current.stop();
      speechRecognitionRef.current = null;
    }
    set("speechRecognitionActive", false);
  }, [set]);

  const handleStartCall = useCallback(async () => {
    if (!state.selectedContact) {
      toast({
        title: "No Contact Selected",
        description: "Please select a contact before starting a call.",
        variant: "destructive",
      });
      return;
    }

    try {
      console.log("Starting call to contact:", state.selectedContact);

      // Only fetch goals for business contacts
      if (state.selectedContact.type === "business") {
        try {
          console.log(
            "Fetching goals for business contact:",
            state.selectedContact.id
          );
          // Fetch goals for this contact
          const goalsResponse = await whisperApi.getContactGoals(
            state.selectedContact.id
          );
          if (goalsResponse.success && goalsResponse.data) {
            console.log("Goals fetched successfully:", goalsResponse.data);
            set("goals", goalsResponse.data);
          }
        } catch (error: any) {
          console.error("Error fetching contact goals:", error);

          // If the contact doesn't exist, refresh the contacts list
          if (error.message?.includes("Contact not found")) {
            toast({
              title: "Contact Error",
              description:
                "Contact not found or not owned by you. Please refresh your contacts list.",
              variant: "destructive",
            });
            fetchContacts();
            return; // Exit the function early
          }
        }
      } else {
        // Clear goals for personal contacts
        console.log("Personal contact selected, clearing goals");
        set("goals", []);
      }

      // Show loading state
      set("loading", true);

      // Initiate outbound call using Twilio
      console.log(
        "Initiating outbound call to contact ID:",
        state.selectedContact.id
      );
      const callResponse = await whisperApi.initiateOutboundCall(
        state.selectedContact.id
      );

      console.log("Call response:", callResponse);

      if (!callResponse.success || !callResponse.data) {
        throw new Error(
          callResponse.error?.message || "Failed to initiate call"
        );
      }

      const connectionDetails = callResponse.data;
      console.log("Connection details received:", connectionDetails);

      // Store the call ID and connection details
      set("currentCallId", connectionDetails.callId);
      set("connectionDetails", connectionDetails);

      // Connect to LiveKit
      console.log("Connecting to LiveKit with details:", {
        serverUrl: connectionDetails.serverUrl,
        roomName: connectionDetails.roomName,
        participantToken:
          connectionDetails.participantToken?.substring(0, 20) + "..." || "N/A",
      });

      const connected = await connectToLiveKit(connectionDetails);
      if (!connected) {
        throw new Error("Failed to connect to LiveKit");
      }

      console.log("Successfully connected to LiveKit");

      // Start speech recognition
      console.log("Starting speech recognition");
      startSpeechRecognition();

      // Update state
      set("loading", false);
      set("activeCall", true);
      set("whisperEnabled", state.selectedContact.type === "business"); // Only enable whisper for business contacts
      set("callTranscript", [
        {
          type: "ai",
          message: `Calling ${state.selectedContact.name} at ${
            state.selectedContact.phone
          }... ${
            state.selectedContact.type === "business"
              ? "AI assistant is ready to help."
              : ""
          }`,
          timestamp: new Date().toISOString(),
        },
      ]);

      // Add Twilio call status information if available
      if (connectionDetails.twilioSid) {
        set("callTranscript", [
          ...state.callTranscript,
          {
            type: "ai",
            message: `Twilio call initiated with SID: ${
              connectionDetails.twilioSid
            }. Status: ${connectionDetails.twilioStatus || "unknown"}`,
            timestamp: new Date().toISOString(),
          },
          {
            type: "ai",
            message:
              "LiveKit connection established. Waiting for the other person to join the call...",
            timestamp: new Date().toISOString(),
          },
          {
            type: "ai",
            message:
              "Note: The other person will only hear you, not the AI assistant.",
            timestamp: new Date().toISOString(),
          },
        ]);
      }

      toast({
        title: "Call Initiated",
        description: `Calling ${state.selectedContact.name} at ${
          state.selectedContact.phone
        }${
          state.selectedContact.type === "business" ? " with AI assistance" : ""
        }`,
      });
    } catch (error) {
      console.error("Error starting call:", error);
      set("loading", false);
      toast({
        title: "Error Starting Call",
        description:
          error instanceof Error
            ? error.message
            : "Failed to start the call. Please try again.",
        variant: "destructive",
      });
    }
  }, [
    state.selectedContact,
    state.callTranscript,
    set,
    toast,
    connectToLiveKit,
    startSpeechRecognition,
    fetchContacts,
  ]);

  const handleEndCall = useCallback(async () => {
    console.log("Ending call with ID:", state.currentCallId);

    // Show loading state
    set("loading", true);

    // Stop speech recognition
    console.log("Stopping speech recognition");
    stopSpeechRecognition();

    // Disconnect from LiveKit
    console.log("Disconnecting from LiveKit");
    disconnectFromLiveKit();

    // End the call on the server if we have a call ID
    if (state.currentCallId) {
      try {
        // End the call using the appropriate API endpoint
        // For Twilio calls, use the Twilio endpoint
        if (state.selectedContact) {
          console.log("Ending Twilio call on server:", state.currentCallId);
          const response = await apiClient.post(
            `/twilio/calls/${state.currentCallId}/end`
          );
          console.log("End call response:", response.data);
        } else {
          // For regular LiveKit calls, use the whisper endpoint
          console.log(
            "Ending regular LiveKit call on server:",
            state.currentCallId
          );
          const response = await whisperApi.endCall(state.currentCallId);
          console.log("End call response:", response);
        }
      } catch (error) {
        console.error("Error ending call on server:", error);
      }
    } else {
      console.warn("No call ID available to end the call on the server");
    }

    // Update state
    set("loading", false);
    set("activeCall", false);
    set("whisperEnabled", false);
    set("callTranscript", []);
    set("currentCallId", undefined);
    set("connectionDetails", undefined);

    toast({
      title: "Call Ended",
      description: "The call has been terminated.",
    });
  }, [
    state.currentCallId,
    state.selectedContact,
    set,
    toast,
    stopSpeechRecognition,
    disconnectFromLiveKit,
  ]);

  const handleSelectContact = useCallback((contact: Contact) => {
    set("selectedContact", contact);
  }, []);

  const handleSendMessage = useCallback(async () => {
    if (state.userMessage.trim()) {
      // Store the message before clearing the input
      const message = state.userMessage;
      set("userMessage", "");

      // Process the message for AI advice
      await processTranscriptionForAdvice(message);
    }
  }, [state.userMessage, set, processTranscriptionForAdvice]);

  const handleVoiceInput = useCallback(() => {
    if (!state.speechRecognitionActive) {
      startSpeechRecognition();
    } else {
      stopSpeechRecognition();
    }
  }, [
    state.speechRecognitionActive,
    startSpeechRecognition,
    stopSpeechRecognition,
  ]);

  // Fetch contacts when component mounts
  useEffect(() => {
    fetchContacts();
  }, [fetchContacts]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (roomRef.current) {
        roomRef.current.disconnect();
      }
      if (localTrackRef.current) {
        localTrackRef.current.stop();
      }
      if (speechRecognitionRef.current) {
        speechRecognitionRef.current.stop();
      }
    };
  }, []);

  return {
    state,
    set,
    handleStartCall,
    handleEndCall,
    handleSelectContact,
    handleSendMessage,
    handleVoiceInput,
    // Enhanced whisper functionality
    setWhisperMode,
    triggerAiAgent,
    getRoomState,
  };
}
