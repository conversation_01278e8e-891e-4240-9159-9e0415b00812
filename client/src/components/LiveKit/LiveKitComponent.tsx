import {
  AgentState,
  Live<PERSON>it<PERSON>oom,
  RoomAudio<PERSON><PERSON>er,
  useVoiceAssistant,
} from "@livekit/components-react";
import { MediaDeviceFailure } from "livekit-client";
import { useEffect, useMemo } from "react";
import { NoAgentNotification } from "./NoAgentNotification";
import { ConnectionDetails } from "@/types/connection";
import { TranscriptionTile } from "../Transcriptions/TranscriptionTile";

export default function LiveKitComponent({
  connectionDetails,
  updateConnectionDetails,
  agentState,
  setAgentState,
}: {
  connectionDetails: ConnectionDetails | undefined;
  updateConnectionDetails: (
    connectionDetails: ConnectionDetails | undefined
  ) => void;
  onConnectButtonClicked: () => void;
  agentState: AgentState;
  setAgentState: (state: AgentState) => void;
}) {
  console.log("agentState", agentState);
  return (
    <LiveKitRoom
      token={connectionDetails?.participantToken}
      serverUrl={connectionDetails?.serverUrl}
      connect={connectionDetails !== undefined}
      audio={true}
      video={false}
      onMediaDeviceFailure={onDeviceFailure}
      onDisconnected={() => {
        updateConnectionDetails(undefined);
      }}
      className="w-full h-full"
    >
      <VoiceAssistant onStateChange={setAgentState} />
      <RoomAudioRenderer />
      <NoAgentNotification state={agentState} />
    </LiveKitRoom>
  );
}

function VoiceAssistant(props: { onStateChange: (state: AgentState) => void }) {
  const { state, audioTrack } = useVoiceAssistant();
  console.log("state", state);
  console.log("audioTrack", audioTrack);
  const chatTileContent = useMemo(() => {
    if (audioTrack) {
      return <TranscriptionTile agentAudioTrack={audioTrack} />;
    }
    return <></>;
  }, [audioTrack]);
  useEffect(() => {
    props.onStateChange(state);
  }, [props, state]);
  return <div className={``}>{chatTileContent}</div>;
}

function onDeviceFailure(error?: MediaDeviceFailure) {
  console.error(error);
  alert(
    "Error acquiring camera or microphone permissions. Please make sure you grant the necessary permissions in your browser and reload the tab"
  );
}
