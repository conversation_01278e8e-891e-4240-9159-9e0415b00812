import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Contact } from "@/types/contact";
import { useContactContext } from "@/lib/contexts/ContactContext";

interface ContactMergeProps {
  isOpen: boolean;
  onClose: () => void;
  selectedContactIds: string[];
}

export function ContactMerge({
  isOpen,
  onClose,
  selectedContactIds,
}: ContactMergeProps) {
  const { contacts, mergeContacts } = useContactContext();
  const [primaryContactId, setPrimaryContactId] = useState<string>("");
  const [selectedContacts, setSelectedContacts] = useState<Contact[]>([]);
  // const [selectedValues, setSelectedValues] = useState<Record<string, string>>(
  //   {}
  // );

  // const fields = [
  //   { key: "name", label: "Name" },
  //   { key: "email", label: "Email" },
  //   { key: "phone", label: "Phone" },
  //   { key: "type", label: "Type" },
  //   { key: "transparencyLevel", label: "Transparency Level" },
  //   { key: "subcategory", label: "Subcategory" },
  // ];

  useEffect(() => {
    // Get the selected contacts from the context
    const selected = contacts.filter((contact) =>
      selectedContactIds.includes(contact.id)
    );
    setSelectedContacts(selected);

    // Set the first contact as the primary by default
    if (selected.length > 0 && !primaryContactId) {
      setPrimaryContactId(selected[0].id);
    }
  }, [contacts, selectedContactIds, primaryContactId]);

  const handleMerge = async () => {
    if (!primaryContactId) return;

    // Get secondary contact IDs (all selected contacts except the primary)
    const secondaryIds = selectedContactIds.filter(
      (id) => id !== primaryContactId
    );

    if (secondaryIds.length === 0) return;

    // Call the merge function from context
    await mergeContacts(primaryContactId, secondaryIds);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-gray-800 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle>Merge Contacts</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <p className="text-gray-300 mb-4">
            Select the primary contact. All other contacts will be merged into
            this one and then deleted.
          </p>

          <RadioGroup
            value={primaryContactId}
            onValueChange={setPrimaryContactId}
          >
            {selectedContacts.map((contact) => (
              <div
                key={contact.id}
                className="flex items-start space-x-3 p-3 rounded-md bg-gray-700 mb-2"
              >
                <RadioGroupItem
                  value={contact.id}
                  id={contact.id}
                  className="mt-1"
                />
                <div className="flex-1">
                  <Label
                    htmlFor={contact.id}
                    className="font-medium text-white block"
                  >
                    {contact.name}
                  </Label>
                  <div className="text-sm text-gray-400">
                    <p>{contact.email}</p>
                    <p>{contact.phone}</p>
                    <p className="mt-1">
                      Type: {contact.type} | Subcategory:{" "}
                      {contact.subcategory || "None"}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </RadioGroup>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={onClose}
            className="bg-gray-700 text-white hover:bg-gray-600"
          >
            Cancel
          </Button>
          <Button
            onClick={handleMerge}
            className="bg-teal-600 hover:bg-teal-700 text-white"
            disabled={!primaryContactId}
          >
            Merge Contacts
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
