import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Trash2, Search, Filter, RefreshCcw } from "lucide-react";
import { ContactTable } from "./ContactTable";
import { ContactForm } from "./ContactForm";
import { QuickView } from "./QuickView";
import { GoalManager } from "./GoalManager";
import { ContactFilters } from "./ContactFilters";
// import { ActivityReport } from "./ActivityReport";
import { ContactMerge } from "./ContactMerge";
import { ExportImport } from "./ExportImport";
import { Contact } from "@/types/contact";
import { useToast } from "@/components/ui/use-toast";
import { useContactContext } from "@/lib/contexts/ContactContext";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON>ead<PERSON>,
  <PERSON>ertD<PERSON>og<PERSON><PERSON><PERSON>,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface ContactListProps {
  initialShowModal?: boolean;
}

export function ContactList({ initialShowModal = false }: ContactListProps) {
  const { toast } = useToast();
  const {
    filteredContacts,
    selectedContacts,
    isLoading,
    showAddContactModal,
    editingContact,
    searchQuery,
    setShowAddContactModal,
    setEditingContact,
    fetchContacts,
    createContact,
    updateContact,
    deleteContact,
    bulkDeleteContacts,
    toggleSelectContact,
    selectAllContacts,
    setSearchQuery,
    setFilterType,
    setFilterSubcategory,
  } = useContactContext();

  const [showFilters, setShowFilters] = useState(false);
  const [showQuickViewModal, setShowQuickViewModal] = useState(false);
  const [showGoalManagerModal, setShowGoalManagerModal] = useState(false);
  const [showMergeDialog, setShowMergeDialog] = useState(false);
  const [currentContact, setCurrentContact] = useState<Contact | null>(null);

  useEffect(() => {
    if (initialShowModal) {
      setShowAddContactModal(true);
    }
  }, [initialShowModal, setShowAddContactModal]);

  // Fetch contacts on component mount
  useEffect(() => {
    fetchContacts();
  }, [fetchContacts]);

  // Handle contact form submission
  const handleSaveContact = async (contactData: Contact) => {
    if (contactData.id) {
      await updateContact(contactData.id, contactData);
    } else {
      await createContact(contactData as Omit<Contact, "id">);
    }
    setShowAddContactModal(false);
    setEditingContact(null);
  };

  // Handle contact edit
  const handleEditContact = (contact: Contact) => {
    setEditingContact(contact);
    setShowAddContactModal(true);
  };

  // Handle contact deletion
  const handleDeleteContact = async (contactId: string) => {
    await deleteContact(contactId);
  };

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedContacts.length > 0) {
      await bulkDeleteContacts(selectedContacts);
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchContacts();
  };

  const handleQuickView = (contact: Contact) => {
    setCurrentContact(contact);
    setShowQuickViewModal(true);
  };

  const handleManageGoals = (contact: Contact) => {
    setCurrentContact(contact);
    setShowGoalManagerModal(true);
  };

  // Handle select all contacts
  const handleSelectAll = () => {
    selectAllContacts();
  };

  const handleSaveGoals = (goals: any[]) => {
    if (currentContact) {
      const updatedContact = { ...currentContact, goals };
      updateContact(currentContact.id, updatedContact);
      setShowGoalManagerModal(false);
      toast({
        title: "Goals Updated",
        description: "Contact goals have been successfully updated.",
      });
    }
  };

  const handleImport = (importedContacts: Contact[]) => {
    // Handle import by creating each contact
    importedContacts.forEach((contact) => {
      const { id, ...contactData } = contact;
      createContact(contactData as Omit<Contact, "id">);
    });

    toast({
      title: "Contacts Imported",
      description: `${importedContacts.length} contacts have been imported.`,
    });
  };

  return (
    <div className="p-8 bg-gray-900 text-white">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-teal-400">Contact List</h1>
          <p className="text-gray-400">
            Manage your contacts and their AI interaction preferences
          </p>
        </div>
        <div className="flex space-x-2">
          <Button
            onClick={handleRefresh}
            variant="outline"
            className="bg-gray-700 hover:bg-gray-600 text-white"
          >
            <RefreshCcw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <ExportImport contacts={filteredContacts} onImport={handleImport} />
          <Button
            onClick={() => setShowAddContactModal(true)}
            className="bg-teal-600 hover:bg-teal-700 text-white"
          >
            <Plus className="mr-2 h-4 w-4" /> Add New Contact
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Search contacts..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-gray-700 text-white border-gray-600"
          />
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => setShowFilters(!showFilters)}
            variant="outline"
            className="bg-gray-700 hover:bg-gray-600 text-white"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          {selectedContacts.length > 0 && (
            <>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button
                    variant="outline"
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete ({selectedContacts.length})
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent className="bg-gray-800 text-white">
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                    <AlertDialogDescription className="text-gray-400">
                      This action cannot be undone. This will permanently delete{" "}
                      {selectedContacts.length} selected contacts.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel className="bg-gray-700 text-white hover:bg-gray-600">
                      Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleBulkDelete}
                      className="bg-red-600 text-white hover:bg-red-700"
                    >
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>

              {selectedContacts.length >= 2 && (
                <Button
                  variant="outline"
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                  onClick={() => setShowMergeDialog(true)}
                >
                  Merge ({selectedContacts.length})
                </Button>
              )}
            </>
          )}
        </div>
      </div>

      {showFilters && (
        <div className="mb-6">
          <ContactFilters
            searchTerm={searchQuery}
            onSearchChange={setSearchQuery}
            filterType={"All"}
            onFilterTypeChange={(type) => {
              const contactType = type === "All" ? null : type;
              setFilterType(contactType);
            }}
            filterTransparency={"all"}
            onFilterTransparencyChange={() => {}}
            filterSubcategory={"all"}
            onFilterSubcategoryChange={(subcategory) => {
              const contactSubcategory =
                subcategory === "all" ? null : subcategory;
              setFilterSubcategory(contactSubcategory);
            }}
            filterCampaign={"all"}
            onFilterCampaignChange={() => {}}
            campaigns={[]}
            showPersonalFilters={true}
          />
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-teal-500"></div>
        </div>
      ) : filteredContacts.length === 0 ? (
        <div className="text-center py-12 bg-gray-800 rounded-lg">
          <h3 className="text-xl font-medium text-gray-300">
            No contacts found
          </h3>
          <p className="text-gray-400 mt-2">
            {searchQuery
              ? "Try adjusting your search or filters"
              : "Get started by adding your first contact"}
          </p>
          {!searchQuery && (
            <Button
              onClick={() => setShowAddContactModal(true)}
              className="mt-4 bg-teal-600 hover:bg-teal-700 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Contact
            </Button>
          )}
        </div>
      ) : (
        <div className="bg-gray-800 rounded-lg p-6">
          <ContactTable
            contacts={filteredContacts}
            selectedContacts={selectedContacts}
            onEdit={handleEditContact}
            onDelete={handleDeleteContact}
            onQuickView={handleQuickView}
            onManageGoals={handleManageGoals}
            onSelect={toggleSelectContact}
            onSelectAll={handleSelectAll}
          />
        </div>
      )}

      <ContactForm
        isOpen={showAddContactModal}
        onClose={() => {
          setShowAddContactModal(false);
          setEditingContact(null);
        }}
        onSave={handleSaveContact}
        contact={editingContact}
      />

      {showQuickViewModal && currentContact && (
        <QuickView
          contact={currentContact}
          isOpen={showQuickViewModal}
          onClose={() => setShowQuickViewModal(false)}
        />
      )}

      {showGoalManagerModal && currentContact && (
        <GoalManager
          contact={currentContact}
          isOpen={showGoalManagerModal}
          onClose={() => setShowGoalManagerModal(false)}
          onSave={handleSaveGoals}
        />
      )}

      {showMergeDialog && (
        <ContactMerge
          isOpen={showMergeDialog}
          onClose={() => setShowMergeDialog(false)}
          selectedContactIds={selectedContacts}
        />
      )}
    </div>
  );
}
