import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Copy, Check, Mic } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { apiClient } from "@/services/api";

interface EmbedCodeGeneratorProps {
  assistantId: string;
}

export const EmbedCodeGenerator: React.FC<EmbedCodeGeneratorProps> = ({
  assistantId,
}) => {
  const { toast } = useToast();
  const [apiKey, setApiKey] = useState("");
  const [copied, setCopied] = useState(false);
  const [settings, setSettings] = useState({
    theme: "light",
    position: "right",
    accentColor: "#0ea5e9",
    welcomeMessage: "Hello! How can I help you today?",
    buttonText: "Chat with us",
    buttonIcon: "💬",
    embedType: "voice", // 'text' or 'voice'
    ballSize: "500px", // Size for the AbstractBall
  });

  // Fetch user's API key
  useEffect(() => {
    const fetchApiKey = async () => {
      try {
        const response = await apiClient.get("/user/api-keys");
        if (response.data && response.data.publicApiKey) {
          setApiKey(response.data.publicApiKey);
        }
      } catch (error) {
        console.error("Error fetching API key:", error);
        toast({
          title: "Error",
          description: "Failed to fetch your API key. Please try again.",
          variant: "destructive",
        });
      }
    };

    fetchApiKey();
  }, [toast]);

  const handleSettingChange = (
    key: string,
    value: string | number | boolean
  ) => {
    setSettings((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Generate the embed code
  const generateEmbedCode = () => {
    const apiUrl =
      import.meta.env.VITE_API_URL || "http://localhost:3030/api/v1";

    if (settings.embedType === "voice") {
      return generateVoiceEmbedCode(apiUrl);
    } else {
      return generateTextEmbedCode(apiUrl);
    }
  };

  // Generate code for voice assistant widget with simple button and visualizer
  const generateVoiceEmbedCode = (apiUrl: string) => {
    return `<!-- TAlkai247 Voice Assistant Widget -->
      <script>
        (function() {
          // Create widget container
          const container = document.createElement('div');
          container.id = 'talkai-voice-widget';
          container.style.position = 'fixed';
          container.style.bottom = '20px';
          container.style.right = '20px';
          container.style.zIndex = '9999';
          container.style.display = 'flex';
          container.style.flexDirection = 'column';
          container.style.alignItems = 'center';
          container.style.gap = '10px';
          document.body.appendChild(container);

          // Create visualizer container
          const visualizerContainer = document.createElement('div');
          visualizerContainer.id = 'visualizer-container';
          visualizerContainer.style.width = '100%';
          visualizerContainer.style.height = '20px';
          visualizerContainer.style.display = 'flex';
          visualizerContainer.style.alignItems = 'center';
          visualizerContainer.style.justifyContent = 'center';
          visualizerContainer.style.gap = '3px';
          visualizerContainer.style.display = 'none';
          container.appendChild(visualizerContainer);

          // Create visualizer bars
          for (let i = 0; i < 10; i++) {
            const bar = document.createElement('div');
            bar.className = 'visualizer-bar';
            bar.style.width = '4px';
            bar.style.height = '5px';
            bar.style.backgroundColor = '${settings.accentColor}';
            bar.style.borderRadius = '2px';
            bar.style.transition = 'height 0.1s ease';
            visualizerContainer.appendChild(bar);
          }

          // Create voice button
          const voiceButton = document.createElement('button');
          voiceButton.id = 'toggle-call-btn';
          voiceButton.style.padding = '0.75rem 1.5rem';
          voiceButton.style.backgroundColor = '${settings.accentColor}';
          voiceButton.style.color = 'white';
          voiceButton.style.border = 'none';
          voiceButton.style.borderRadius = '2rem';
          voiceButton.style.cursor = 'pointer';
          voiceButton.style.display = 'flex';
          voiceButton.style.alignItems = 'center';
          voiceButton.style.gap = '0.75rem';
          voiceButton.style.fontWeight = '500';
          voiceButton.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
          
          // Add mic icon SVG
          voiceButton.innerHTML = \`
            <svg id="call-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
              <line x1="12" y1="19" x2="12" y2="23"></line>
              <line x1="8" y1="23" x2="16" y2="23"></line>
            </svg>
            <span id="call-state">${settings.buttonText}</span>
          \`;
          
          container.appendChild(voiceButton);

          let isConnected = false;
          let livekitRoom = null;
          let connectionDetails = null;
          let agentState = 'disconnected';
          let volumeLevelValue = 0;

          // Update visualization based on agent state
          function updateVisualization(state, volume = 0) {
            agentState = state;
            volumeLevelValue = volume;
            
            const bars = document.querySelectorAll('.visualizer-bar');
            
            if (state === 'listening') {
              // Animate bars based on volume
              bars.forEach((bar, i) => {
                const height = 5 + (volume * 15) * (1 - Math.abs(i - 4.5) / 5);
                bar.style.height = \`\${height}px\`;
                bar.style.backgroundColor = '${settings.accentColor}';
              });
            } else if (state === 'speaking') {
              // Pulse animation for speaking
              bars.forEach((bar, i) => {
                const delay = i * 0.05;
                const height = 5 + (volume * 10) * (1 - Math.abs(i - 4.5) / 5);
                bar.style.height = \`\${height}px\`;
                bar.style.backgroundColor = '${settings.accentColor}';
              });
            } else if (state === 'connected') {
              // Small idle animation when connected
              bars.forEach((bar, i) => {
                const height = 5 + Math.sin(Date.now() / 500 + i) * 3;
                bar.style.height = \`\${height}px\`;
                bar.style.backgroundColor = '${settings.accentColor}';
              });
            } else {
              // Reset to minimal height when disconnected
              bars.forEach(bar => {
                bar.style.height = '5px';
                bar.style.backgroundColor = '${settings.accentColor}';
              });
            }
          }

          // Toggle voice widget and connection
          function toggleVoiceConnection() {
            if (!isConnected) {
              // Connect and show visualization
              visualizerContainer.style.display = 'flex';
              updateVisualization('connecting');
              connectToLiveKit();
              
              // Change button to phone-off icon
              document.getElementById('call-icon').innerHTML = \`
                <path d="M10.68 13.31a16 16 0 0 0 3.41 2.6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7 2 2 0 0 1 1.72 2v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.42 19.42 0 0 1-3.33-2.67m-2.67-3.34a19.79 19.79 0 0 1-3.07-8.63A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91"></path>
                <line x1="23" y1="1" x2="1" y2="23"></line>
              \`;
              document.getElementById('call-state').textContent = 'End Call';
            } else {
              // Disconnect and hide visualization
              visualizerContainer.style.display = 'none';
              disconnectFromLiveKit();
              
              // Change button back to mic icon
              document.getElementById('call-icon').innerHTML = \`
                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                <line x1="12" y1="19" x2="12" y2="23"></line>
                <line x1="8" y1="23" x2="16" y2="23"></line>
              \`;
              document.getElementById('call-state').textContent = "${settings.buttonText}";
            }
          }

          // Set up button click handler
          voiceButton.addEventListener('click', toggleVoiceConnection);

          // Load LiveKit script
          const loadLiveKitScript = () => {
            return new Promise((resolve, reject) => {
              if (window.LivekitClient) {
                resolve();
                return;
              }

              const script = document.createElement('script');
              script.src = 'https://unpkg.com/livekit-client@2.11.2/dist/livekit-client.umd.js';
              script.async = true;
              script.onload = () => resolve();
              script.onerror = () => reject(new Error('Failed to load LiveKit script'));
              document.body.appendChild(script);
            });
          };

          // Connect to LiveKit
          async function connectToLiveKit() {
            try {
              updateVisualization('connecting');
              
              const response = await fetch('${apiUrl}/public/livekit/${assistantId}', {
                method: 'GET',
                headers: {
                  'Content-Type': 'application/json',
                  'x-api-key': '${apiKey}',
                },
              });

              if (!response.ok) {
                updateVisualization('error');
                throw new Error('Failed to get LiveKit connection details');
              }

              const data = await response.json();
              if (data.success && data.data) {
                connectionDetails = data.data;

                try {
                  await loadLiveKitScript();
                  initializeLiveKit();
                } catch (error) {
                  console.error('Error loading LiveKit:', error);
                  updateVisualization('error');
                }
              } else {
                updateVisualization('error');
                throw new Error(data.error?.message || 'Unknown error');
              }
            } catch (error) {
              console.error('Error connecting to LiveKit:', error);
              updateVisualization('error');
            }
          }

          // Initialize LiveKit
          async function initializeLiveKit() {
            if (!connectionDetails || !window.LivekitClient) {
              console.error('LiveKit not loaded or connection details missing');
              updateVisualization('error');
              return;
            }

            try {
              const room = new window.LivekitClient.Room({
                adaptiveStream: true,
                dynacast: true,
              });

              // Handle connection state changes
              room.on(window.LivekitClient.RoomEvent.Connected, () => {
                isConnected = true;
                updateVisualization('connected');
              });

              room.on(window.LivekitClient.RoomEvent.Reconnecting, () => {
                updateVisualization('connecting');
              });

              // Handle audio track subscription
              room.on(window.LivekitClient.RoomEvent.TrackSubscribed, (track, publication, participant) => {
                if (track.kind === 'audio') {
                  const audioElement = document.createElement('audio');
                  audioElement.id = 'talkai-audio-element';
                  audioElement.autoplay = true;
                  document.body.appendChild(audioElement);
                  track.attach(audioElement);
                }
              });

              // Handle data messages
              room.on(window.LivekitClient.RoomEvent.DataReceived, (data, participant) => {
                try {
                  const decodedData = JSON.parse(new TextDecoder().decode(data));
                  
                  if (decodedData.type === 'state') {
                    updateVisualization(decodedData.state, decodedData.volumeLevel || 0);
                  }
                } catch (e) {
                  console.error('Error parsing data message:', e);
                }
              });

              // Handle volume levels for user speech
              room.on(window.LivekitClient.RoomEvent.ActiveSpeakersChanged, (speakers) => {
                if (speakers.length > 0) {
                  const volume = speakers[0].level;
                  if (agentState === 'listening') {
                    updateVisualization('listening', volume);
                  }
                }
              });

              room.on(window.LivekitClient.RoomEvent.Disconnected, () => {
                isConnected = false;
                updateVisualization('disconnected');
              });

              // Connect to room
              await room.connect(connectionDetails.serverUrl, connectionDetails.participantToken);
              livekitRoom = room;

              // Create and publish local audio track
              const localTrack = await window.LivekitClient.createLocalAudioTrack({
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true,
              });

              await room.localParticipant.publishTrack(localTrack);
            } catch (error) {
              console.error('Error initializing LiveKit:', error);
              updateVisualization('error');
            }
          }

          // Disconnect from LiveKit
          function disconnectFromLiveKit() {
            if (livekitRoom) {
              livekitRoom.disconnect();
              livekitRoom = null;
            }
            isConnected = false;
            connectionDetails = null;
            updateVisualization('disconnected');
            
            // Remove audio element if exists
            const audioElement = document.getElementById('talkai-audio-element');
            if (audioElement) {
              audioElement.remove();
            }
          }

          // Animation loop for visualizer
          function animateVisualizer() {
            if (agentState === 'connected') {
              updateVisualization('connected');
            }
            requestAnimationFrame(animateVisualizer);
          }

          // Start animation loop
          animateVisualizer();
        })();
      </script>`;
  };

  // Generate code for text chat widget (unchanged)
  const generateTextEmbedCode = (apiUrl: string) => {
    return `<!-- TAlkai247 Chat Widget -->
<script>
(function() {
  // Create widget container
  const container = document.createElement('div');
  container.id = 'talkai-chat-widget';
  document.body.appendChild(container);

  // Load styles
  const style = document.createElement('style');
  style.textContent = \`
    #talkai-chat-widget {
      position: fixed;
      z-index: 9999;
    }

    .talkai-chat-button {
      position: fixed;
      bottom: 20px;
      ${settings.position}: 20px;
      padding: 12px 20px;
      border-radius: 50px;
      border: none;
      background-color: ${settings.accentColor};
      color: white;
      font-weight: 600;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      z-index: 9999;
      transition: all 0.3s ease;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .talkai-chat-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    }

    .talkai-chat-container {
      position: fixed;
      bottom: 90px;
      ${settings.position}: 20px;
      width: 350px;
      height: 500px;
      border-radius: 12px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      background-color: #fff;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      z-index: 9999;
      border: 1px solid #e5e7eb;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }

    .talkai-chat-header {
      padding: 16px;
      background-color: ${settings.accentColor};
      color: white;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .talkai-chat-header h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
    }

    .talkai-close-button {
      background: none;
      border: none;
      color: white;
      cursor: pointer;
      font-size: 16px;
      padding: 0;
    }

    .talkai-messages-container {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
      display: flex;
      flex-direction: column;
      gap: 12px;
      background-color: ${settings.theme === "dark" ? "#1f2937" : "#ffffff"};
      color: ${settings.theme === "dark" ? "#f3f4f6" : "#333333"};
    }

    .talkai-message {
      max-width: 80%;
      padding: 10px 14px;
      border-radius: 18px;
      position: relative;
      word-wrap: break-word;
    }

    .talkai-user-message {
      align-self: flex-end;
      background-color: ${settings.theme === "dark" ? "#4b5563" : "#e5e7eb"};
      color: ${settings.theme === "dark" ? "#f3f4f6" : "#333333"};
      border-bottom-right-radius: 4px;
    }

    .talkai-assistant-message {
      align-self: flex-start;
      background-color: ${settings.theme === "dark" ? "#1f2937" : "#f3f4f6"};
      color: ${settings.theme === "dark" ? "#f3f4f6" : "#333333"};
      border-bottom-left-radius: 4px;
    }

    .talkai-message-timestamp {
      font-size: 10px;
      opacity: 0.7;
      margin-top: 4px;
      text-align: right;
    }

    .talkai-input-container {
      display: flex;
      padding: 12px;
      border-top: 1px solid #e5e7eb;
      background-color: ${settings.theme === "dark" ? "#1f2937" : "#ffffff"};
    }

    .talkai-input {
      flex: 1;
      padding: 10px 14px;
      border: 1px solid #e5e7eb;
      border-radius: 20px;
      background-color: ${settings.theme === "dark" ? "#374151" : "#f3f4f6"};
      color: ${settings.theme === "dark" ? "#f3f4f6" : "#333333"};
      resize: none;
      outline: none;
      font-family: inherit;
      font-size: 14px;
    }

    .talkai-send-button {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: none;
      background-color: ${settings.accentColor};
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      margin-left: 8px;
      transition: all 0.2s ease;
    }

    .talkai-send-button:hover {
      transform: scale(1.05);
    }

    .talkai-send-button:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .talkai-typing-indicator {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 6px 10px;
    }

    .talkai-typing-indicator span {
      height: 8px;
      width: 8px;
      margin: 0 2px;
      background-color: #bbb;
      border-radius: 50%;
      display: inline-block;
      opacity: 0.4;
    }

    .talkai-typing-indicator span:nth-child(1) {
      animation: pulse 1s infinite;
    }

    .talkai-typing-indicator span:nth-child(2) {
      animation: pulse 1s infinite 0.2s;
    }

    .talkai-typing-indicator span:nth-child(3) {
      animation: pulse 1s infinite 0.4s;
    }

    @keyframes pulse {
      0% {
        opacity: 0.4;
        transform: scale(1);
      }
      50% {
        opacity: 1;
        transform: scale(1.2);
      }
      100% {
        opacity: 0.4;
        transform: scale(1);
      }
    }
  \`;
  document.head.appendChild(style);

  // Chat state
  let isOpen = false;
  let messages = [];
  let conversationId = null;
  let assistantName = 'Assistant';

  // Create chat button
  const createChatButton = () => {
    const button = document.createElement('button');
    button.className = 'talkai-chat-button';
    button.innerHTML = '${settings.buttonIcon} ${settings.buttonText}';
    button.onclick = toggleChat;
    return button;
  };

  // Create chat container
  const createChatContainer = () => {
    const container = document.createElement('div');
    container.className = 'talkai-chat-container';

    // Header
    const header = document.createElement('div');
    header.className = 'talkai-chat-header';

    const title = document.createElement('h3');
    title.textContent = assistantName;

    const closeButton = document.createElement('button');
    closeButton.className = 'talkai-close-button';
    closeButton.textContent = '✕';
    closeButton.onclick = toggleChat;

    header.appendChild(title);
    header.appendChild(closeButton);

    // Messages container
    const messagesContainer = document.createElement('div');
    messagesContainer.className = 'talkai-messages-container';

    // Input container
    const inputContainer = document.createElement('div');
    inputContainer.className = 'talkai-input-container';

    const input = document.createElement('textarea');
    input.className = 'talkai-input';
    input.placeholder = 'Type your message...';
    input.rows = 1;
    input.onkeypress = (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
      }
    };

    const sendButton = document.createElement('button');
    sendButton.className = 'talkai-send-button';
    sendButton.innerHTML = '<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M22 2L11 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>';
    sendButton.onclick = sendMessage;

    inputContainer.appendChild(input);
    inputContainer.appendChild(sendButton);

    // Assemble container
    container.appendChild(header);
    container.appendChild(messagesContainer);
    container.appendChild(inputContainer);

    return container;
  };

  // Toggle chat visibility
  const toggleChat = () => {
    isOpen = !isOpen;
    render();

    // If opening for the first time, fetch assistant details
    if (isOpen && messages.length === 0) {
      fetchAssistantDetails();
    }
  };

  // Fetch assistant details
  const fetchAssistantDetails = async () => {
    try {
      const response = await fetch('${apiUrl}/public/assistant/${assistantId}', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': '${apiKey}',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch assistant details');
      }

      const data = await response.json();
      if (data.success && data.data) {
        assistantName = data.data.name;

        // Add first message if provided
        if (data.data.firstMessage) {
          addMessage('assistant', data.data.firstMessage);
        } else {
          // Use default welcome message
          addMessage('assistant', '${settings.welcomeMessage}');
        }

        render();
      }
    } catch (error) {
      console.error('Error fetching assistant details:', error);
      // Use default welcome message on error
      addMessage('assistant', '${settings.welcomeMessage}');
      render();
    }
  };

  // Send a message
  const sendMessage = async () => {
    const input = document.querySelector('.talkai-input');
    const message = input.value.trim();

    if (!message) return;

    // Clear input
    input.value = '';

    // Add user message
    addMessage('user', message);

    // Show typing indicator
    showTypingIndicator();

    try {
      const response = await fetch('${apiUrl}/public/chat/${assistantId}', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': '${apiKey}',
        },
        body: JSON.stringify({
          message: message,
          conversationId: conversationId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const data = await response.json();

      // Remove typing indicator
      hideTypingIndicator();

      if (data.success && data.data) {
        // Save conversation ID for future messages
        if (data.data.conversationId) {
          conversationId = data.data.conversationId;
        }

        // Add assistant response
        addMessage('assistant', data.data.message);
      } else {
        throw new Error(data.error?.message || 'Unknown error');
      }
    } catch (error) {
      console.error('Error sending message:', error);

      // Remove typing indicator
      hideTypingIndicator();

      // Add error message
      addMessage('assistant', 'Sorry, I encountered an error. Please try again later.');
    }
  };

  // Add a message to the chat
  const addMessage = (role, content) => {
    messages.push({
      role,
      content,
      timestamp: new Date(),
    });

    render();
    scrollToBottom();
  };

  // Show typing indicator
  const showTypingIndicator = () => {
    const messagesContainer = document.querySelector('.talkai-messages-container');

    // Create typing indicator
    const typingIndicator = document.createElement('div');
    typingIndicator.className = 'talkai-message talkai-assistant-message';
    typingIndicator.id = 'talkai-typing-indicator';

    const indicator = document.createElement('div');
    indicator.className = 'talkai-typing-indicator';
    indicator.innerHTML = '<span></span><span></span><span></span>';

    typingIndicator.appendChild(indicator);
    messagesContainer.appendChild(typingIndicator);

    scrollToBottom();
  };

  // Hide typing indicator
  const hideTypingIndicator = () => {
    const typingIndicator = document.getElementById('talkai-typing-indicator');
    if (typingIndicator) {
      typingIndicator.remove();
    }
  };

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    const messagesContainer = document.querySelector('.talkai-messages-container');
    if (messagesContainer) {
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
  };

  // Render the chat UI
  const render = () => {
    const widgetContainer = document.getElementById('talkai-chat-widget');
    widgetContainer.innerHTML = '';

    // Add chat button
    const chatButton = createChatButton();
    widgetContainer.appendChild(chatButton);

    // If chat is open, add chat container
    if (isOpen) {
      const chatContainer = createChatContainer();
      widgetContainer.appendChild(chatContainer);

      // Render messages
      const messagesContainer = document.querySelector('.talkai-messages-container');
      messagesContainer.innerHTML = '';

      messages.forEach(message => {
        const messageElement = document.createElement('div');
        messageElement.className = \`talkai-message talkai-\${message.role}-message\`;

        const contentElement = document.createElement('div');
        contentElement.className = 'talkai-message-content';
        contentElement.textContent = message.content;

        const timestampElement = document.createElement('div');
        timestampElement.className = 'talkai-message-timestamp';
        timestampElement.textContent = new Date(message.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

        messageElement.appendChild(contentElement);
        messageElement.appendChild(timestampElement);

        messagesContainer.appendChild(messageElement);
      });

      scrollToBottom();
    }
  };

  // Initial render
  render();
})();
</script>`;
  };

  const embedCode = generateEmbedCode();

  const copyToClipboard = () => {
    navigator.clipboard.writeText(embedCode);
    setCopied(true);
    toast({
      title: "Copied!",
      description: "Embed code copied to clipboard",
    });

    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Embed Your Assistant</CardTitle>
        <CardDescription>
          Generate code to embed your assistant on any website
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="settings">
          <TabsList className="mb-4">
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="code">Embed Code</TabsTrigger>
          </TabsList>

          <TabsContent value="settings">
            <div className="space-y-4">
              <div>
                <Label>Your API Key</Label>
                <div className="flex items-center mt-1.5">
                  <Input
                    value={apiKey}
                    readOnly
                    className="font-mono text-sm"
                  />
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  This API key will be used to authenticate requests from your
                  embedded chat widget.
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Theme</Label>
                  <Select
                    value={settings.theme}
                    onValueChange={(value) =>
                      handleSettingChange("theme", value)
                    }
                  >
                    <SelectTrigger className="mt-1.5">
                      <SelectValue placeholder="Select theme" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Light</SelectItem>
                      <SelectItem value="dark">Dark</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Position</Label>
                  <Select
                    value={settings.position}
                    onValueChange={(value) =>
                      handleSettingChange("position", value)
                    }
                  >
                    <SelectTrigger className="mt-1.5">
                      <SelectValue placeholder="Select position" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="right">Right</SelectItem>
                      <SelectItem value="left">Left</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label>Accent Color</Label>
                <div className="flex items-center gap-4 mt-1.5">
                  <div
                    className="w-10 h-10 rounded-full border"
                    style={{ backgroundColor: settings.accentColor }}
                  />
                  <Input
                    type="text"
                    value={settings.accentColor}
                    onChange={(e) =>
                      handleSettingChange("accentColor", e.target.value)
                    }
                    className="font-mono"
                  />
                </div>
              </div>

              <div>
                <Label>Welcome Message</Label>
                <Textarea
                  value={settings.welcomeMessage}
                  onChange={(e) =>
                    handleSettingChange("welcomeMessage", e.target.value)
                  }
                  className="mt-1.5"
                  rows={2}
                />
              </div>

              <div>
                <Label>Embed Type</Label>
                <div className="flex gap-4 mt-2">
                  {/* <Button
                    type="button"
                    variant={
                      settings.embedType === "text" ? "default" : "outline"
                    }
                    className="flex-1 flex items-center justify-center gap-2"
                    onClick={() => handleSettingChange("embedType", "text")}
                  >
                    <MessageSquare className="h-4 w-4" />
                    Text Chat
                  </Button> */}

                  <Button
                    type="button"
                    variant={
                      settings.embedType === "voice" ? "default" : "outline"
                    }
                    className="flex-1 flex items-center justify-center gap-2"
                    onClick={() => handleSettingChange("embedType", "voice")}
                  >
                    <Mic className="h-4 w-4" />
                    Voice Assistant
                  </Button>
                </div>
              </div>

              {settings.embedType === "voice" && (
                <div>
                  <Label>Ball Size</Label>
                  <Input
                    value={settings.ballSize}
                    onChange={(e) =>
                      handleSettingChange("ballSize", e.target.value)
                    }
                    className="mt-1.5"
                    placeholder="e.g., 500px"
                  />
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Button Text</Label>
                  <Input
                    value={settings.buttonText}
                    onChange={(e) =>
                      handleSettingChange("buttonText", e.target.value)
                    }
                    className="mt-1.5"
                  />
                </div>

                <div>
                  <Label>Button Icon</Label>
                  <Input
                    value={settings.buttonIcon}
                    onChange={(e) =>
                      handleSettingChange("buttonIcon", e.target.value)
                    }
                    className="mt-1.5"
                  />
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="code">
            <div className="space-y-4">
              <div>
                <Label>Copy this code and paste it into your website</Label>
                <div className="relative mt-1.5">
                  <Textarea
                    value={embedCode}
                    readOnly
                    className="font-mono text-xs h-[400px] overflow-auto"
                  />
                  <Button
                    size="sm"
                    variant="outline"
                    className="absolute top-2 right-2"
                    onClick={copyToClipboard}
                  >
                    {copied ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              <div className="bg-teal-600 p-4 rounded-md">
                <h3 className="font-medium mb-2">Instructions</h3>
                <ol className="list-decimal list-inside space-y-2 text-sm">
                  <li>Copy the code above</li>
                  <li>
                    Paste it just before the closing{" "}
                    <code className="bg-gray-200 text-gray-600 px-1 rounded">
                      &lt;/body&gt;
                    </code>{" "}
                    tag of your website
                  </li>
                  <li>
                    The{" "}
                    {settings.embedType === "voice"
                      ? "voice assistant"
                      : "chat widget"}{" "}
                    will automatically appear on your website
                  </li>
                  <li>
                    You can customize the appearance by changing the settings in
                    the "Settings" tab
                  </li>
                </ol>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default EmbedCodeGenerator;
