import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { User } from "@/types/schema";

interface ProfileInformationInterface {
  isEditing: boolean;
  editedUser: User;
  setEditedUser: React.Dispatch<React.SetStateAction<User>>;
  user: User;
  handleSaveChanges: () => void;
  setIsEditing: React.Dispatch<React.SetStateAction<boolean>>;
}

const ProfileInformation = ({
  isEditing,
  editedUser,
  setEditedUser,
  user,
  handleSaveChanges,
  setIsEditing,
}: ProfileInformationInterface) => {
  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader>
        <CardTitle className="text-white">Profile Information</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <Label htmlFor="name" className="text-white">
              Name
            </Label>
            <Input
              id="name"
              value={isEditing ? editedUser.name : user.name}
              onChange={(e) =>
                setEditedUser({ ...editedUser, name: e.target.value })
              }
              readOnly={!isEditing}
              className="bg-gray-700 text-white border-gray-600"
            />
          </div>
          <div>
            <Label htmlFor="email" className="text-white">
              Email
            </Label>
            <Input
              id="email"
              value={isEditing ? editedUser.email : user.email}
              onChange={(e) =>
                setEditedUser({ ...editedUser, email: e.target.value })
              }
              readOnly={!isEditing}
              className="bg-gray-700 text-white border-gray-600"
            />
          </div>
          <div>
            <Label htmlFor="company" className="text-white">
              Company
            </Label>
            <Input
              id="company"
              value={isEditing ? editedUser.company : user.company}
              onChange={(e) =>
                setEditedUser({ ...editedUser, company: e.target.value })
              }
              readOnly={!isEditing}
              className="bg-gray-700 text-white border-gray-600"
            />
          </div>
          <div>
            <Label htmlFor="role" className="text-white">
              Role
            </Label>
            <Input
              id="role"
              value={user.role}
              readOnly
              className="bg-gray-700 text-white border-gray-600"
            />
          </div>
          <div>
            <Label htmlFor="phoneNumber" className="text-white">
              Phone Number
            </Label>
            <Input
              id="phoneNumber"
              value={isEditing ? editedUser.phoneNumber : user.phoneNumber}
              onChange={(e) =>
                setEditedUser({ ...editedUser, phoneNumber: e.target.value })
              }
              readOnly={!isEditing}
              className="bg-gray-700 text-white border-gray-600"
            />
          </div>
          {isEditing ? (
            <div className="flex space-x-2">
              <Button
                onClick={handleSaveChanges}
                className="bg-teal-600 hover:bg-teal-700 text-white"
              >
                Save Changes
              </Button>
              <Button
                onClick={() => setIsEditing(false)}
                variant="outline"
                className="bg-gray-700 text-white border-gray-600"
              >
                Cancel
              </Button>
            </div>
          ) : (
            <Button
              onClick={() => setIsEditing(true)}
              className="bg-teal-600 hover:bg-teal-700 text-white"
            >
              Edit Profile
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ProfileInformation;
