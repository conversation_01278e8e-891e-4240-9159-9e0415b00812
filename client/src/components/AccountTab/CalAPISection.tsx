import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
// import { User } from "@/types/schema";
import { Eye, EyeOff } from "lucide-react";
import { Link } from "react-router-dom";
// import { useToast } from "@/components/ui/use-toast";

interface CalAPIAddEditInterface {
  isCalEditing: boolean;
  editedUser: any;
  setEditedUser: any;
  user: any;
  handleCalSaveChanges: () => void;
  setIsCalEditing: React.Dispatch<React.SetStateAction<boolean>>;
  showCalAPIKey: boolean;
  setShowCalAPIKey: React.Dispatch<React.SetStateAction<boolean>>;
}

const CalAPISection = ({
  isCalEditing,
  editedUser,
  setEditedUser,
  user,
  handleCalSaveChanges,
  setIsCalEditing,
  showCalAPIKey,
  setShowCalAPIKey,
}: CalAPIAddEditInterface) => {
  return (
    <Card className="bg-gray-800 border-gray-700">
      <CardHeader>
        <CardTitle className="text-white">Cal API Key</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <Label htmlFor="calEventTypeId" className="text-white">
              Cal Event Type Id{" || "}
              <span className="text-blue-500">
                <Link to="https://app.cal.com/event-types">
                  How to get Event Types?
                </Link>
              </span>
            </Label>
            <Input
              id="calEventTypeId"
              value={
                isCalEditing
                  ? editedUser.calEventTypeId || ""
                  : user?.calEventTypeId || ""
              }
              onChange={(e) =>
                setEditedUser((prev: any) => ({
                  ...prev,
                  calEventTypeId: e.target.value,
                }))
              }
              readOnly={!isCalEditing}
              className="bg-gray-700 text-white border-gray-600"
            />
          </div>
          <div>
            <Label htmlFor="calApiKey" className="text-white">
              API Key {" || "}
              <span className="text-blue-500">
                <Link to="https://app.cal.com/settings/developer/api-keys">
                  How to get API Key?
                </Link>
              </span>
            </Label>
            <div className="flex">
              <Input
                id="calApiKey"
                type={showCalAPIKey ? "text" : "password"}
                value={
                  isCalEditing
                    ? editedUser.calApiKey || ""
                    : user?.calApiKey || ""
                }
                readOnly={!isCalEditing}
                onChange={(e) =>
                  setEditedUser((prev: any) => ({
                    ...prev,
                    calApiKey: e.target.value,
                  }))
                }
                className="bg-gray-700 text-white border-gray-600 flex-grow"
              />
              <Button
                onClick={() => setShowCalAPIKey(!showCalAPIKey)}
                className="ml-2 bg-gray-700 hover:bg-gray-600 text-white"
              >
                {showCalAPIKey ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
          {isCalEditing ? (
            <div className="flex space-x-2">
              <Button
                onClick={handleCalSaveChanges}
                className="bg-teal-600 hover:bg-teal-700 text-white"
              >
                Save Changes
              </Button>
              <Button
                onClick={() => setIsCalEditing(false)}
                variant="outline"
                className="bg-gray-700 text-white border-gray-600"
              >
                Cancel
              </Button>
            </div>
          ) : (
            <Button
              onClick={() => setIsCalEditing(true)}
              className="bg-teal-600 hover:bg-teal-700 text-white"
            >
              Edit Cal API
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default CalAPISection;
// import { Button } from "@/components/ui/button";
// import { Input } from "@/components/ui/input";
// import { Label } from "@/components/ui/label";
// import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
// import { User } from "@/types/schema";
// import { Eye, EyeOff } from "lucide-react";
// interface CalAPIAddEditInterface {
//   isCalEditing: boolean;
//   editedUser: User;
//   setEditedUser: React.Dispatch<React.SetStateAction<User>>;
//   user: User;
//   handleCalSaveChanges: () => void;
//   setIsCalEditing: React.Dispatch<React.SetStateAction<boolean>>;
//   showCalAPIKey: boolean;
//   setShowCalAPIKey: React.Dispatch<React.SetStateAction<boolean>>;
// }

// const CalAPISection = ({
//   isCalEditing,
//   editedUser,
//   setEditedUser,
//   user,
//   handleCalSaveChanges,
//   setIsCalEditing,
//   showCalAPIKey,
//   setShowCalAPIKey,
// }: CalAPIAddEditInterface) => {
//   return (
//     <Card className="bg-gray-800 border-gray-700">
//       <CardHeader>
//         <CardTitle className="text-white">Cal API Key</CardTitle>
//       </CardHeader>
//       <CardContent>
//         <div className="space-y-4">
//           <div>
//             <Label htmlFor="name" className="text-white">
//               API Name
//             </Label>
//             <Input
//               id="calEventTypeId"
//               value={isCalEditing ? editedUser?.calEventTypeId : user?.calEventTypeId}
//               onChange={(e) =>
//                 setEditedUser({ ...editedUser, calEventTypeId: e.target.value })
//               }
//               readOnly={!isCalEditing}
//               className="bg-gray-700 text-white border-gray-600"
//             />
//           </div>
//           <div>
//             <Label htmlFor="calApiKey" className="text-white">
//               API Key
//             </Label>
//             <div className="flex">
//               <Input
//                 id="calApiKey"
//                 type={showCalAPIKey ? "text" : "password"}
//                 value={isCalEditing ? editedUser?.calApiKey : user?.calApiKey}
//                 readOnly={!isCalEditing}
//                 onChange={(e) =>
//                   setEditedUser({ ...editedUser, calApiKey: e.target.value })
//                 }
//                 className="bg-gray-700 text-white border-gray-600 flex-grow"
//               />
//               <Button
//                 onClick={() => setShowCalAPIKey(!showCalAPIKey)}
//                 className="ml-2 bg-gray-700 hover:bg-gray-600 text-white"
//               >
//                 {showCalAPIKey ? (
//                   <EyeOff className="h-4 w-4" />
//                 ) : (
//                   <Eye className="h-4 w-4" />
//                 )}
//               </Button>
//             </div>
//           </div>
//           {isCalEditing ? (
//             <div className="flex space-x-2">
//               <Button
//                 onClick={handleCalSaveChanges}
//                 className="bg-teal-600 hover:bg-teal-700 text-white"
//               >
//                 Save Changes
//               </Button>
//               <Button
//                 onClick={() => setIsCalEditing(false)}
//                 variant="outline"
//                 className="bg-gray-700 text-white border-gray-600"
//               >
//                 Cancel
//               </Button>
//             </div>
//           ) : (
//             <Button
//               onClick={() => setIsCalEditing(true)}
//               className="bg-teal-600 hover:bg-teal-700 text-white"
//             >
//               Edit Cal API
//             </Button>
//           )}
//         </div>
//       </CardContent>
//     </Card>
//   );
// };

// export default CalAPISection;
