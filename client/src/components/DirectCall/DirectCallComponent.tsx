import { useState, useEffect, useCallback, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { PhoneIcon, X, Loader2, User, Volume2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Contact } from "@/components/Whisper/types";
import { contactApi } from "@/services/contactApi";
import { directCallApi } from "@/services/directCallApi";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Slider } from "@/components/ui/slider";
import { Device, Call } from "@twilio/voice-sdk";
import {
  Room,
  RoomEvent,
  RemoteParticipant,
  Track,
  RemoteTrackPublication,
} from "livekit-client";
import { apiClient } from "@/services/api";

export function DirectCallComponent() {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isCallActive, setIsCallActive] = useState(false);
  const [callStatus, setCallStatus] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState("");
  const [callSidOut, setCallSidOut] = useState("");
  const [volume, setVolume] = useState(50);
  // Add to your component state
  const [whisperSuggestion, setWhisperSuggestion] = useState<string>("");
  const [liveKitRoom, setLiveKitRoom] = useState<Room | null>(null);
  const { toast } = useToast();

  // Refs for Twilio Device
  const deviceRef = useRef<Device | null>(null);
  const callRef = useRef<Call | null>(null);
  console.log("callRef.current!.parameters.CallSid", callRef);

  // Initialize Twilio Device
  const initializeTwilioDevice = useCallback(async () => {
    try {
      // Get a token from the server
      const response = await directCallApi.getClientToken();

      if (!response.success || !response.data) {
        throw new Error(
          response.error?.message || "Failed to get Twilio token"
        );
      }

      const { token, identity } = response.data;
      console.log(`Got token for identity: ${identity}`);

      // Initialize the Twilio Device with the modern SDK
      // Setup the device with the token
      deviceRef.current = new Device(token);

      // Setup event listeners
      deviceRef.current.on("registered", () => {
        console.log("Twilio Device is ready for calls");
        // Request microphone permission early
        navigator.mediaDevices
          .getUserMedia({ audio: true })
          .then(() => console.log("Microphone permission granted"))
          .catch((err) => {
            console.error("Microphone permission denied:", err);
            toast({
              title: "Microphone Access Required",
              description: "Please allow microphone access to make calls",
              variant: "destructive",
            });
          });
      });

      deviceRef.current.on("error", (error) => {
        console.error("Twilio Device error:", error);
        toast({
          title: "Connection Error",
          description:
            error.message || "An error occurred with the call connection",
          variant: "destructive",
        });
      });

      deviceRef.current.on("incoming", (call) => {
        console.log("Incoming call from:", call.parameters.From);

        // Store the call reference
        callRef.current = call;

        // Auto-accept the call
        call.accept();

        // Update UI
        setIsCallActive(true);
        setCallStatus("Call connected");

        // If we have a selected contact, update the UI with their info
        if (selectedContact) {
          toast({
            title: "Call Connected",
            description: `You are now connected to ${selectedContact.name}`,
          });
        } else {
          toast({
            title: "Call Connected",
            description: "You are now connected to your call",
          });
        }

        // Setup call event listeners
        setupCallEventListeners(call);
      });

      deviceRef.current.on("connect", (call) => {
        console.log("Call connected:", call);
      });

      deviceRef.current.on("disconnect", (call) => {
        console.log("Call disconnected:", call);
      });

      console.log("Twilio Device initialized successfully");
    } catch (error) {
      console.error("Error initializing Twilio Device:", error);
      toast({
        title: "Connection Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to initialize call system",
        variant: "destructive",
      });
    }
  }, [toast, selectedContact]);

  // Setup event listeners for a call
  const setupCallEventListeners = (call: Call) => {
    call.on("accept", () => {
      console.log("Call accepted");
      setCallStatus("Call connected");

      // Set the volume when the call is accepted
      setTimeout(() => {
        // In the new SDK, audio elements are created automatically
        const audioElements = document.getElementsByTagName("audio");
        for (let i = 0; i < audioElements.length; i++) {
          audioElements[i].volume = volume / 100;
        }
      }, 500); // Small delay to ensure audio elements are created
    });

    call.on("disconnect", () => {
      console.log("Call disconnected");
      setIsCallActive(false);
      setCallStatus("");
      callRef.current = null;

      toast({
        title: "Call Ended",
        description: "The call has been disconnected.",
      });

      // If we have a selected contact, update the UI
      if (selectedContact) {
        setCallStatus(`Call with ${selectedContact.name} ended`);
        setTimeout(() => setCallStatus(""), 3000);
      }
    });

    call.on("error", (error) => {
      console.error("Call error:", error);
      toast({
        title: "Call Error",
        description: error.message || "An error occurred during the call",
        variant: "destructive",
      });
    });

    // Monitor audio quality
    call.on("volume", (_inputVolume, outputVolume) => {
      if (outputVolume <= 0) {
        console.log("No audio detected from the other party");
      }
    });

    // Set the volume for any audio elements
    const audioElements = document.getElementsByTagName("audio");
    for (let i = 0; i < audioElements.length; i++) {
      audioElements[i].volume = volume / 100;
    }
  };

  // Fetch contacts
  const fetchContacts = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await contactApi.getAll();

      if (response.success && response.data) {
        // Map the contacts to our local interface
        const mappedContacts = response.data.map((contact) => ({
          id: contact.id,
          name: contact.name,
          phone: contact.phone,
          email: contact.email,
          type: contact.type.toLowerCase() as "business" | "personal",
          transparencyLevel: contact.transparencyLevel as
            | "FULL"
            | "PARTIAL"
            | "NONE",
          subcategory: contact.subcategory,
          customSubcategory: contact.customSubcategory,
          campaignId: contact.campaignId,
          tags: contact.tags,
          notes: contact.notes,
        }));

        setContacts(mappedContacts);
      } else {
        throw new Error(response.error?.message || "Failed to fetch contacts");
      }
    } catch (error) {
      console.error("Error fetching contacts:", error);
      toast({
        title: "Error",
        description: "Failed to fetch contacts. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  // Start a direct call
  // Start a direct call
  const startCall = async () => {
    if (!selectedContact) {
      toast({
        title: "No Contact Selected",
        description: "Please select a contact before starting a call.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      setCallStatus("Initiating call...");

      // Check if Twilio Device is ready
      if (!deviceRef.current) {
        await initializeTwilioDevice();
        if (!deviceRef.current) {
          throw new Error("Failed to initialize Twilio Device");
        }
      }

      // Make a direct outbound call from the browser to the phone number
      const params = {
        // Pass the contact ID to the server so it can track the call
        contactId: selectedContact.id,
        // The To parameter will be used by the Twilio Application to route the call
        To: selectedContact.phone,
      };

      console.log("Making outbound call with params:", params);

      // Make the call directly from the browser using the Twilio Device
      const call = await deviceRef.current.connect({ params });

      // Store the call reference
      callRef.current = call;
      console.log("callRef.current", callRef.current);

      // Setup call event listeners
      setupCallEventListeners(call);

      setIsCallActive(true);
      setCallStatus(`Calling ${selectedContact.name}...`);

      toast({
        title: "Call Initiated",
        description: `Calling ${selectedContact.name} at ${selectedContact.phone}`,
      });

      // Also notify the server about the call for tracking purposes
      try {
        // Wait for the CallSid to become available
        // In some cases, CallSid might not be immediately available after call initiation
        setTimeout(async () => {
          // Log the entire call object to inspect its structure
          console.log("Call object:", call);

          // Access CallSid correctly from the parameters
          const callSid = call.parameters?.CallSid;
          console.log("CallSid:", callSid);
          setCallSidOut(callSid);
          if (callSid) {
            const response = await directCallApi.initiateCall(
              selectedContact.id,
              callSid
            );
            console.log("Server notified about call:", response);
          } else {
            console.warn("CallSid not available yet");
            // You might want to retry after another delay if needed
          }
        }, 1000); // Add a 1 second delay to ensure CallSid is available
      } catch (notifyError) {
        console.error("Error notifying server about call:", notifyError);
        // Continue with the call even if server notification fails
      }
    } catch (error) {
      console.error("Error starting call:", error);
      setCallStatus("Call failed");
      toast({
        title: "Call Failed",
        description:
          error instanceof Error ? error.message : "Failed to start the call",
        variant: "destructive",
      });
      setIsCallActive(false);
    } finally {
      setIsLoading(false);
    }
  };

  // End the call
  const endCall = async () => {
    try {
      setIsLoading(true);
      setCallStatus("Ending call...");

      console.log("Ending call, current call reference:", callRef.current);

      // First, try to end the call via the API
      try {
        const response = await directCallApi.endCall();
        console.log("API call to end call response:", response);
      } catch (apiError) {
        console.error("API error when ending call:", apiError);
        // Continue even if the API call fails
      }

      // Then, try to disconnect the Twilio call if it exists
      if (callRef.current) {
        try {
          console.log("Disconnecting active call...");
          callRef.current.disconnect();
        } catch (disconnectError) {
          console.error("Error disconnecting call:", disconnectError);
        }
        callRef.current = null;
      } else {
        console.log("No active call reference found to disconnect");

        // If no call reference exists but the device is active, try to disconnect all calls
        if (deviceRef.current) {
          try {
            console.log("Attempting to disconnect all calls on the device");
            // In the new SDK, we use device.disconnectAll() to disconnect all calls
            deviceRef.current.disconnectAll();
          } catch (disconnectAllError) {
            console.error("Error disconnecting all calls:", disconnectAllError);
          }
        }
      }

      // Force update the UI state regardless of call state
      setIsCallActive(false);
      setCallStatus("");

      toast({
        title: "Call Ended",
        description: "The call has been terminated.",
      });

      // Reinitialize the Twilio device to ensure a clean state for the next call
      setTimeout(() => {
        if (deviceRef.current) {
          try {
            console.log("Reinitializing Twilio device for clean state");
            initializeTwilioDevice();
          } catch (reinitError) {
            console.error("Error reinitializing Twilio device:", reinitError);
          }
        }
      }, 1000);
    } catch (error) {
      console.error("Error ending call:", error);
      toast({
        title: "Error",
        description: "Failed to end the call properly.",
        variant: "destructive",
      });
      // Force the UI to show call as ended even if there was an error
      setIsCallActive(false);
      setCallStatus("");
    } finally {
      setIsLoading(false);
    }
  };
  // Add this effect to join the LiveKit room when call starts
  useEffect(() => {
    if (isCallActive && callRef.current) {
      const joinLiveKitRoom = async () => {
        try {
          console.log("callSidOut", callSidOut);
          const response = await directCallApi.getLiveKitInfo(callSidOut);

          if (response.success && response.data) {
            const { url, room, token } = response.data.liveKit;

            const newRoom = new Room();
            await newRoom.connect(url, token, {
              // Automatically subscribe to all tracks
              autoSubscribe: true,
            });

            // Setup event listeners
            newRoom
              .on(
                RoomEvent.TrackSubscribed,
                (track, publication, participant) => {
                  if (
                    track.kind === Track.Kind.Audio &&
                    participant.identity.startsWith("customer_")
                  ) {
                    processAudioTrack(publication);
                  }
                }
              )
              .on(
                RoomEvent.DataReceived,
                (payload: Uint8Array, participant?: RemoteParticipant) => {
                  console.log("participant", participant);
                  try {
                    const decoder = new TextDecoder();
                    const text = decoder.decode(payload);
                    const data = JSON.parse(text);

                    if (data.type === "suggestion") {
                      setWhisperSuggestion(data.message);
                    }
                  } catch (error) {
                    console.error("Error processing data message:", error);
                  }
                }
              );

            setLiveKitRoom(newRoom);

            // Notify the whisper agent to start monitoring
            await apiClient.post("/whisper-agent/start", {
              roomName: room,
            });
          }
        } catch (error) {
          console.error("Error joining LiveKit room:", error);
        }
      };

      joinLiveKitRoom();
    }

    return () => {
      if (liveKitRoom) {
        liveKitRoom.disconnect();
      }
    };
  }, [isCallActive]);

  const processAudioTrack = async (publication: RemoteTrackPublication) => {
    if (publication.track) {
      const track = publication.track;

      if (track.kind === Track.Kind.Audio) {
        try {
          // Create a media stream source
          const audioContext = new AudioContext();
          const mediaStream = new MediaStream();
          mediaStream.addTrack(track.mediaStreamTrack);

          const source = audioContext.createMediaStreamSource(mediaStream);
          const processor = audioContext.createScriptProcessor(4096, 1, 1);

          source.connect(processor);
          processor.connect(audioContext.destination);

          processor.onaudioprocess = async (e) => {
            // Here you would send the audio data to your backend for processing
            // For example:
            const audioData = e.inputBuffer.getChannelData(0);
            console.log("audioData", audioData);
            //await sendAudioToBackend(audioData);
          };
        } catch (error) {
          console.error("Error processing audio track:", error);
        }
      }
    }
  };
  // Handle volume change
  const handleVolumeChange = (value: number) => {
    setVolume(value);

    // Update volume of any active call by finding audio elements
    // In the new SDK, we don't access mediaStream directly
    const audioElements = document.getElementsByTagName("audio");
    for (let i = 0; i < audioElements.length; i++) {
      audioElements[i].volume = value / 100;
    }
  };

  // Filter contacts based on search query
  const filteredContacts = contacts.filter(
    (contact) =>
      contact.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      contact.phone.includes(searchQuery)
  );

  // Initialize Twilio Device on component mount
  useEffect(() => {
    initializeTwilioDevice();

    // Cleanup on unmount
    return () => {
      if (deviceRef.current) {
        // In the new SDK, we use device.destroy() to clean up
        deviceRef.current.destroy();
      }
    };
  }, [initializeTwilioDevice]);

  // Fetch contacts on component mount
  useEffect(() => {
    fetchContacts();
  }, [fetchContacts]);

  return (
    <div className="container mx-auto p-6 bg-gray-900 text-gray-100">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-teal-400">Direct Call</h1>
        <p className="text-gray-400">
          Make direct calls to your contacts from your web browser
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Contact Selection */}
        <div className="col-span-1">
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">Select Contact</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Input
                  placeholder="Search contacts..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="bg-gray-700 border-gray-600 text-white"
                />
                <ScrollArea className="h-[400px]">
                  <div className="space-y-2">
                    {isLoading ? (
                      <div className="flex justify-center py-4">
                        <Loader2 className="h-6 w-6 animate-spin text-teal-400" />
                      </div>
                    ) : filteredContacts.length === 0 ? (
                      <div className="text-center py-4 text-gray-400">
                        No contacts found
                      </div>
                    ) : (
                      filteredContacts.map((contact) => (
                        <div
                          key={contact.id}
                          className={`p-3 rounded-md cursor-pointer ${
                            selectedContact?.id === contact.id
                              ? "bg-teal-600"
                              : "bg-gray-700 hover:bg-gray-600"
                          }`}
                          onClick={() => setSelectedContact(contact)}
                        >
                          <div className="font-medium">{contact.name}</div>
                          <div className="text-sm text-gray-300">
                            {contact.phone}
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                </ScrollArea>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Call Interface */}
        <div className="col-span-1 md:col-span-2">
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white flex justify-between items-center">
                {isCallActive ? "Active Call" : "Start New Call"}
                {selectedContact && (
                  <div className="flex items-center text-sm text-gray-400">
                    <User className="h-4 w-4 mr-2" />
                    {selectedContact.name} ({selectedContact.phone})
                  </div>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex space-x-4">
                  <Button
                    onClick={startCall}
                    disabled={isCallActive || !selectedContact || isLoading}
                    className="bg-teal-600 hover:bg-teal-700"
                  >
                    {isLoading && !isCallActive ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Calling...
                      </>
                    ) : (
                      <>
                        <PhoneIcon className="mr-2 h-4 w-4" />
                        Start Call
                      </>
                    )}
                  </Button>
                  <Button
                    onClick={endCall}
                    disabled={!isCallActive || isLoading}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {isLoading && isCallActive ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Ending...
                      </>
                    ) : (
                      <>
                        <X className="mr-2 h-4 w-4" />
                        End Call
                      </>
                    )}
                  </Button>
                </div>

                {!selectedContact && !isCallActive && (
                  <div className="text-center py-8 text-gray-400">
                    Please select a contact to start a call
                  </div>
                )}

                {isCallActive && (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between bg-gray-700 p-3 rounded-md">
                      <div className="flex items-center">
                        <div className="h-3 w-3 rounded-full mr-2 bg-green-500"></div>
                        <span className="text-gray-300">Call Active</span>
                      </div>
                      <div className="text-gray-300">{callStatus}</div>
                    </div>
                    <div className="bg-gray-700 p-6 rounded-md text-center">
                      <div className="text-2xl font-bold text-teal-400 mb-2">
                        {selectedContact?.name}
                      </div>
                      <div className="text-gray-300 mb-4">
                        {selectedContact?.phone}
                      </div>
                      <div className="text-gray-400">
                        Call connected. You can speak now.
                      </div>
                      {isCallActive && whisperSuggestion && (
                        <div className="mt-4 p-3 bg-blue-900 rounded-md border border-blue-700">
                          <div className="font-bold text-blue-300 mb-1">
                            Whisper Suggestion
                          </div>
                          <div className="text-white">{whisperSuggestion}</div>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center space-x-2">
                      <Volume2 className="h-4 w-4 text-gray-400" />
                      <Slider
                        value={[volume]}
                        onValueChange={(value) => handleVolumeChange(value[0])}
                        max={100}
                        step={1}
                        className="flex-1"
                      />
                      <span className="text-gray-400 min-w-[3ch]">
                        {volume}%
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
