import { useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Campaign } from "@/types/schema";
import { RealTimeMetrics } from "./RealTimeMetrics";
import { CampaignMetrics } from "./CampaignMetrics";
import { ChartVisualizations } from "./ChartVisualizations";
import { BarChart, Activity, Clock } from "lucide-react";

interface CampaignAnalyticsProps {
  campaign: Campaign;
}

export function CampaignAnalytics({ campaign }: CampaignAnalyticsProps) {
  const [timeRange, setTimeRange] = useState<"day" | "week" | "month">("day");

  return (
    <div className="space-y-6">
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="bg-gray-700 mb-4">
          <TabsTrigger
            value="overview"
            className="data-[state=active]:bg-gray-600"
          >
            <Activity className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger
            value="realtime"
            className="data-[state=active]:bg-gray-600"
          >
            <Clock className="h-4 w-4 mr-2" />
            Real-Time
          </TabsTrigger>
          <TabsTrigger
            value="performance"
            className="data-[state=active]:bg-gray-600"
          >
            <BarChart className="h-4 w-4 mr-2" />
            Performance
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <CampaignMetrics campaign={campaign} />
        </TabsContent>

        <TabsContent value="realtime">
          <RealTimeMetrics campaign={campaign} />
        </TabsContent>

        <TabsContent value="performance">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-white">
                Campaign Performance
              </h3>
              <div className="flex bg-gray-700 rounded-md p-1">
                <button
                  className={`px-3 py-1 text-sm rounded-md ${
                    timeRange === "day"
                      ? "bg-gray-600 text-white"
                      : "text-gray-400"
                  }`}
                  onClick={() => setTimeRange("day")}
                >
                  Day
                </button>
                <button
                  className={`px-3 py-1 text-sm rounded-md ${
                    timeRange === "week"
                      ? "bg-gray-600 text-white"
                      : "text-gray-400"
                  }`}
                  onClick={() => setTimeRange("week")}
                >
                  Week
                </button>
                <button
                  className={`px-3 py-1 text-sm rounded-md ${
                    timeRange === "month"
                      ? "bg-gray-600 text-white"
                      : "text-gray-400"
                  }`}
                  onClick={() => setTimeRange("month")}
                >
                  Month
                </button>
              </div>
            </div>

            <ChartVisualizations campaign={campaign} timeRange={timeRange} />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
