import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus, Trash2, Edit2 } from "lucide-react";
import { CampaignGoal } from "@/types/schema";

interface GoalManagerProps {
  goals: CampaignGoal[];
  onChange: (goals: CampaignGoal[]) => void;
}

export function GoalManager({ goals, onChange }: GoalManagerProps) {
  const [newGoal, setNewGoal] = useState<Partial<CampaignGoal>>({
    title: "",
    target: 10,
    progress: 0,
    completed: false,
  });

  const [editingIndex, setEditingIndex] = useState<number | null>(null);

  const handleAddGoal = () => {
    if (!newGoal.title || !newGoal.target) return;

    const goalToAdd: CampaignGoal = {
      id: Math.random().toString(36).substring(2, 9),
      title: newGoal.title,
      target: newGoal.target || 10,
      progress: 0,
      completed: false,
    } as CampaignGoal;

    onChange([...goals, goalToAdd]);
    setNewGoal({ title: "", target: 10, progress: 0, completed: false });
  };

  const handleUpdateGoal = () => {
    if (editingIndex === null || !newGoal.title || !newGoal.target) return;

    const updatedGoals = [...goals];
    updatedGoals[editingIndex] = {
      ...updatedGoals[editingIndex],
      title: newGoal.title,
      target: newGoal.target,
    };

    onChange(updatedGoals);
    setNewGoal({ title: "", target: 10, progress: 0, completed: false });
    setEditingIndex(null);
  };

  const handleDeleteGoal = (index: number) => {
    const updatedGoals = [...goals];
    updatedGoals.splice(index, 1);
    onChange(updatedGoals);

    if (editingIndex === index) {
      setEditingIndex(null);
      setNewGoal({ title: "", target: 10, progress: 0, completed: false });
    }
  };

  const handleEditGoal = (index: number) => {
    const goal = goals[index];
    setNewGoal({
      title: goal.title,
      target: goal.target,
      progress: goal.progress,
      completed: goal.completed,
    });
    setEditingIndex(index);
  };

  return (
    <div className="space-y-4">
      <div className="bg-gray-700 p-3 rounded-md">
        <div className="grid grid-cols-12 gap-2 mb-2">
          <div className="col-span-7">
            <Label htmlFor="goal-title" className="text-sm text-gray-300">
              Goal Title
            </Label>
            <Input
              id="goal-title"
              placeholder="Enter goal title"
              className="bg-gray-800 border-gray-600 text-white"
              value={newGoal.title}
              onChange={(e) =>
                setNewGoal({ ...newGoal, title: e.target.value })
              }
            />
          </div>
          <div className="col-span-3">
            <Label htmlFor="goal-target" className="text-sm text-gray-300">
              Target
            </Label>
            <Input
              id="goal-target"
              type="number"
              min={1}
              className="bg-gray-800 border-gray-600 text-white"
              value={newGoal.target}
              onChange={(e) =>
                setNewGoal({
                  ...newGoal,
                  target: parseInt(e.target.value) || 1,
                })
              }
            />
          </div>
          <div className="col-span-2 flex items-end">
            <Button
              className="w-full bg-teal-600 hover:bg-teal-700"
              onClick={editingIndex !== null ? handleUpdateGoal : handleAddGoal}
            >
              {editingIndex !== null ? (
                <Edit2 className="h-4 w-4" />
              ) : (
                <Plus className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        {goals.length === 0 ? (
          <div className="text-center p-4 text-gray-400 bg-gray-700 rounded-md">
            No goals defined. Add your first campaign goal above.
          </div>
        ) : (
          goals.map((goal, index) => (
            <div
              key={goal.id}
              className={`flex items-center justify-between p-3 rounded-md ${
                editingIndex === index
                  ? "bg-gray-600 border border-teal-500"
                  : "bg-gray-700"
              }`}
            >
              <div className="flex-1">
                <div className="font-medium text-white">{goal.title}</div>
                <div className="text-sm text-gray-400">
                  Target: {goal.target} • Progress: {goal.progress} (
                  {Math.round((goal.progress / goal.target) * 100)}%)
                </div>
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-teal-400 hover:text-teal-300 hover:bg-gray-600"
                  onClick={() => handleEditGoal(index)}
                >
                  <Edit2 className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-red-400 hover:text-red-300 hover:bg-gray-600"
                  onClick={() => handleDeleteGoal(index)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
