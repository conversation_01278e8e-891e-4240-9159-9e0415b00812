import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Campaign, CampaignGoal } from "@/types/schema";
import { formatDate } from "@/lib/utils";
import CampaignGoals from "./CampaignGoals";
import CampaignTeam from "./CampaignTeam";
import CampaignChannels from "./CampaignChannels";
import CampaignComments from "./CampaignComments";
import CampaignContacts from "./CampaignContacts";

interface CampaignDetailsProps {
  campaign: Campaign;
  onEdit: (campaign: Campaign) => void;
  onDelete: (id: string) => void;
  onUpdateGoals: (
    campaignId: string,
    goals: CampaignGoal[]
  ) => Promise<CampaignGoal[] | null>;
  onAddTeamMember: (
    campaignId: string,
    email: string,
    role: string
  ) => Promise<any>;
  onRemoveTeamMember: (
    campaignId: string,
    memberId: string
  ) => Promise<boolean>;
  onAddComment: (campaignId: string, content: string) => Promise<any>;
  onUpdateChannels: (campaignId: string, channelSettings: any) => Promise<any>;
}

const CampaignDetails: React.FC<CampaignDetailsProps> = ({
  campaign,
  onEdit,
  onDelete,
  onUpdateGoals,
  onAddTeamMember,
  onRemoveTeamMember,
  onAddComment,
  onUpdateChannels,
}) => {
  const [activeTab, setActiveTab] = useState("overview");

  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case "DRAFT":
        return "bg-gray-500";
      case "SCHEDULED":
        return "bg-blue-500";
      case "ACTIVE":
        return "bg-green-500";
      case "COMPLETED":
        return "bg-purple-500";
      case "CANCELLED":
        return "bg-red-500";
      case "PAUSED":
        return "bg-yellow-500";
      default:
        return "bg-gray-500";
    }
  };

  const calculateProgress = () => {
    if (!campaign.goals || campaign.goals.length === 0) return 0;

    const totalTargets = campaign.goals.reduce(
      (sum, goal) => sum + goal.target,
      0
    );
    const totalProgress = campaign.goals.reduce(
      (sum, goal) => sum + goal.progress,
      0
    );

    return totalTargets > 0
      ? Math.round((totalProgress / totalTargets) * 100)
      : 0;
  };

  const handleDelete = () => {
    if (window.confirm("Are you sure you want to delete this campaign?")) {
      onDelete(campaign.id);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold">{campaign.name}</h1>
          <p className="text-muted-foreground">{campaign.description}</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => onEdit(campaign)}>
            Edit
          </Button>
          <Button variant="destructive" onClick={handleDelete}>
            Delete
          </Button>
        </div>
      </div>

      <div className="flex flex-wrap gap-4">
        <Badge className={getStatusColor(campaign.status)}>
          {campaign.status}
        </Badge>
        <div className="text-sm text-muted-foreground">
          Start: {formatDate(campaign.startDate)}
        </div>
        {campaign.endDate && (
          <div className="text-sm text-muted-foreground">
            End: {formatDate(campaign.endDate)}
          </div>
        )}
      </div>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle>Campaign Progress</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Overall Progress</span>
              <span>{calculateProgress()}%</span>
            </div>
            <Progress value={calculateProgress()} className="h-2" />
          </div>
        </CardContent>
      </Card>

      <Tabs
        defaultValue="overview"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <TabsList className="grid grid-cols-5 w-full">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="goals">Goals</TabsTrigger>
          <TabsTrigger value="contacts">Contacts</TabsTrigger>
          <TabsTrigger value="channels">Channels</TabsTrigger>
          <TabsTrigger value="team">Team</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Campaign Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Total Calls</p>
                  <p className="text-2xl font-bold">
                    {campaign.metrics.totalCalls}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">
                    Successful Calls
                  </p>
                  <p className="text-2xl font-bold">
                    {campaign.metrics.successfulCalls}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Failed Calls</p>
                  <p className="text-2xl font-bold">
                    {campaign.metrics.failedCalls}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Avg. Duration</p>
                  <p className="text-2xl font-bold">
                    {Math.round(campaign.metrics.averageDuration / 60)} min
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {campaign.callScript && (
            <Card>
              <CardHeader>
                <CardTitle>Call Script</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-muted p-4 rounded-md">
                  <p className="whitespace-pre-line">{campaign.callScript}</p>
                </div>
              </CardContent>
            </Card>
          )}

          <CampaignComments
            comments={campaign.comments || []}
            onAddComment={(content) => onAddComment(campaign.id, content)}
          />
        </TabsContent>

        <TabsContent value="goals" className="mt-4">
          <CampaignGoals
            campaignId={campaign.id}
            goals={campaign.goals || []}
            onUpdateGoals={onUpdateGoals}
          />
        </TabsContent>

        <TabsContent value="contacts" className="mt-4">
          <CampaignContacts contacts={campaign.contacts || []} />
        </TabsContent>

        <TabsContent value="channels" className="mt-4">
          <CampaignChannels
            campaignId={campaign.id}
            channelSettings={campaign.channelSettings}
            onUpdateChannels={onUpdateChannels}
          />
        </TabsContent>

        <TabsContent value="team" className="mt-4">
          <CampaignTeam
            campaignId={campaign.id}
            teamMembers={campaign.teamMembers || []}
            onAddTeamMember={onAddTeamMember}
            onRemoveTeamMember={onRemoveTeamMember}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CampaignDetails;
