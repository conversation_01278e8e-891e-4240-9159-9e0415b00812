import { useState, useCallback } from "react";
import { <PERSON>roll<PERSON><PERSON> } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Loader2, Search, X, Check, Filter } from "lucide-react";
import { useContactsForCampaign } from "@/hooks/useContactsForCampaign";

interface ContactSelectorProps {
  selectedContactIds: string[];
  onContactsSelected: (contactIds: string[]) => void;
  campaignId?: string;
}

export function ContactSelector({
  selectedContactIds,
  onContactsSelected,
  campaignId,
}: ContactSelectorProps) {
  const {
    contacts,
    isLoading,
    searchQuery,
    filterType,
    setSearchQuery,
    setFilterType,
  } = useContactsForCampaign(campaignId, selectedContactIds);

  const [showFilters, setShowFilters] = useState(false);

  // Custom toggle function that updates parent component
  const toggleContactSelection = useCallback(
    (contactId: string) => {
      const newSelectedIds = selectedContactIds.includes(contactId)
        ? selectedContactIds.filter((id) => id !== contactId)
        : [...selectedContactIds, contactId];

      onContactsSelected(newSelectedIds);
    },
    [selectedContactIds, onContactsSelected]
  );

  // Custom select all function
  const handleSelectAll = useCallback(() => {
    const allContactIds = contacts.map((contact) => contact.id);
    onContactsSelected(allContactIds);
  }, [contacts, onContactsSelected]);

  // Custom clear function
  const handleClearSelection = useCallback(() => {
    onContactsSelected([]);
  }, [onContactsSelected]);

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search contacts..."
            className="pl-8 bg-gray-700 text-white border-gray-600"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-1 top-1.5 h-6 w-6 text-gray-500 hover:text-white"
              onClick={() => setSearchQuery("")}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        <Button
          variant="outline"
          size="icon"
          className={`bg-gray-700 border-gray-600 ${
            showFilters ? "text-teal-400" : "text-gray-400"
          }`}
          onClick={() => setShowFilters(!showFilters)}
        >
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      {showFilters && (
        <div className="p-3 bg-gray-700 rounded-md">
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="type-filter" className="text-sm text-gray-300">
                Contact Type
              </Label>
              <Select
                value={filterType || ""}
                onValueChange={(value) =>
                  setFilterType(value === "" ? null : value)
                }
              >
                <SelectTrigger
                  id="type-filter"
                  className="bg-gray-800 border-gray-600 text-white"
                >
                  <SelectValue placeholder="All Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Types</SelectItem>
                  <SelectItem value="PERSONAL">Personal</SelectItem>
                  <SelectItem value="BUSINESS">Business</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-400">
          {selectedContactIds.length} of {contacts.length} selected
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            className="bg-gray-700 border-gray-600 text-white"
            onClick={handleSelectAll}
          >
            <Check className="h-3 w-3 mr-1" /> Select All
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="bg-gray-700 border-gray-600 text-white"
            onClick={handleClearSelection}
          >
            <X className="h-3 w-3 mr-1" /> Clear
          </Button>
        </div>
      </div>

      <ScrollArea className="h-60 bg-gray-700 rounded p-2">
        {isLoading ? (
          <div className="flex justify-center items-center h-full">
            <Loader2 className="h-6 w-6 animate-spin text-teal-500" />
            <span className="ml-2 text-teal-500">Loading contacts...</span>
          </div>
        ) : contacts.length === 0 ? (
          <div className="text-center p-4 text-gray-400">
            {searchQuery || filterType
              ? "No contacts match your search criteria"
              : "No contacts found. Add contacts first!"}
          </div>
        ) : (
          contacts.map((contact) => (
            <div
              key={contact.id}
              className="flex items-center space-x-2 mb-2 p-2 hover:bg-gray-600 rounded"
            >
              <Checkbox
                id={`contact-${contact.id}`}
                checked={selectedContactIds.includes(contact.id)}
                onCheckedChange={() => toggleContactSelection(contact.id)}
              />
              <div className="flex-1">
                <Label
                  htmlFor={`contact-${contact.id}`}
                  className="text-white font-medium cursor-pointer"
                >
                  {contact.name}
                </Label>
                <div className="text-xs text-gray-400">
                  {contact.phone} • {contact.email}
                </div>
              </div>
              <div className="text-xs px-2 py-1 rounded bg-gray-600 text-gray-300 capitalize">
                {contact.type?.toLowerCase()}
              </div>
            </div>
          ))
        )}
      </ScrollArea>
    </div>
  );
}
