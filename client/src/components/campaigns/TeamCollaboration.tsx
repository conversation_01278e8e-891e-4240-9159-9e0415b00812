import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import {
  Users,
  MessageSquare,
  Clock,
  Send,
  UserPlus,
  Shield,
  Eye,
  EyeOff,
  <PERSON><PERSON>2,
  Trash2,
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";

interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: "owner" | "admin" | "editor" | "viewer";
  avatarUrl?: string;
  lastActive?: Date;
}

interface Comment {
  id: string;
  userId: string;
  userName: string;
  userAvatarUrl?: string;
  content: string;
  timestamp: Date;
  attachments?: string[];
}

interface TeamCollaborationProps {
  campaignId: string;
}

export function TeamCollaboration({ campaignId }: TeamCollaborationProps) {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLoadingTeam, setIsLoadingTeam] = useState(false);
  const [isLoadingComments, setIsLoadingComments] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch team members and comments on component mount
  useEffect(() => {
    const fetchTeamData = async () => {
      if (!campaignId || campaignId === "new") return;

      setIsLoadingTeam(true);
      setIsLoadingComments(true);

      try {
        // In a real implementation, these would be API calls
        // For now, we'll simulate API calls with mock data
        const mockTeamMembers: TeamMember[] = [
          {
            id: "1",
            name: "John Doe",
            email: "<EMAIL>",
            role: "owner",
            avatarUrl: "",
            lastActive: new Date(),
          },
          {
            id: "2",
            name: "Jane Smith",
            email: "<EMAIL>",
            role: "admin",
            avatarUrl: "",
            lastActive: new Date(Date.now() - 3600000), // 1 hour ago
          },
          {
            id: "3",
            name: "Bob Johnson",
            email: "<EMAIL>",
            role: "editor",
            avatarUrl: "",
            lastActive: new Date(Date.now() - 86400000), // 1 day ago
          },
        ];

        const mockComments: Comment[] = [
          {
            id: "1",
            userId: "1",
            userName: "John Doe",
            content:
              "I've updated the campaign goals. Please review when you get a chance.",
            timestamp: new Date(Date.now() - 3600000), // 1 hour ago
          },
          {
            id: "2",
            userId: "2",
            userName: "Jane Smith",
            content:
              "The contact list looks good. I've added a few more high-priority contacts.",
            timestamp: new Date(Date.now() - 7200000), // 2 hours ago
          },
        ];

        // Simulate API delay
        setTimeout(() => {
          setTeamMembers(mockTeamMembers);
          setIsLoadingTeam(false);
        }, 500);

        setTimeout(() => {
          setComments(mockComments);
          setIsLoadingComments(false);
        }, 800);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : "An unknown error occurred";
        setError(errorMessage);
        toast({
          title: "Error",
          description: `Failed to load team data: ${errorMessage}`,
          variant: "destructive",
        });
        setIsLoadingTeam(false);
        setIsLoadingComments(false);
      }
    };

    fetchTeamData();
  }, [campaignId]);

  const [newComment, setNewComment] = useState("");
  const [showInviteDialog, setShowInviteDialog] = useState(false);
  const [inviteEmail, setInviteEmail] = useState("");
  const [inviteRole, setInviteRole] = useState<"admin" | "editor" | "viewer">(
    "editor"
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleAddComment = async () => {
    if (!newComment.trim() || !campaignId || campaignId === "new") return;

    try {
      // In a real implementation, this would be an API call
      // For now, we'll simulate an API call
      const comment: Comment = {
        id: Date.now().toString(),
        userId: "1", // Current user
        userName: "John Doe", // Current user
        content: newComment,
        timestamp: new Date(),
      };

      // Simulate API call
      setTimeout(() => {
        setComments([comment, ...comments]);
        setNewComment("");

        toast({
          title: "Comment Added",
          description: "Your comment has been added to the campaign.",
        });
      }, 500);
    } catch (error) {
      console.error("Error adding comment:", error);
      toast({
        title: "Error",
        description: "Failed to add comment",
        variant: "destructive",
      });
    }
  };

  const handleInviteTeamMember = async () => {
    if (!inviteEmail || !campaignId || campaignId === "new") {
      toast({
        title: "Error",
        description: "Email address is required",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // In a real implementation, this would be an API call
      // For now, we'll simulate an API call
      const newMember: TeamMember = {
        id: Date.now().toString(),
        name: inviteEmail.split("@")[0], // Use part of email as name
        email: inviteEmail,
        role: inviteRole,
        lastActive: undefined,
      };

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      setTeamMembers([...teamMembers, newMember]);

      toast({
        title: "Invitation Sent",
        description: `Invitation sent to ${inviteEmail} with ${inviteRole} access.`,
      });

      setInviteEmail("");
      setShowInviteDialog(false);
    } catch (error) {
      console.error("Error inviting team member:", error);
      toast({
        title: "Error",
        description: "Failed to send invitation",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChangeRole = async (
    memberId: string,
    newRole: "owner" | "admin" | "editor" | "viewer"
  ) => {
    if (!campaignId || campaignId === "new") return;

    try {
      // In a real implementation, this would be an API call
      // For now, we'll simulate an API call
      const member = teamMembers.find((m) => m.id === memberId);

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      setTeamMembers((members) =>
        members.map((member) =>
          member.id === memberId ? { ...member, role: newRole } : member
        )
      );

      toast({
        title: "Role Updated",
        description: `${member?.name}'s role has been updated to ${newRole}.`,
      });
    } catch (error) {
      console.error("Error updating role:", error);
      toast({
        title: "Error",
        description: "Failed to update team member role",
        variant: "destructive",
      });
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    if (!campaignId || campaignId === "new") return;

    if (confirm("Are you sure you want to remove this team member?")) {
      try {
        const member = teamMembers.find((m) => m.id === memberId);

        // In a real implementation, this would be an API call
        // For now, we'll simulate an API call

        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 500));

        setTeamMembers((members) => members.filter((m) => m.id !== memberId));

        toast({
          title: "Member Removed",
          description: `${member?.name} has been removed from the campaign.`,
        });
      } catch (error) {
        console.error("Error removing team member:", error);
        toast({
          title: "Error",
          description: "Failed to remove team member",
          variant: "destructive",
        });
      }
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "owner":
        return "bg-purple-500";
      case "admin":
        return "bg-blue-500";
      case "editor":
        return "bg-green-500";
      case "viewer":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "owner":
        return <Shield className="h-3 w-3" />;
      case "admin":
        return <Shield className="h-3 w-3" />;
      case "editor":
        return <Eye className="h-3 w-3" />;
      case "viewer":
        return <EyeOff className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const formatTimeAgo = (date: Date) => {
    const seconds = Math.floor((new Date().getTime() - date.getTime()) / 1000);

    let interval = seconds / 31536000;
    if (interval > 1) return Math.floor(interval) + " years ago";

    interval = seconds / 2592000;
    if (interval > 1) return Math.floor(interval) + " months ago";

    interval = seconds / 86400;
    if (interval > 1) return Math.floor(interval) + " days ago";

    interval = seconds / 3600;
    if (interval > 1) return Math.floor(interval) + " hours ago";

    interval = seconds / 60;
    if (interval > 1) return Math.floor(interval) + " minutes ago";

    return Math.floor(seconds) + " seconds ago";
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-white">Team Collaboration</h3>
        <Button
          variant="outline"
          size="sm"
          className="bg-gray-700 border-gray-600 text-white"
          onClick={() => setShowInviteDialog(true)}
        >
          <UserPlus className="h-4 w-4 mr-2 text-teal-400" />
          Invite Team Member
        </Button>
      </div>

      <Tabs defaultValue="team" className="w-full">
        <TabsList className="bg-gray-700 mb-4">
          <TabsTrigger value="team" className="data-[state=active]:bg-gray-600">
            <Users className="h-4 w-4 mr-2" />
            Team Members
          </TabsTrigger>
          <TabsTrigger
            value="comments"
            className="data-[state=active]:bg-gray-600"
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            Comments
          </TabsTrigger>
        </TabsList>

        <TabsContent value="team">
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader className="pb-2">
              <CardTitle className="text-white text-base">
                Campaign Team
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-80">
                {isLoadingTeam ? (
                  <div className="flex justify-center items-center h-40">
                    <Loader2 className="h-6 w-6 animate-spin text-teal-500" />
                    <span className="ml-2 text-teal-500">
                      Loading team members...
                    </span>
                  </div>
                ) : error ? (
                  <div className="text-center p-4 text-red-400">
                    Error loading team members: {error}
                  </div>
                ) : teamMembers.length === 0 ? (
                  <div className="text-center p-4 text-gray-400">
                    No team members found. Invite someone to collaborate!
                  </div>
                ) : (
                  <div className="space-y-4">
                    {teamMembers.map((member) => (
                      <div
                        key={member.id}
                        className="flex items-center justify-between p-3 bg-gray-700 rounded-md"
                      >
                        <div className="flex items-center space-x-3">
                          <Avatar>
                            <AvatarImage src={member.avatarUrl} />
                            <AvatarFallback className="bg-gray-600 text-white">
                              {member.name.substring(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium text-white">
                              {member.name}
                            </div>
                            <div className="text-xs text-gray-400">
                              {member.email}
                            </div>
                            {member.lastActive && (
                              <div className="text-xs text-gray-500 flex items-center mt-1">
                                <Clock className="h-3 w-3 mr-1" />
                                {formatTimeAgo(member.lastActive)}
                              </div>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Badge
                            className={`${getRoleBadgeColor(
                              member.role
                            )} flex items-center space-x-1`}
                          >
                            {getRoleIcon(member.role)}
                            <span className="capitalize">{member.role}</span>
                          </Badge>

                          {member.role !== "owner" && (
                            <Select
                              value={member.role}
                              onValueChange={(value) =>
                                handleChangeRole(member.id, value as any)
                              }
                            >
                              <SelectTrigger className="h-7 w-24 bg-gray-600 border-gray-500 text-white">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="admin">Admin</SelectItem>
                                <SelectItem value="editor">Editor</SelectItem>
                                <SelectItem value="viewer">Viewer</SelectItem>
                              </SelectContent>
                            </Select>
                          )}

                          {member.role !== "owner" && (
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-7 w-7 text-red-400 hover:text-red-300 hover:bg-gray-600"
                              onClick={() => handleRemoveMember(member.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>

          <div className="mt-4 text-sm text-gray-400">
            <p>
              <strong>Owner:</strong> Full access and can manage team members
            </p>
            <p>
              <strong>Admin:</strong> Can edit campaign settings and invite team
              members
            </p>
            <p>
              <strong>Editor:</strong> Can edit campaign content but not
              settings
            </p>
            <p>
              <strong>Viewer:</strong> Can only view the campaign
            </p>
          </div>
        </TabsContent>

        <TabsContent value="comments">
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader className="pb-2">
              <CardTitle className="text-white text-base">
                Campaign Comments
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex space-x-3 mb-4">
                <Avatar>
                  <AvatarFallback className="bg-gray-600 text-white">
                    JD
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <Textarea
                    className="bg-gray-700 text-white border-gray-600 min-h-20"
                    placeholder="Add a comment..."
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                  />
                  <div className="flex justify-end mt-2">
                    <Button
                      className="bg-teal-600 hover:bg-teal-700 text-white"
                      onClick={handleAddComment}
                      disabled={!newComment.trim()}
                    >
                      <Send className="h-4 w-4 mr-2" />
                      Post Comment
                    </Button>
                  </div>
                </div>
              </div>

              <ScrollArea className="h-80">
                {isLoadingComments ? (
                  <div className="flex justify-center items-center h-40">
                    <Loader2 className="h-6 w-6 animate-spin text-teal-500" />
                    <span className="ml-2 text-teal-500">
                      Loading comments...
                    </span>
                  </div>
                ) : error ? (
                  <div className="text-center p-4 text-red-400">
                    Error loading comments: {error}
                  </div>
                ) : comments.length === 0 ? (
                  <div className="text-center p-4 text-gray-400">
                    No comments yet. Be the first to comment!
                  </div>
                ) : (
                  <div className="space-y-4">
                    {comments.map((comment) => (
                      <div
                        key={comment.id}
                        className="flex space-x-3 p-3 bg-gray-700 rounded-md"
                      >
                        <Avatar>
                          <AvatarImage src={comment.userAvatarUrl} />
                          <AvatarFallback className="bg-gray-600 text-white">
                            {comment.userName.substring(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div className="font-medium text-white">
                              {comment.userName}
                            </div>
                            <div className="text-xs text-gray-400">
                              {formatTimeAgo(comment.timestamp)}
                            </div>
                          </div>
                          <div className="mt-1 text-gray-300">
                            {comment.content}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Dialog open={showInviteDialog} onOpenChange={setShowInviteDialog}>
        <DialogContent className="bg-gray-900 text-white">
          <DialogHeader>
            <DialogTitle>Invite Team Member</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div>
              <Label htmlFor="invite-email" className="text-white">
                Email Address
              </Label>
              <Input
                id="invite-email"
                type="email"
                className="bg-gray-700 text-white border-gray-600"
                placeholder="Enter email address"
                value={inviteEmail}
                onChange={(e) => setInviteEmail(e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="invite-role" className="text-white">
                Role
              </Label>
              <Select
                value={inviteRole}
                onValueChange={(value) => setInviteRole(value as any)}
              >
                <SelectTrigger
                  id="invite-role"
                  className="bg-gray-700 text-white border-gray-600"
                >
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="admin">Admin</SelectItem>
                  <SelectItem value="editor">Editor</SelectItem>
                  <SelectItem value="viewer">Viewer</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-400 mt-1">
                {inviteRole === "admin" &&
                  "Can edit campaign settings and invite team members"}
                {inviteRole === "editor" &&
                  "Can edit campaign content but not settings"}
                {inviteRole === "viewer" && "Can only view the campaign"}
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              className="bg-gray-700 border-gray-600 text-white"
              onClick={() => setShowInviteDialog(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              className="bg-teal-600 hover:bg-teal-700 text-white"
              onClick={handleInviteTeamMember}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Send Invitation
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
