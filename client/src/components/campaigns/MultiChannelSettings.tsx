import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Phone,
  Mail,
  MessageSquare,
  Clock,
  Settings,
  AlertCircle,
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";

interface ChannelSettings {
  enabled: boolean;
  message?: string;
  template?: string;
  delay?: number;
  sendTime?: string;
}

interface MultiChannelConfig {
  voice: ChannelSettings;
  sms: ChannelSettings;
  email: ChannelSettings;
}

interface MultiChannelSettingsProps {
  onSave: (config: MultiChannelConfig) => void;
  initialConfig?: Partial<MultiChannelConfig>;
}

export function MultiChannelSettings({
  onSave,
  initialConfig,
}: MultiChannelSettingsProps) {
  const [config, setConfig] = useState<MultiChannelConfig>(() => {
    // Create default config
    const defaultConfig: MultiChannelConfig = {
      voice: {
        enabled: true,
        message: "",
        delay: 0,
      },
      sms: {
        enabled: false,
        message: "",
        delay: 60,
        sendTime: "09:00",
      },
      email: {
        enabled: false,
        template: "default",
        delay: 120,
        sendTime: "10:00",
      },
    };

    // If initialConfig is provided, merge it with the default config
    if (initialConfig) {
      return {
        voice: { ...defaultConfig.voice, ...(initialConfig.voice || {}) },
        sms: { ...defaultConfig.sms, ...(initialConfig.sms || {}) },
        email: { ...defaultConfig.email, ...(initialConfig.email || {}) },
      };
    }

    return defaultConfig;
  });

  const [activeTab, setActiveTab] = useState<"voice" | "sms" | "email">(
    "voice"
  );

  const handleToggleChannel = (
    channel: keyof MultiChannelConfig,
    enabled: boolean
  ) => {
    setConfig((prev) => ({
      ...prev,
      [channel]: {
        ...prev[channel],
        enabled,
      },
    }));
  };

  const handleUpdateChannelSetting = (
    channel: keyof MultiChannelConfig,
    setting: keyof ChannelSettings,
    value: any
  ) => {
    setConfig((prev) => ({
      ...prev,
      [channel]: {
        ...prev[channel],
        [setting]: value,
      },
    }));
  };

  const handleSaveSettings = async () => {
    // Validate settings
    if (config.voice.enabled && !config.voice.message) {
      toast({
        title: "Validation Error",
        description: "Voice script is required when voice channel is enabled",
        variant: "destructive",
      });
      setActiveTab("voice");
      return;
    }

    if (config.sms.enabled && !config.sms.message) {
      toast({
        title: "Validation Error",
        description: "SMS message is required when SMS channel is enabled",
        variant: "destructive",
      });
      setActiveTab("sms");
      return;
    }

    if (config.email.enabled && !config.email.template) {
      toast({
        title: "Validation Error",
        description: "Email template is required when email channel is enabled",
        variant: "destructive",
      });
      setActiveTab("email");
      return;
    }

    try {
      // Save settings
      onSave(config);

      toast({
        title: "Settings Saved",
        description: "Multi-channel campaign settings have been saved",
      });
    } catch (error) {
      console.error("Error saving channel settings:", error);
      toast({
        title: "Error",
        description: "Failed to save channel settings",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-white">
          Multi-Channel Campaign
        </h3>
        <Button
          className="bg-teal-600 hover:bg-teal-700 text-white"
          onClick={handleSaveSettings}
        >
          <Settings className="h-4 w-4 mr-2" />
          Save Channel Settings
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card
          className={`border-l-4 ${
            config.voice.enabled ? "border-l-teal-500" : "border-l-gray-700"
          } bg-gray-800 border-gray-700`}
        >
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle className="text-white text-base flex items-center">
                <Phone className="h-4 w-4 mr-2 text-teal-400" />
                Voice Calls
              </CardTitle>
              <Switch
                checked={config.voice.enabled}
                onCheckedChange={(checked) =>
                  handleToggleChannel("voice", checked)
                }
              />
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-400">
              {config.voice.enabled
                ? "AI-powered voice calls will be made to contacts"
                : "Voice channel is disabled"}
            </p>
            {config.voice.enabled && (
              <Button
                variant="link"
                className="text-teal-400 p-0 h-auto text-xs"
                onClick={() => setActiveTab("voice")}
              >
                Configure voice settings
              </Button>
            )}
          </CardContent>
        </Card>

        <Card
          className={`border-l-4 ${
            config.sms.enabled ? "border-l-orange-500" : "border-l-gray-700"
          } bg-gray-800 border-gray-700`}
        >
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle className="text-white text-base flex items-center">
                <MessageSquare className="h-4 w-4 mr-2 text-orange-400" />
                SMS Messages
              </CardTitle>
              <Switch
                checked={config.sms.enabled}
                onCheckedChange={(checked) =>
                  handleToggleChannel("sms", checked)
                }
              />
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-400">
              {config.sms.enabled
                ? `SMS will be sent ${config.sms.delay} minutes after call`
                : "SMS channel is disabled"}
            </p>
            {config.sms.enabled && (
              <Button
                variant="link"
                className="text-orange-400 p-0 h-auto text-xs"
                onClick={() => setActiveTab("sms")}
              >
                Configure SMS settings
              </Button>
            )}
          </CardContent>
        </Card>

        <Card
          className={`border-l-4 ${
            config.email.enabled ? "border-l-blue-500" : "border-l-gray-700"
          } bg-gray-800 border-gray-700`}
        >
          <CardHeader className="pb-2">
            <div className="flex justify-between items-center">
              <CardTitle className="text-white text-base flex items-center">
                <Mail className="h-4 w-4 mr-2 text-blue-400" />
                Email
              </CardTitle>
              <Switch
                checked={config.email.enabled}
                onCheckedChange={(checked) =>
                  handleToggleChannel("email", checked)
                }
              />
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-400">
              {config.email.enabled
                ? `Email will be sent ${config.email.delay} minutes after call`
                : "Email channel is disabled"}
            </p>
            {config.email.enabled && (
              <Button
                variant="link"
                className="text-blue-400 p-0 h-auto text-xs"
                onClick={() => setActiveTab("email")}
              >
                Configure email settings
              </Button>
            )}
          </CardContent>
        </Card>
      </div>

      <Tabs
        value={activeTab}
        onValueChange={(value) => setActiveTab(value as any)}
        className="w-full"
      >
        <TabsList className="bg-gray-700 mb-4">
          <TabsTrigger
            value="voice"
            className="data-[state=active]:bg-gray-600"
          >
            <Phone className="h-4 w-4 mr-2" />
            Voice Settings
          </TabsTrigger>
          <TabsTrigger value="sms" className="data-[state=active]:bg-gray-600">
            <MessageSquare className="h-4 w-4 mr-2" />
            SMS Settings
          </TabsTrigger>
          <TabsTrigger
            value="email"
            className="data-[state=active]:bg-gray-600"
          >
            <Mail className="h-4 w-4 mr-2" />
            Email Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="voice" className="space-y-4">
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader className="pb-2">
              <CardTitle className="text-white text-base">
                Voice Call Script
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                className="bg-gray-700 text-white border-gray-600 min-h-32"
                placeholder="Enter the script for your AI assistant to follow during calls..."
                value={config.voice.message || ""}
                onChange={(e) =>
                  handleUpdateChannelSetting("voice", "message", e.target.value)
                }
                disabled={!config.voice.enabled}
              />
              <p className="text-xs text-gray-400 mt-2">
                This script will be used by the AI assistant when making calls.
                You can use variables like {"{contact_name}"},{" "}
                {"{company_name}"}, etc.
              </p>
            </CardContent>
          </Card>

          {!config.voice.enabled && (
            <div className="flex items-center p-3 bg-gray-700 rounded-md text-amber-400 text-sm">
              <AlertCircle className="h-4 w-4 mr-2" />
              Voice channel is currently disabled. Enable it to configure voice
              settings.
            </div>
          )}
        </TabsContent>

        <TabsContent value="sms" className="space-y-4">
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader className="pb-2">
              <CardTitle className="text-white text-base">
                SMS Message
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Textarea
                className="bg-gray-700 text-white border-gray-600 h-24"
                placeholder="Enter the SMS message to send to contacts..."
                value={config.sms.message || ""}
                onChange={(e) =>
                  handleUpdateChannelSetting("sms", "message", e.target.value)
                }
                disabled={!config.sms.enabled}
              />
              <p className="text-xs text-gray-400">
                SMS message that will be sent to contacts. You can use variables
                like {"{contact_name}"}, {"{company_name}"}, etc.
              </p>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="sms-delay" className="text-white">
                    Delay After Call (minutes)
                  </Label>
                  <Input
                    id="sms-delay"
                    type="number"
                    min={0}
                    max={1440}
                    className="bg-gray-700 text-white border-gray-600"
                    value={config.sms.delay}
                    onChange={(e) =>
                      handleUpdateChannelSetting(
                        "sms",
                        "delay",
                        parseInt(e.target.value) || 0
                      )
                    }
                    disabled={!config.sms.enabled}
                  />
                </div>
                <div>
                  <Label htmlFor="sms-time" className="text-white">
                    Send Time
                  </Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="sms-time"
                      type="time"
                      className="bg-gray-700 text-white border-gray-600"
                      value={config.sms.sendTime}
                      onChange={(e) =>
                        handleUpdateChannelSetting(
                          "sms",
                          "sendTime",
                          e.target.value
                        )
                      }
                      disabled={!config.sms.enabled}
                    />
                    <Clock className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {!config.sms.enabled && (
            <div className="flex items-center p-3 bg-gray-700 rounded-md text-amber-400 text-sm">
              <AlertCircle className="h-4 w-4 mr-2" />
              SMS channel is currently disabled. Enable it to configure SMS
              settings.
            </div>
          )}
        </TabsContent>

        <TabsContent value="email" className="space-y-4">
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader className="pb-2">
              <CardTitle className="text-white text-base">
                Email Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="email-template" className="text-white">
                  Email Template
                </Label>
                <Select
                  value={config.email.template}
                  onValueChange={(value) =>
                    handleUpdateChannelSetting("email", "template", value)
                  }
                  disabled={!config.email.enabled}
                >
                  <SelectTrigger
                    id="email-template"
                    className="bg-gray-700 text-white border-gray-600"
                  >
                    <SelectValue placeholder="Select template" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="default">Default Follow-up</SelectItem>
                    <SelectItem value="meeting">Meeting Request</SelectItem>
                    <SelectItem value="demo">Product Demo</SelectItem>
                    <SelectItem value="custom">Custom Template</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email-delay" className="text-white">
                    Delay After Call (minutes)
                  </Label>
                  <Input
                    id="email-delay"
                    type="number"
                    min={0}
                    max={1440}
                    className="bg-gray-700 text-white border-gray-600"
                    value={config.email.delay}
                    onChange={(e) =>
                      handleUpdateChannelSetting(
                        "email",
                        "delay",
                        parseInt(e.target.value) || 0
                      )
                    }
                    disabled={!config.email.enabled}
                  />
                </div>
                <div>
                  <Label htmlFor="email-time" className="text-white">
                    Send Time
                  </Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="email-time"
                      type="time"
                      className="bg-gray-700 text-white border-gray-600"
                      value={config.email.sendTime}
                      onChange={(e) =>
                        handleUpdateChannelSetting(
                          "email",
                          "sendTime",
                          e.target.value
                        )
                      }
                      disabled={!config.email.enabled}
                    />
                    <Clock className="h-4 w-4 text-gray-400" />
                  </div>
                </div>
              </div>

              {config.email.template === "custom" && (
                <div>
                  <Label htmlFor="email-subject" className="text-white">
                    Email Subject
                  </Label>
                  <Input
                    id="email-subject"
                    className="bg-gray-700 text-white border-gray-600"
                    placeholder="Enter email subject"
                    disabled={!config.email.enabled}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {!config.email.enabled && (
            <div className="flex items-center p-3 bg-gray-700 rounded-md text-amber-400 text-sm">
              <AlertCircle className="h-4 w-4 mr-2" />
              Email channel is currently disabled. Enable it to configure email
              settings.
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
