import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Campaign } from "@/types/schema";
import { Loader2 } from "lucide-react";

// Mock data for charts
const generateMockData = (days: number) => {
  const data = [];
  const now = new Date();

  for (let i = 0; i < days; i++) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);

    data.push({
      date: date.toISOString().split("T")[0],
      calls: Math.floor(Math.random() * 20) + 5,
      successRate: Math.random() * 30 + 50, // 50-80%
      duration: Math.floor(Math.random() * 120) + 60, // 1-3 minutes
      sentiment: Math.random() * 0.3 + 0.5, // 0.5-0.8
    });
  }

  return data.reverse();
};

interface ChartVisualizationsProps {
  campaign: Campaign;
  timeRange: "day" | "week" | "month";
}

export function ChartVisualizations({
  campaign,
  timeRange,
}: ChartVisualizationsProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [chartData, setChartData] = useState<any[]>([]);

  // Fetch chart data based on time range
  useEffect(() => {
    setIsLoading(true);

    // Simulate API call
    setTimeout(() => {
      const days = timeRange === "day" ? 1 : timeRange === "week" ? 7 : 30;
      setChartData(generateMockData(days));
      setIsLoading(false);
    }, 1000);
  }, [timeRange]);

  // Calculate summary metrics
  const calculateSummary = () => {
    if (chartData.length === 0) return null;

    const totalCalls = chartData.reduce((sum, day) => sum + day.calls, 0);
    const avgSuccessRate =
      chartData.reduce((sum, day) => sum + day.successRate, 0) /
      chartData.length;
    const avgDuration =
      chartData.reduce((sum, day) => sum + day.duration, 0) / chartData.length;
    const avgSentiment =
      chartData.reduce((sum, day) => sum + day.sentiment, 0) / chartData.length;

    return {
      totalCalls,
      avgSuccessRate,
      avgDuration,
      avgSentiment,
    };
  };

  const summary = calculateSummary();

  // Render bar chart for call volume
  const renderCallVolumeChart = () => {
    if (chartData.length === 0) return null;

    const maxCalls = Math.max(...chartData.map((day) => day.calls));

    return (
      <div className="h-48 flex items-end space-x-1">
        {chartData.map((day, index) => (
          <div key={index} className="flex flex-col items-center flex-1">
            <div
              className="w-full bg-teal-500 rounded-t"
              style={{
                height: `${(day.calls / maxCalls) * 100}%`,
                minHeight: "4px",
              }}
            />
            <div className="text-xs text-gray-400 mt-1 truncate w-full text-center">
              {timeRange === "day"
                ? new Date(day.date).toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })
                : new Date(day.date).toLocaleDateString([], {
                    month: "short",
                    day: "numeric",
                  })}
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Render line chart for success rate
  const renderSuccessRateChart = () => {
    if (chartData.length === 0) return null;

    // Create points for the SVG path
    const points = chartData
      .map((day, index) => {
        const x = (index / (chartData.length - 1)) * 100;
        const y = 100 - (day.successRate / 100) * 100;
        return `${x},${y}`;
      })
      .join(" ");

    return (
      <div className="h-48 relative">
        {/* Grid lines */}
        <div className="absolute inset-0 flex flex-col justify-between">
          {[0, 25, 50, 75, 100].map((percent) => (
            <div
              key={percent}
              className="w-full h-px bg-gray-700"
              style={{ bottom: `${percent}%` }}
            />
          ))}
        </div>

        {/* Y-axis labels */}
        <div className="absolute inset-y-0 left-0 w-8 flex flex-col justify-between">
          {[0, 25, 50, 75, 100].map((percent) => (
            <div
              key={percent}
              className="text-xs text-gray-400"
              style={{ bottom: `${percent}%`, transform: "translateY(50%)" }}
            >
              {percent}%
            </div>
          ))}
        </div>

        {/* SVG line chart */}
        <svg
          className="absolute inset-0 h-full w-full overflow-visible"
          preserveAspectRatio="none"
        >
          <polyline
            points={points}
            fill="none"
            stroke="#14b8a6"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>

        {/* X-axis labels */}
        <div className="absolute bottom-0 inset-x-0 flex justify-between">
          {chartData.map((day, index) => (
            <div key={index} className="text-xs text-gray-400 truncate">
              {timeRange === "day"
                ? new Date(day.date).toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })
                : new Date(day.date).toLocaleDateString([], {
                    month: "short",
                    day: "numeric",
                  })}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render pie chart for call outcomes
  const renderCallOutcomesChart = () => {
    if (!summary) return null;

    const successRate = summary.avgSuccessRate;
    const failureRate = 100 - successRate;

    // Calculate SVG arc paths for pie chart
    const radius = 40;
    const centerX = 50;
    const centerY = 50;

    // Success slice
    const successAngle = (successRate / 100) * 360;
    const successEndX =
      centerX + radius * Math.cos(((successAngle - 90) * Math.PI) / 180);
    const successEndY =
      centerY + radius * Math.sin(((successAngle - 90) * Math.PI) / 180);

    const successPath = `
      M ${centerX} ${centerY}
      L ${centerX} ${centerY - radius}
      A ${radius} ${radius} 0 ${
      successAngle > 180 ? 1 : 0
    } 1 ${successEndX} ${successEndY}
      Z
    `;

    // Failure slice
    const failurePath = `
      M ${centerX} ${centerY}
      L ${successEndX} ${successEndY}
      A ${radius} ${radius} 0 ${failureRate > 180 ? 1 : 0} 1 ${centerX} ${
      centerY - radius
    }
      Z
    `;

    return (
      <div className="flex items-center justify-center h-32">
        <div className="relative w-24 h-24">
          <svg viewBox="0 0 100 100">
            <path d={successPath} fill="#14b8a6" />
            <path d={failurePath} fill="#ef4444" />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center text-white font-medium">
            {Math.round(successRate)}%
          </div>
        </div>
        <div className="ml-4 space-y-2">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-teal-500 rounded-full mr-2" />
            <span className="text-sm text-gray-300">
              Success ({Math.round(successRate)}%)
            </span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-red-500 rounded-full mr-2" />
            <span className="text-sm text-gray-300">
              Failed ({Math.round(failureRate)}%)
            </span>
          </div>
        </div>
      </div>
    );
  };

  // Render heat map for best call times
  const renderBestCallTimesChart = () => {
    // Mock data for best call times
    const hours = [9, 10, 11, 12, 13, 14, 15, 16, 17];
    const days = ["Mon", "Tue", "Wed", "Thu", "Fri"];

    const heatmapData = days
      .map((day) => {
        return hours.map((hour) => {
          return {
            day,
            hour,
            value: Math.random(),
          };
        });
      })
      .flat();

    return (
      <div className="h-32">
        <div className="grid grid-cols-9 gap-1 h-full">
          {hours.map((hour) => (
            <div key={hour} className="flex flex-col">
              <div className="text-xs text-gray-400 text-center mb-1">
                {hour}:00
              </div>
              <div className="flex-1 grid grid-rows-5 gap-1">
                {days.map((day) => {
                  const cell = heatmapData.find(
                    (d) => d.day === day && d.hour === hour
                  );
                  const intensity = cell ? Math.floor(cell.value * 100) : 0;

                  return (
                    <div
                      key={`${day}-${hour}`}
                      className="rounded"
                      style={{
                        backgroundColor: `rgba(20, 184, 166, ${
                          cell?.value || 0
                        })`,
                        cursor: "pointer",
                      }}
                      title={`${day} ${hour}:00 - Success rate: ${intensity}%`}
                    />
                  );
                })}
              </div>
            </div>
          ))}
        </div>
        <div className="flex justify-between mt-1">
          {days.map((day) => (
            <div
              key={day}
              className="text-xs text-gray-400 rotate-90 origin-left"
            >
              {day}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render goal progress chart
  const renderGoalProgressChart = () => {
    if (!campaign.goals || campaign.goals.length === 0) {
      return (
        <div className="text-center text-gray-400 h-32 flex items-center justify-center">
          No goals defined for this campaign
        </div>
      );
    }

    return (
      <div className="space-y-3 h-32 overflow-y-auto py-1">
        {campaign.goals.map((goal) => {
          const progress = (goal.progress / goal.target) * 100;

          return (
            <div key={goal.id} className="space-y-1">
              <div className="flex justify-between text-xs">
                <span className="text-gray-300">{goal.title}</span>
                <span className="text-gray-400">
                  {goal.progress} / {goal.target}
                </span>
              </div>
              <div className="h-2 bg-gray-700 rounded-full overflow-hidden">
                <div
                  className={`h-full rounded-full ${
                    goal.completed ? "bg-green-500" : "bg-teal-500"
                  }`}
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-teal-500" />
          <span className="ml-2 text-teal-500">Loading analytics...</span>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-4 gap-4">
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="pt-6">
                <div className="text-2xl font-bold text-white">
                  {summary?.totalCalls}
                </div>
                <div className="text-xs text-gray-400">Total Calls</div>
              </CardContent>
            </Card>
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="pt-6">
                <div className="text-2xl font-bold text-white">
                  {summary?.avgSuccessRate.toFixed(1)}%
                </div>
                <div className="text-xs text-gray-400">Success Rate</div>
              </CardContent>
            </Card>
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="pt-6">
                <div className="text-2xl font-bold text-white">
                  {Math.floor(summary?.avgDuration || 0 / 60)}m{" "}
                  {Math.round(summary?.avgDuration || 0 % 60)}s
                </div>
                <div className="text-xs text-gray-400">Avg. Duration</div>
              </CardContent>
            </Card>
            <Card className="bg-gray-800 border-gray-700">
              <CardContent className="pt-6">
                <div className="text-2xl font-bold text-white">
                  {((summary?.avgSentiment || 0) * 100).toFixed(0)}%
                </div>
                <div className="text-xs text-gray-400">Sentiment Score</div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader className="pb-2">
                <CardTitle className="text-white text-base">
                  Call Volume
                </CardTitle>
              </CardHeader>
              <CardContent>{renderCallVolumeChart()}</CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardHeader className="pb-2">
                <CardTitle className="text-white text-base">
                  Success Rate Trend
                </CardTitle>
              </CardHeader>
              <CardContent>{renderSuccessRateChart()}</CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card className="bg-gray-800 border-gray-700">
              <CardHeader className="pb-2">
                <CardTitle className="text-white text-base">
                  Call Outcomes
                </CardTitle>
              </CardHeader>
              <CardContent>{renderCallOutcomesChart()}</CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardHeader className="pb-2">
                <CardTitle className="text-white text-base">
                  Best Call Times
                </CardTitle>
              </CardHeader>
              <CardContent>{renderBestCallTimesChart()}</CardContent>
            </Card>

            <Card className="bg-gray-800 border-gray-700">
              <CardHeader className="pb-2">
                <CardTitle className="text-white text-base">
                  Goal Progress
                </CardTitle>
              </CardHeader>
              <CardContent>{renderGoalProgressChart()}</CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  );
}
