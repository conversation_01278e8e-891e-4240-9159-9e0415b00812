import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Campaign } from "@/types/schema";
import { Loader2, <PERSON><PERSON><PERSON>, <PERSON>O<PERSON>, Clock, BarChart2 } from "lucide-react";

interface RealTimeMetricsProps {
  campaign: Campaign;
  refreshInterval?: number; // in milliseconds
}

export function RealTimeMetrics({
  campaign,
  refreshInterval = 5000,
}: RealTimeMetricsProps) {
  const [metrics, setMetrics] = useState(campaign.metrics);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  // Simulate real-time updates
  useEffect(() => {
    const updateMetrics = () => {
      setIsLoading(true);

      // In a real app, this would be an API call to get the latest metrics
      setTimeout(() => {
        // Simulate random changes to metrics
        const totalCalls = metrics.totalCalls + Math.floor(Math.random() * 3);
        const successfulCalls =
          metrics.successfulCalls + Math.floor(Math.random() * 2);
        const failedCalls = totalCalls - successfulCalls;

        setMetrics({
          ...metrics,
          totalCalls,
          successfulCalls,
          failedCalls,
          averageDuration: Math.floor(Math.random() * 120) + 60, // 1-3 minutes
          averageSentiment: Math.random() * 0.2 + 0.6, // 0.6-0.8 (positive)
        });

        setLastUpdated(new Date());
        setIsLoading(false);
      }, 1000);
    };

    // Initial update
    updateMetrics();

    // Set up interval for updates
    const intervalId = setInterval(updateMetrics, refreshInterval);

    // Clean up on unmount
    return () => clearInterval(intervalId);
  }, [campaign.id, refreshInterval]);

  // Calculate success rate
  const successRate =
    metrics.totalCalls > 0
      ? (metrics.successfulCalls / metrics.totalCalls) * 100
      : 0;

  // Format time since last update
  const getTimeSinceUpdate = () => {
    const now = new Date();
    const diffInSeconds = Math.floor(
      (now.getTime() - lastUpdated.getTime()) / 1000
    );

    if (diffInSeconds < 60) {
      return `${diffInSeconds} seconds ago`;
    } else {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? "s" : ""} ago`;
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-white">Real-Time Metrics</h3>
        <div className="flex items-center text-sm text-gray-400">
          <Clock className="h-3 w-3 mr-1" />
          Updated {getTimeSinceUpdate()}
          {isLoading && (
            <Loader2 className="h-3 w-3 ml-2 animate-spin text-teal-500" />
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-base flex items-center">
              <PhoneCall className="h-4 w-4 mr-2 text-teal-400" />
              Call Statistics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-gray-400 text-xs">Total Calls</p>
                <p className="text-white text-xl">{metrics.totalCalls}</p>
              </div>
              <div>
                <p className="text-gray-400 text-xs">Success Rate</p>
                <p className="text-white text-xl">{successRate.toFixed(1)}%</p>
              </div>
              <div>
                <p className="text-gray-400 text-xs">Successful</p>
                <p className="text-white text-xl">{metrics.successfulCalls}</p>
              </div>
              <div>
                <p className="text-gray-400 text-xs">Failed</p>
                <p className="text-white text-xl">{metrics.failedCalls}</p>
              </div>
            </div>

            <div className="mt-4">
              <div className="flex justify-between mb-1">
                <span className="text-xs text-gray-400">Success Rate</span>
                <span className="text-xs text-gray-400">
                  {successRate.toFixed(1)}%
                </span>
              </div>
              <Progress value={successRate} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-base flex items-center">
              <BarChart2 className="h-4 w-4 mr-2 text-teal-400" />
              Performance Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-xs text-gray-400">
                    Avg. Call Duration
                  </span>
                  <span className="text-xs text-gray-400">
                    {Math.floor(metrics.averageDuration / 60)}m{" "}
                    {metrics.averageDuration % 60}s
                  </span>
                </div>
                <Progress
                  value={(metrics.averageDuration / 180) * 100}
                  className="h-2"
                />
              </div>

              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-xs text-gray-400">Sentiment Score</span>
                  <span className="text-xs text-gray-400">
                    {(metrics.averageSentiment * 100).toFixed(0)}%
                  </span>
                </div>
                <Progress
                  value={metrics.averageSentiment * 100}
                  className="h-2"
                />
              </div>

              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-xs text-gray-400">Active Calls</span>
                  <span className="text-xs text-gray-400">
                    {Math.floor(Math.random() * 3)}
                  </span>
                </div>
                <div className="flex space-x-1 mt-1">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <div
                      key={i}
                      className={`h-2 flex-1 rounded ${
                        i < Math.floor(Math.random() * 3)
                          ? "bg-teal-500 animate-pulse"
                          : "bg-gray-700"
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="bg-gray-800 border-gray-700">
        <CardHeader className="pb-2">
          <CardTitle className="text-white text-base flex items-center">
            <PhoneOff className="h-4 w-4 mr-2 text-orange-400" />
            Call Failure Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {metrics.failedCalls > 0 ? (
              <>
                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-xs text-gray-400">No Answer</span>
                    <span className="text-xs text-gray-400">
                      {Math.floor(metrics.failedCalls * 0.6)} calls
                    </span>
                  </div>
                  <Progress value={60} className="h-2" />
                </div>

                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-xs text-gray-400">Busy</span>
                    <span className="text-xs text-gray-400">
                      {Math.floor(metrics.failedCalls * 0.3)} calls
                    </span>
                  </div>
                  <Progress value={30} className="h-2" />
                </div>

                <div>
                  <div className="flex justify-between mb-1">
                    <span className="text-xs text-gray-400">
                      Technical Issues
                    </span>
                    <span className="text-xs text-gray-400">
                      {Math.floor(metrics.failedCalls * 0.1)} calls
                    </span>
                  </div>
                  <Progress value={10} className="h-2" />
                </div>
              </>
            ) : (
              <div className="text-center py-2 text-gray-400">
                No failed calls to analyze
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
