import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { CampaignTeamMember } from "@/types/schema";
import { UserPlus, X, Check, Trash2 } from "lucide-react";

interface CampaignTeamProps {
  campaignId: string;
  teamMembers: CampaignTeamMember[];
  onAddTeamMember: (
    campaignId: string,
    email: string,
    role: string
  ) => Promise<any>;
  onRemoveTeamMember: (
    campaignId: string,
    memberId: string
  ) => Promise<boolean>;
}

const CampaignTeam: React.FC<CampaignTeamProps> = ({
  campaignId,
  teamMembers,
  onAddTeamMember,
  onRemoveTeamMember,
}) => {
  const [isAdding, setIsAdding] = useState(false);
  const [email, setEmail] = useState("");
  const [role, setRole] = useState("VIEWER");
  const [isLoading, setIsLoading] = useState(false);

  const handleAddMember = async () => {
    if (!email) {
      alert("Please enter an email address");
      return;
    }

    setIsLoading(true);
    try {
      await onAddTeamMember(campaignId, email, role);
      setEmail("");
      setRole("VIEWER");
      setIsAdding(false);
    } catch (error) {
      console.error("Error adding team member:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    if (window.confirm("Are you sure you want to remove this team member?")) {
      setIsLoading(true);
      try {
        await onRemoveTeamMember(campaignId, memberId);
      } catch (error) {
        console.error("Error removing team member:", error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "OWNER":
        return "bg-purple-100 text-purple-800";
      case "ADMIN":
        return "bg-red-100 text-red-800";
      case "EDITOR":
        return "bg-blue-100 text-blue-800";
      case "VIEWER":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "ACCEPTED":
        return "bg-green-100 text-green-800";
      case "PENDING":
        return "bg-yellow-100 text-yellow-800";
      case "DECLINED":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Team Members</CardTitle>
          <CardDescription>
            Manage who has access to this campaign
          </CardDescription>
        </div>
        {!isAdding ? (
          <Button onClick={() => setIsAdding(true)}>
            <UserPlus className="h-4 w-4 mr-1" /> Add Member
          </Button>
        ) : (
          <Button variant="ghost" onClick={() => setIsAdding(false)}>
            <X className="h-4 w-4" />
          </Button>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        {isAdding && (
          <div className="border rounded-md p-4 space-y-3">
            <h3 className="font-medium">Add Team Member</h3>
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium">Email</label>
                <Input
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  type="email"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Role</label>
                <Select value={role} onValueChange={setRole}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ADMIN">Admin</SelectItem>
                    <SelectItem value="EDITOR">Editor</SelectItem>
                    <SelectItem value="VIEWER">Viewer</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button
                onClick={handleAddMember}
                disabled={isLoading || !email}
                className="w-full"
              >
                <Check className="h-4 w-4 mr-1" /> Add Member
              </Button>
            </div>
          </div>
        )}

        {teamMembers.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No team members have been added to this campaign.
          </div>
        ) : (
          <div className="space-y-2">
            {teamMembers.map((member) => (
              <div
                key={member.id}
                className="flex items-center justify-between border rounded-md p-3"
              >
                <div className="flex items-center space-x-3">
                  <Avatar>
                    <AvatarImage
                      src={`https://api.dicebear.com/7.x/initials/svg?seed=${member.user.name}`}
                    />
                    <AvatarFallback>
                      {getInitials(member.user.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{member.user.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {member.user.email}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div
                    className={`text-xs px-2 py-1 rounded-full ${getRoleColor(
                      member.role
                    )}`}
                  >
                    {member.role}
                  </div>
                  <div
                    className={`text-xs px-2 py-1 rounded-full ${getStatusColor(
                      member.status
                    )}`}
                  >
                    {member.status}
                  </div>
                  {member.role !== "OWNER" && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveMember(member.id)}
                      disabled={isLoading}
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CampaignTeam;
