import React, { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Campaign, Contact, CampaignGoal, CampaignTemplate } from "@/types/schema";
import { format } from "date-fns";
import { toast } from "@/components/ui/use-toast";
import { Check, X } from "lucide-react";

interface CampaignFormProps {
  campaign?: Campaign;
  contacts: Contact[];
  onSubmit: (campaignData: any) => Promise<Campaign | null>;
  onCancel: () => void;
  template?: CampaignTemplate | null;
}

const CampaignForm: React.FC<CampaignFormProps> = ({
  campaign,
  contacts,
  onSubmit,
  onCancel,
  template,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    id: "",
    name: "",
    description: "",
    startDate: format(new Date(), "yyyy-MM-dd"),
    endDate: "",
    status: "DRAFT",
    phoneNumber: "",
    assistant: "",
  });
  const [selectedContactIds, setSelectedContactIds] = useState<string[]>([]);
  const [campaignGoals, setCampaignGoals] = useState<CampaignGoal[]>([]);
  const [activeTab, setActiveTab] = useState("details");

  // Initialize form with campaign data if editing
  useEffect(() => {
    if (campaign) {
      const startDate = campaign.startDate instanceof Date
        ? campaign.startDate
        : new Date(campaign.startDate);
      
      const endDate = campaign.endDate
        ? campaign.endDate instanceof Date
          ? campaign.endDate
          : new Date(campaign.endDate)
        : null;
      
      setFormData({
        id: campaign.id,
        name: campaign.name,
        description: campaign.description || "",
        startDate: format(startDate, "yyyy-MM-dd"),
        endDate: endDate ? format(endDate, "yyyy-MM-dd") : "",
        status: campaign.status,
        phoneNumber: campaign.phoneNumberId || "",
        assistant: campaign.assistantId || "",
      });
      
      setSelectedContactIds(campaign.contacts.map(contact => contact.id));
      setCampaignGoals(campaign.goals || []);
    }
  }, [campaign]);

  // Initialize form with template data if provided
  useEffect(() => {
    if (template) {
      const templateData = template.campaignData;
      
      setFormData(prev => ({
        ...prev,
        name: template.name,
        description: templateData.description || prev.description,
      }));
      
      if (templateData.goals && Array.isArray(templateData.goals)) {
        setCampaignGoals(templateData.goals.map((goal: any) => ({
          id: `temp-${Date.now()}-${Math.random()}`,
          campaignId: "",
          title: goal.title,
          description: goal.description || "",
          target: goal.target || 0,
          progress: 0,
          completed: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        })));
      }
      
      toast({
        title: "Template Applied",
        description: `Template "${template.name}" has been applied to the campaign.`,
      });
    }
  }, [template]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.startDate) {
      toast({
        title: "Validation Error",
        description: "Name and start date are required",
        variant: "destructive",
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      const campaignData = {
        id: formData.id,
        name: formData.name,
        description: formData.description,
        startDate: new Date(formData.startDate),
        endDate: formData.endDate ? new Date(formData.endDate) : undefined,
        status: formData.status,
        contacts: selectedContactIds,
        goals: campaignGoals,
        phoneNumberId: formData.phoneNumber || undefined,
        assistantId: formData.assistant || undefined,
      };
      
      const result = await onSubmit(campaignData);
      
      if (result) {
        toast({
          title: "Success",
          description: `Campaign ${formData.id ? "updated" : "created"} successfully`,
        });
        onCancel();
      }
    } catch (error) {
      console.error("Error submitting campaign:", error);
      toast({
        title: "Error",
        description: `Failed to ${formData.id ? "update" : "create"} campaign`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleContactToggle = (contactId: string) => {
    setSelectedContactIds(prev => 
      prev.includes(contactId)
        ? prev.filter(id => id !== contactId)
        : [...prev, contactId]
    );
  };

  const handleAddGoal = () => {
    const newGoal: CampaignGoal = {
      id: `temp-${Date.now()}`,
      campaignId: formData.id || "",
      title: "",
      description: "",
      target: 0,
      progress: 0,
      completed: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    setCampaignGoals([...campaignGoals, newGoal]);
  };

  const handleGoalChange = (index: number, field: keyof CampaignGoal, value: any) => {
    const updatedGoals = [...campaignGoals];
    updatedGoals[index] = {
      ...updatedGoals[index],
      [field]: field === 'target' || field === 'progress' ? parseInt(value) || 0 : value,
    };
    setCampaignGoals(updatedGoals);
  };

  const handleRemoveGoal = (index: number) => {
    const updatedGoals = [...campaignGoals];
    updatedGoals.splice(index, 1);
    setCampaignGoals(updatedGoals);
  };

  return (
    <form onSubmit={handleSubmit}>
      <Card>
        <CardHeader>
          <CardTitle>{formData.id ? "Edit Campaign" : "Create New Campaign"}</CardTitle>
          <CardDescription>
            {formData.id
              ? "Update your campaign details"
              : "Set up a new campaign to reach your contacts"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="details" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="contacts">Contacts</TabsTrigger>
              <TabsTrigger value="goals">Goals</TabsTrigger>
            </TabsList>
            
            <TabsContent value="details" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Campaign Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter campaign name"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Describe the purpose of this campaign"
                  rows={3}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="endDate">End Date (Optional)</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => setFormData({ ...formData, status: value })}
                >
                  <SelectTrigger id="status">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="DRAFT">Draft</SelectItem>
                    <SelectItem value="SCHEDULED">Scheduled</SelectItem>
                    <SelectItem value="ACTIVE">Active</SelectItem>
                    <SelectItem value="PAUSED">Paused</SelectItem>
                    <SelectItem value="COMPLETED">Completed</SelectItem>
                    <SelectItem value="CANCELLED">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="assistant">Assistant</Label>
                  <Input
                    id="assistant"
                    value={formData.assistant}
                    onChange={(e) => setFormData({ ...formData, assistant: e.target.value })}
                    placeholder="Select an assistant"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    value={formData.phoneNumber}
                    onChange={(e) => setFormData({ ...formData, phoneNumber: e.target.value })}
                    placeholder="Select a phone number"
                  />
                </div>
              </div>
            </TabsContent>
            
            <TabsContent value="contacts" className="space-y-4">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-medium">Select Contacts</h3>
                <div className="text-sm text-muted-foreground">
                  {selectedContactIds.length} contacts selected
                </div>
              </div>
              
              <div className="border rounded-md p-4 max-h-[400px] overflow-y-auto">
                {contacts.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No contacts available. Please add contacts first.
                  </div>
                ) : (
                  <div className="space-y-2">
                    {contacts.map((contact) => (
                      <div
                        key={contact.id}
                        className="flex items-center space-x-2 p-2 hover:bg-muted rounded-md"
                      >
                        <input
                          type="checkbox"
                          id={`contact-${contact.id}`}
                          checked={selectedContactIds.includes(contact.id)}
                          onChange={() => handleContactToggle(contact.id)}
                          className="rounded"
                        />
                        <label
                          htmlFor={`contact-${contact.id}`}
                          className="flex-1 cursor-pointer"
                        >
                          <div className="font-medium">{contact.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {contact.email} • {contact.phone}
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="goals" className="space-y-4">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-medium">Campaign Goals</h3>
                <Button type="button" variant="outline" onClick={handleAddGoal}>
                  Add Goal
                </Button>
              </div>
              
              {campaignGoals.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground border rounded-md">
                  No goals defined. Add goals to track campaign progress.
                </div>
              ) : (
                <div className="space-y-4">
                  {campaignGoals.map((goal, index) => (
                    <div key={goal.id} className="border rounded-md p-4 space-y-3">
                      <div className="flex justify-between">
                        <h4 className="font-medium">Goal {index + 1}</h4>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveGoal(index)}
                        >
                          <X className="h-4 w-4 text-red-500" />
                        </Button>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor={`goal-title-${index}`}>Title</Label>
                        <Input
                          id={`goal-title-${index}`}
                          value={goal.title}
                          onChange={(e) => handleGoalChange(index, "title", e.target.value)}
                          placeholder="Goal title"
                        />
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor={`goal-desc-${index}`}>Description</Label>
                        <Textarea
                          id={`goal-desc-${index}`}
                          value={goal.description || ""}
                          onChange={(e) => handleGoalChange(index, "description", e.target.value)}
                          placeholder="Goal description"
                          rows={2}
                        />
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`goal-target-${index}`}>Target</Label>
                          <Input
                            id={`goal-target-${index}`}
                            type="number"
                            value={goal.target}
                            onChange={(e) => handleGoalChange(index, "target", e.target.value)}
                            min={0}
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <Label htmlFor={`goal-progress-${index}`}>Progress</Label>
                          <Input
                            id={`goal-progress-${index}`}
                            type="number"
                            value={goal.progress}
                            onChange={(e) => handleGoalChange(index, "progress", e.target.value)}
                            min={0}
                            max={goal.target}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </span>
            ) : (
              <span className="flex items-center">
                <Check className="h-4 w-4 mr-1" />
                {formData.id ? "Update Campaign" : "Create Campaign"}
              </span>
            )}
          </Button>
        </CardFooter>
      </Card>
    </form>
  );
};

export default CampaignForm;
