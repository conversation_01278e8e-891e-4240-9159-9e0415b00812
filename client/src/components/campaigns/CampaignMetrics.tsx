import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Campaign } from "@/types/schema";
import { format } from "date-fns";

interface CampaignMetricsProps {
  campaign: Campaign;
}

export function CampaignMetrics({ campaign }: CampaignMetricsProps) {
  // Calculate success rate
  const successRate =
    campaign.metrics.totalCalls > 0
      ? (campaign.metrics.successfulCalls / campaign.metrics.totalCalls) * 100
      : 0;

  // Format dates
  const startDate =
    campaign.startDate instanceof Date
      ? format(campaign.startDate, "MMM dd, yyyy")
      : format(new Date(campaign.startDate), "MMM dd, yyyy");

  const endDate = campaign.endDate
    ? campaign.endDate instanceof Date
      ? format(campaign.endDate, "MMM dd, yyyy")
      : format(new Date(campaign.endDate), "MMM dd, yyyy")
    : "Ongoing";

  // Calculate campaign progress
  const calculateProgress = () => {
    if (!campaign.endDate) return 0;

    const start = new Date(campaign.startDate).getTime();
    const end = new Date(campaign.endDate).getTime();
    const now = new Date().getTime();

    if (now >= end) return 100;
    if (now <= start) return 0;

    return Math.round(((now - start) / (end - start)) * 100);
  };

  const campaignProgress = calculateProgress();

  // Calculate goal progress
  const goalProgress =
    campaign.goals.length > 0
      ? (campaign.goals.reduce(
          (acc, goal) => acc + goal.progress / goal.target,
          0
        ) /
          campaign.goals.length) *
        100
      : 0;

  return (
    <div className="space-y-4">
      <Card className="bg-gray-800 border-gray-700">
        <CardHeader className="pb-2">
          <CardTitle className="text-white text-lg">
            Campaign Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-gray-400 text-sm">Start Date</p>
              <p className="text-white">{startDate}</p>
            </div>
            <div>
              <p className="text-gray-400 text-sm">End Date</p>
              <p className="text-white">{endDate}</p>
            </div>
            <div>
              <p className="text-gray-400 text-sm">Status</p>
              <p className="text-white capitalize">{campaign.status}</p>
            </div>
            <div>
              <p className="text-gray-400 text-sm">Contacts</p>
              <p className="text-white">{campaign.contacts?.length || 0}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gray-800 border-gray-700">
        <CardHeader className="pb-2">
          <CardTitle className="text-white text-lg">
            Campaign Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-400">Timeline</span>
                <span className="text-sm text-gray-400">
                  {campaignProgress}%
                </span>
              </div>
              <Progress value={campaignProgress} className="h-2" />
            </div>

            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-400">Goals</span>
                <span className="text-sm text-gray-400">
                  {Math.round(goalProgress)}%
                </span>
              </div>
              <Progress value={goalProgress} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="bg-gray-800 border-gray-700">
        <CardHeader className="pb-2">
          <CardTitle className="text-white text-lg">Call Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-gray-400 text-sm">Total Calls</p>
              <p className="text-white text-xl">
                {campaign.metrics.totalCalls}
              </p>
            </div>
            <div>
              <p className="text-gray-400 text-sm">Success Rate</p>
              <p className="text-white text-xl">{successRate.toFixed(1)}%</p>
            </div>
            <div>
              <p className="text-gray-400 text-sm">Successful Calls</p>
              <p className="text-white text-xl">
                {campaign.metrics.successfulCalls}
              </p>
            </div>
            <div>
              <p className="text-gray-400 text-sm">Failed Calls</p>
              <p className="text-white text-xl">
                {campaign.metrics.failedCalls}
              </p>
            </div>
          </div>

          <div className="mt-4">
            <div className="flex justify-between mb-1">
              <span className="text-sm text-gray-400">Success Rate</span>
              <span className="text-sm text-gray-400">
                {successRate.toFixed(1)}%
              </span>
            </div>
            <Progress value={successRate} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {campaign.goals.length > 0 && (
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-lg">Campaign Goals</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {campaign.goals.map((goal, index) => {
                const goalPercentage = (goal.progress / goal.target) * 100;
                return (
                  <div key={goal.id || index}>
                    <div className="flex justify-between mb-1">
                      <span className="text-sm text-white">{goal.title}</span>
                      <span className="text-sm text-gray-400">
                        {goal.progress} / {goal.target} (
                        {Math.round(goalPercentage)}%)
                      </span>
                    </div>
                    <Progress value={goalPercentage} className="h-2" />
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
