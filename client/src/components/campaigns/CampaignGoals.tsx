import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { CampaignGoal } from "@/types/schema";
import { Trash2, Plus, Check, X } from "lucide-react";

interface CampaignGoalsProps {
  campaignId: string;
  goals: CampaignGoal[];
  onUpdateGoals: (
    campaignId: string,
    goals: CampaignGoal[]
  ) => Promise<CampaignGoal[] | null>;
}

const CampaignGoals: React.FC<CampaignGoalsProps> = ({
  campaignId,
  goals,
  onUpdateGoals,
}) => {
  const [editingGoals, setEditingGoals] = useState<CampaignGoal[]>(goals);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleAddGoal = () => {
    const newGoal: CampaignGoal = {
      id: `temp-${Date.now()}`,
      campaignId,
      title: "",
      description: "",
      target: 0,
      progress: 0,
      completed: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    setEditingGoals([...editingGoals, newGoal]);
  };

  const handleRemoveGoal = (index: number) => {
    const updatedGoals = [...editingGoals];
    updatedGoals.splice(index, 1);
    setEditingGoals(updatedGoals);
  };

  const handleGoalChange = (
    index: number,
    field: keyof CampaignGoal,
    value: any
  ) => {
    const updatedGoals = [...editingGoals];
    updatedGoals[index] = {
      ...updatedGoals[index],
      [field]:
        field === "target" || field === "progress"
          ? parseInt(value) || 0
          : value,
    };
    setEditingGoals(updatedGoals);
  };

  const handleSaveGoals = async () => {
    setIsLoading(true);
    try {
      // Validate goals
      const validGoals = editingGoals.filter(
        (goal) => goal.title.trim() !== "" && goal.target > 0
      );

      if (validGoals.length === 0) {
        alert(
          "Please add at least one valid goal with a title and target value."
        );
        setIsLoading(false);
        return;
      }

      const result = await onUpdateGoals(campaignId, validGoals);
      if (result) {
        setEditingGoals(result);
        setIsEditing(false);
      }
    } catch (error) {
      console.error("Error saving goals:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingGoals(goals);
    setIsEditing(false);
  };

  const calculateProgress = (goal: CampaignGoal) => {
    return goal.target > 0
      ? Math.round((goal.progress / goal.target) * 100)
      : 0;
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Campaign Goals</CardTitle>
          <CardDescription>
            Track and manage your campaign objectives
          </CardDescription>
        </div>
        {!isEditing ? (
          <Button onClick={() => setIsEditing(true)}>Edit Goals</Button>
        ) : (
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={handleCancelEdit}
              disabled={isLoading}
            >
              <X className="h-4 w-4 mr-1" /> Cancel
            </Button>
            <Button onClick={handleSaveGoals} disabled={isLoading}>
              <Check className="h-4 w-4 mr-1" /> Save
            </Button>
          </div>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        {isEditing ? (
          <div className="space-y-4">
            {editingGoals.map((goal, index) => (
              <div key={goal.id} className="border rounded-md p-4 space-y-3">
                <div className="flex justify-between">
                  <h3 className="font-medium">Goal {index + 1}</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveGoal(index)}
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </Button>
                </div>
                <div className="space-y-2">
                  <div>
                    <label className="text-sm font-medium">Title</label>
                    <Input
                      value={goal.title}
                      onChange={(e) =>
                        handleGoalChange(index, "title", e.target.value)
                      }
                      placeholder="Goal title"
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Description</label>
                    <Textarea
                      value={goal.description || ""}
                      onChange={(e) =>
                        handleGoalChange(index, "description", e.target.value)
                      }
                      placeholder="Goal description"
                      rows={2}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Target</label>
                      <Input
                        type="number"
                        value={goal.target}
                        onChange={(e) =>
                          handleGoalChange(index, "target", e.target.value)
                        }
                        min={0}
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Progress</label>
                      <Input
                        type="number"
                        value={goal.progress}
                        onChange={(e) =>
                          handleGoalChange(index, "progress", e.target.value)
                        }
                        min={0}
                        max={goal.target}
                      />
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`completed-${index}`}
                      checked={goal.completed}
                      onChange={(e) =>
                        handleGoalChange(index, "completed", e.target.checked)
                      }
                      className="rounded"
                    />
                    <label htmlFor={`completed-${index}`} className="text-sm">
                      Mark as completed
                    </label>
                  </div>
                </div>
              </div>
            ))}
            <Button
              variant="outline"
              className="w-full"
              onClick={handleAddGoal}
            >
              <Plus className="h-4 w-4 mr-1" /> Add Goal
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {goals.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No goals have been set for this campaign.
              </div>
            ) : (
              goals.map((goal) => (
                <div key={goal.id} className="border rounded-md p-4 space-y-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">{goal.title}</h3>
                      {goal.description && (
                        <p className="text-sm text-muted-foreground">
                          {goal.description}
                        </p>
                      )}
                    </div>
                    {goal.completed && (
                      <div className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                        Completed
                      </div>
                    )}
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between text-sm">
                      <span>
                        Progress: {goal.progress} / {goal.target}
                      </span>
                      <span>{calculateProgress(goal)}%</span>
                    </div>
                    <Progress value={calculateProgress(goal)} className="h-2" />
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CampaignGoals;
