import React, { useState, useRef } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Download, Upload, AlertCircle, Check } from 'lucide-react';
import { Contact } from '@/types/schema';
import { toast } from '@/components/ui/use-toast';

interface CsvImportExportProps {
  onContactsImported: (contacts: Partial<Contact>[]) => void;
  contacts?: Contact[];
}

export function CsvImportExport({ onContactsImported, contacts = [] }: CsvImportExportProps) {
  const [isImporting, setIsImporting] = useState(false);
  const [importError, setImportError] = useState<string | null>(null);
  const [importSuccess, setImportSuccess] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Handle CSV file import
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    setIsImporting(true);
    setImportError(null);
    setImportSuccess(null);
    
    const reader = new FileReader();
    
    reader.onload = (event) => {
      try {
        const csvData = event.target?.result as string;
        const importedContacts = parseCSV(csvData);
        
        if (importedContacts.length === 0) {
          setImportError('No valid contacts found in the CSV file');
          setIsImporting(false);
          return;
        }
        
        onContactsImported(importedContacts);
        setImportSuccess(`Successfully imported ${importedContacts.length} contacts`);
        
        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } catch (error) {
        setImportError('Failed to parse CSV file. Please check the format.');
        console.error('CSV import error:', error);
      } finally {
        setIsImporting(false);
      }
    };
    
    reader.onerror = () => {
      setImportError('Error reading the file');
      setIsImporting(false);
    };
    
    reader.readAsText(file);
  };
  
  // Parse CSV data
  const parseCSV = (csvData: string): Partial<Contact>[] => {
    const lines = csvData.split('\n');
    if (lines.length <= 1) {
      throw new Error('CSV file is empty or has only headers');
    }
    
    const headers = lines[0].split(',').map(header => header.trim());
    const requiredHeaders = ['name', 'email', 'phone'];
    
    // Check if required headers exist
    for (const required of requiredHeaders) {
      if (!headers.includes(required)) {
        throw new Error(`CSV file is missing required header: ${required}`);
      }
    }
    
    const contacts: Partial<Contact>[] = [];
    
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;
      
      const values = line.split(',').map(value => value.trim());
      if (values.length !== headers.length) continue;
      
      const contact: Record<string, any> = {};
      
      headers.forEach((header, index) => {
        contact[header] = values[index];
      });
      
      // Set default values for type if not provided
      if (!contact.type) {
        contact.type = 'PERSONAL';
      }
      
      contacts.push(contact as Partial<Contact>);
    }
    
    return contacts;
  };
  
  // Generate CSV template
  const generateCsvTemplate = () => {
    const headers = ['name', 'email', 'phone', 'type', 'subcategory', 'notes'];
    const csvContent = headers.join(',') + '\n';
    
    // Create a blob and download it
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'contacts_template.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  // Export contacts to CSV
  const exportContactsToCsv = () => {
    if (contacts.length === 0) {
      toast({
        title: 'No contacts to export',
        description: 'There are no contacts available to export.',
        variant: 'destructive',
      });
      return;
    }
    
    const headers = ['name', 'email', 'phone', 'type', 'subcategory', 'notes'];
    let csvContent = headers.join(',') + '\n';
    
    contacts.forEach(contact => {
      const row = [
        contact.name || '',
        contact.email || '',
        contact.phone || '',
        contact.type || '',
        contact.subcategory || '',
        contact.notes || ''
      ].map(value => `"${value.replace(/"/g, '""')}"`).join(',');
      
      csvContent += row + '\n';
    });
    
    // Create a blob and download it
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'contacts_export.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast({
      title: 'Contacts exported',
      description: `Successfully exported ${contacts.length} contacts to CSV.`,
    });
  };
  
  return (
    <div className="space-y-4">
      <div className="flex space-x-2">
        <Button
          className="bg-teal-600 hover:bg-teal-700 text-white"
          onClick={generateCsvTemplate}
        >
          <Download className="w-4 h-4 mr-2" />
          Download CSV Template
        </Button>
        
        {contacts.length > 0 && (
          <Button
            className="bg-teal-600 hover:bg-teal-700 text-white"
            onClick={exportContactsToCsv}
          >
            <Download className="w-4 h-4 mr-2" />
            Export Contacts
          </Button>
        )}
      </div>
      
      <div className="bg-gray-700 p-4 rounded-md">
        <Label htmlFor="csv-upload" className="text-white mb-2 block">
          Import Contacts from CSV
        </Label>
        <div className="flex items-center space-x-2">
          <Input
            id="csv-upload"
            type="file"
            accept=".csv"
            ref={fileInputRef}
            onChange={handleFileChange}
            className="bg-gray-800 border-gray-600 text-white"
            disabled={isImporting}
          />
          <Button
            className="bg-teal-600 hover:bg-teal-700 text-white"
            disabled={isImporting}
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="w-4 h-4 mr-2" />
            {isImporting ? 'Importing...' : 'Import'}
          </Button>
        </div>
        
        {importError && (
          <Alert variant="destructive" className="mt-2">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{importError}</AlertDescription>
          </Alert>
        )}
        
        {importSuccess && (
          <Alert className="mt-2 bg-green-800 border-green-700">
            <Check className="h-4 w-4 text-green-400" />
            <AlertDescription className="text-green-200">{importSuccess}</AlertDescription>
          </Alert>
        )}
        
        <div className="mt-2 text-sm text-gray-400">
          CSV file must include headers: name, email, phone
        </div>
      </div>
    </div>
  );
}
