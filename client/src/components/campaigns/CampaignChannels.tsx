import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ChannelSettings } from "@/types/schema";
import { Phone, MessageSquare, Mail, Save, X } from "lucide-react";

interface CampaignChannelsProps {
  campaignId: string;
  channelSettings?: ChannelSettings | null;
  onUpdateChannels: (
    campaignId: string,
    channelSettings: Partial<ChannelSettings>
  ) => Promise<any>;
}

const CampaignChannels: React.FC<CampaignChannelsProps> = ({
  campaignId,
  channelSettings,
  onUpdateChannels,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [settings, setSettings] = useState<Partial<ChannelSettings>>({
    voice: {
      enabled: false,
      message: "",
      delay: 0,
    },
    sms: {
      enabled: false,
      message: "",
      delay: 0,
      sendTime: "",
    },
    email: {
      enabled: false,
      template: "",
      delay: 0,
      sendTime: "",
    },
  });

  useEffect(() => {
    if (channelSettings) {
      setSettings({
        voice: channelSettings.voice || {
          enabled: false,
          message: "",
          delay: 0,
        },
        sms: channelSettings.sms || {
          enabled: false,
          message: "",
          delay: 0,
          sendTime: "",
        },
        email: channelSettings.email || {
          enabled: false,
          template: "",
          delay: 0,
          sendTime: "",
        },
      });
    }
  }, [channelSettings]);

  const handleToggleChannel = (
    channel: "voice" | "sms" | "email",
    enabled: boolean
  ) => {
    setSettings({
      ...settings,
      [channel]: {
        ...(settings[channel] || {}),
        enabled,
      },
    });
  };

  const handleChangeChannelSetting = (
    channel: "voice" | "sms" | "email",
    field: string,
    value: any
  ) => {
    setSettings({
      ...settings,
      [channel]: {
        ...(settings[channel] || {}),
        [field]: value,
      },
    });
  };

  const handleSaveSettings = async () => {
    setIsLoading(true);
    try {
      await onUpdateChannels(campaignId, settings);
      setIsEditing(false);
    } catch (error) {
      console.error("Error saving channel settings:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelEdit = () => {
    if (channelSettings) {
      setSettings({
        voice: channelSettings.voice || {
          enabled: false,
          message: "",
          delay: 0,
        },
        sms: channelSettings.sms || {
          enabled: false,
          message: "",
          delay: 0,
          sendTime: "",
        },
        email: channelSettings.email || {
          enabled: false,
          template: "",
          delay: 0,
          sendTime: "",
        },
      });
    }
    setIsEditing(false);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Communication Channels</CardTitle>
          <CardDescription>
            Configure how to reach your contacts
          </CardDescription>
        </div>
        {!isEditing ? (
          <Button onClick={() => setIsEditing(true)}>Edit Channels</Button>
        ) : (
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={handleCancelEdit}
              disabled={isLoading}
            >
              <X className="h-4 w-4 mr-1" /> Cancel
            </Button>
            <Button onClick={handleSaveSettings} disabled={isLoading}>
              <Save className="h-4 w-4 mr-1" /> Save
            </Button>
          </div>
        )}
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="voice">
          <TabsList className="grid grid-cols-3 w-full">
            <TabsTrigger value="voice">
              <Phone className="h-4 w-4 mr-1" /> Voice
            </TabsTrigger>
            <TabsTrigger value="sms">
              <MessageSquare className="h-4 w-4 mr-1" /> SMS
            </TabsTrigger>
            <TabsTrigger value="email">
              <Mail className="h-4 w-4 mr-1" /> Email
            </TabsTrigger>
          </TabsList>

          <TabsContent value="voice" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="voice-enabled">Enable Voice Channel</Label>
              <Switch
                id="voice-enabled"
                checked={settings.voice?.enabled || false}
                onCheckedChange={(checked) =>
                  handleToggleChannel("voice", checked)
                }
                disabled={!isEditing}
              />
            </div>

            {(settings.voice?.enabled || isEditing) && (
              <div className="space-y-3">
                <div>
                  <Label htmlFor="voice-message">Voice Message Script</Label>
                  <Textarea
                    id="voice-message"
                    value={settings.voice?.message || ""}
                    onChange={(e) =>
                      handleChangeChannelSetting(
                        "voice",
                        "message",
                        e.target.value
                      )
                    }
                    placeholder="Hello {contact.name}, this is {user.name} from {user.company}..."
                    rows={4}
                    disabled={!isEditing}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Use {"{contact.name}"}, {"{user.name}"}, {"{user.company}"}{" "}
                    as placeholders
                  </p>
                </div>

                <div>
                  <Label htmlFor="voice-delay">Delay (days)</Label>
                  <Input
                    id="voice-delay"
                    type="number"
                    value={settings.voice?.delay || 0}
                    onChange={(e) =>
                      handleChangeChannelSetting(
                        "voice",
                        "delay",
                        parseInt(e.target.value) || 0
                      )
                    }
                    min={0}
                    disabled={!isEditing}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Days to wait before making the call
                  </p>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="sms" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="sms-enabled">Enable SMS Channel</Label>
              <Switch
                id="sms-enabled"
                checked={settings.sms?.enabled || false}
                onCheckedChange={(checked) =>
                  handleToggleChannel("sms", checked)
                }
                disabled={!isEditing}
              />
            </div>

            {(settings.sms?.enabled || isEditing) && (
              <div className="space-y-3">
                <div>
                  <Label htmlFor="sms-message">SMS Message</Label>
                  <Textarea
                    id="sms-message"
                    value={settings.sms?.message || ""}
                    onChange={(e) =>
                      handleChangeChannelSetting(
                        "sms",
                        "message",
                        e.target.value
                      )
                    }
                    placeholder="Hi {contact.name}, this is {user.name} from {user.company}..."
                    rows={3}
                    disabled={!isEditing}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Use {"{contact.name}"}, {"{user.name}"}, {"{user.company}"}{" "}
                    as placeholders
                  </p>
                </div>

                <div>
                  <Label htmlFor="sms-delay">Delay (days)</Label>
                  <Input
                    id="sms-delay"
                    type="number"
                    value={settings.sms?.delay || 0}
                    onChange={(e) =>
                      handleChangeChannelSetting(
                        "sms",
                        "delay",
                        parseInt(e.target.value) || 0
                      )
                    }
                    min={0}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <Label htmlFor="sms-time">Send Time</Label>
                  <Input
                    id="sms-time"
                    type="time"
                    value={settings.sms?.sendTime || ""}
                    onChange={(e) =>
                      handleChangeChannelSetting(
                        "sms",
                        "sendTime",
                        e.target.value
                      )
                    }
                    disabled={!isEditing}
                  />
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="email" className="space-y-4 mt-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="email-enabled">Enable Email Channel</Label>
              <Switch
                id="email-enabled"
                checked={settings.email?.enabled || false}
                onCheckedChange={(checked) =>
                  handleToggleChannel("email", checked)
                }
                disabled={!isEditing}
              />
            </div>

            {(settings.email?.enabled || isEditing) && (
              <div className="space-y-3">
                <div>
                  <Label htmlFor="email-template">Email Template</Label>
                  <Input
                    id="email-template"
                    value={settings.email?.template || ""}
                    onChange={(e) =>
                      handleChangeChannelSetting(
                        "email",
                        "template",
                        e.target.value
                      )
                    }
                    placeholder="sales_outreach_email"
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <Label htmlFor="email-delay">Delay (days)</Label>
                  <Input
                    id="email-delay"
                    type="number"
                    value={settings.email?.delay || 0}
                    onChange={(e) =>
                      handleChangeChannelSetting(
                        "email",
                        "delay",
                        parseInt(e.target.value) || 0
                      )
                    }
                    min={0}
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <Label htmlFor="email-time">Send Time</Label>
                  <Input
                    id="email-time"
                    type="time"
                    value={settings.email?.sendTime || ""}
                    onChange={(e) =>
                      handleChangeChannelSetting(
                        "email",
                        "sendTime",
                        e.target.value
                      )
                    }
                    disabled={!isEditing}
                  />
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default CampaignChannels;
