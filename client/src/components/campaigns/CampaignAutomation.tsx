import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Campaign } from "@/types/schema";
import {
  Clock,
  Calendar,
  Repeat,
  CheckCircle2,
  Play,
  Pause,
  Settings,
} from "lucide-react";
import { toast } from "@/components/ui/use-toast";

interface CampaignAutomationProps {
  campaign: Campaign;
  onUpdate: (
    campaignId: string,
    data: Partial<Campaign>
  ) => Promise<Campaign | null>;
}

export function CampaignAutomation({
  campaign,
  onUpdate,
}: CampaignAutomationProps) {
  const [isScheduleEnabled, setIsScheduleEnabled] = useState(
    campaign.status === "SCHEDULED"
  );
  const [isRecurringEnabled, setIsRecurringEnabled] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [scheduleDate, setScheduleDate] = useState<string>("");
  const [scheduleTime, setScheduleTime] = useState<string>("");
  const [recurringPattern, setRecurringPattern] = useState<string>("daily");
  const [maxAttempts, setMaxAttempts] = useState<number>(3);
  const [retryDelay, setRetryDelay] = useState<number>(60);
  const [callScript, setCallScript] = useState<string>(
    campaign.callScript || ""
  );

  const handleToggleSchedule = (enabled: boolean) => {
    setIsScheduleEnabled(enabled);

    if (enabled && !scheduleDate) {
      // Set default schedule to tomorrow at 9 AM
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(9, 0, 0, 0);

      setScheduleDate(tomorrow.toISOString().split("T")[0]);
      setScheduleTime("09:00");
    }
  };

  const handleSaveAutomation = async () => {
    setIsUpdating(true);

    try {
      const automationSettings = {
        schedule: isScheduleEnabled
          ? {
              date: scheduleDate,
              time: scheduleTime,
              recurring: isRecurringEnabled
                ? {
                    pattern: recurringPattern,
                  }
                : null,
            }
          : null,
        callSettings: {
          maxAttempts,
          retryDelay,
        },
        callScript,
      };

      const status = isScheduleEnabled ? "SCHEDULED" : campaign.status;

      const result = await onUpdate(campaign.id, {
        status,
        automationSettings,
        callScript,
      });

      if (result) {
        toast({
          title: "Automation settings saved",
          description: "Campaign automation has been updated successfully.",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save automation settings.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleStartCampaign = async () => {
    setIsUpdating(true);

    try {
      const result = await onUpdate(campaign.id, {
        status: "ACTIVE",
      });

      if (result) {
        toast({
          title: "Campaign started",
          description: "Campaign is now active and calls will begin shortly.",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to start campaign.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handlePauseCampaign = async () => {
    setIsUpdating(true);

    try {
      const result = await onUpdate(campaign.id, {
        status: "PAUSED",
      });

      if (result) {
        toast({
          title: "Campaign paused",
          description: "Campaign has been paused. No new calls will be made.",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to pause campaign.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-white">Campaign Automation</h3>
        <div className="flex space-x-2">
          {campaign.status === "ACTIVE" ? (
            <Button
              variant="outline"
              size="sm"
              className="bg-gray-700 border-gray-600 text-white"
              onClick={handlePauseCampaign}
              disabled={isUpdating}
            >
              <Pause className="h-4 w-4 mr-2 text-orange-400" />
              Pause Campaign
            </Button>
          ) : (
            <Button
              variant="outline"
              size="sm"
              className="bg-gray-700 border-gray-600 text-white"
              onClick={handleStartCampaign}
              disabled={
                isUpdating ||
                campaign.status === "COMPLETED" ||
                campaign.status === "CANCELLED"
              }
            >
              <Play className="h-4 w-4 mr-2 text-green-400" />
              Start Campaign
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            className="bg-gray-700 border-gray-600 text-white"
            onClick={handleSaveAutomation}
            disabled={isUpdating}
          >
            <Settings className="h-4 w-4 mr-2 text-teal-400" />
            Save Settings
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-base flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-teal-400" />
              Schedule Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label className="text-white">Enable Scheduling</Label>
                <p className="text-xs text-gray-400">
                  Schedule this campaign to run at a specific time
                </p>
              </div>
              <Switch
                checked={isScheduleEnabled}
                onCheckedChange={handleToggleSchedule}
              />
            </div>

            {isScheduleEnabled && (
              <>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="schedule-date" className="text-white">
                      Start Date
                    </Label>
                    <Input
                      id="schedule-date"
                      type="date"
                      className="bg-gray-700 text-white border-gray-600"
                      value={scheduleDate}
                      onChange={(e) => setScheduleDate(e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="schedule-time" className="text-white">
                      Start Time
                    </Label>
                    <Input
                      id="schedule-time"
                      type="time"
                      className="bg-gray-700 text-white border-gray-600"
                      value={scheduleTime}
                      onChange={(e) => setScheduleTime(e.target.value)}
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label className="text-white">Recurring Schedule</Label>
                    <p className="text-xs text-gray-400">
                      Repeat this campaign on a schedule
                    </p>
                  </div>
                  <Switch
                    checked={isRecurringEnabled}
                    onCheckedChange={setIsRecurringEnabled}
                  />
                </div>

                {isRecurringEnabled && (
                  <div>
                    <Label htmlFor="recurring-pattern" className="text-white">
                      Repeat Pattern
                    </Label>
                    <Select
                      value={recurringPattern}
                      onValueChange={setRecurringPattern}
                    >
                      <SelectTrigger
                        id="recurring-pattern"
                        className="bg-gray-700 text-white border-gray-600"
                      >
                        <SelectValue placeholder="Select pattern" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekdays">Weekdays</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="biweekly">Bi-weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>

        <Card className="bg-gray-800 border-gray-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-base flex items-center">
              <Repeat className="h-4 w-4 mr-2 text-teal-400" />
              Call Settings
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="max-attempts" className="text-white">
                Maximum Call Attempts
              </Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="max-attempts"
                  type="number"
                  min={1}
                  max={10}
                  className="bg-gray-700 text-white border-gray-600"
                  value={maxAttempts}
                  onChange={(e) =>
                    setMaxAttempts(parseInt(e.target.value) || 1)
                  }
                />
                <span className="text-sm text-gray-400">attempts</span>
              </div>
              <p className="text-xs text-gray-400 mt-1">
                Number of times to attempt calling a contact before marking as
                failed
              </p>
            </div>

            <div>
              <Label htmlFor="retry-delay" className="text-white">
                Retry Delay
              </Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="retry-delay"
                  type="number"
                  min={15}
                  max={1440}
                  className="bg-gray-700 text-white border-gray-600"
                  value={retryDelay}
                  onChange={(e) =>
                    setRetryDelay(parseInt(e.target.value) || 60)
                  }
                />
                <span className="text-sm text-gray-400">minutes</span>
              </div>
              <p className="text-xs text-gray-400 mt-1">
                Time to wait before retrying a failed call
              </p>
            </div>

            <div className="pt-2">
              <div className="flex items-center space-x-2 mb-2">
                <Clock className="h-4 w-4 text-teal-400" />
                <Label className="text-white">Call Window</Label>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="start-time" className="text-xs text-gray-400">
                    Start Time
                  </Label>
                  <Input
                    id="start-time"
                    type="time"
                    className="bg-gray-700 text-white border-gray-600"
                    defaultValue="09:00"
                  />
                </div>
                <div>
                  <Label htmlFor="end-time" className="text-xs text-gray-400">
                    End Time
                  </Label>
                  <Input
                    id="end-time"
                    type="time"
                    className="bg-gray-700 text-white border-gray-600"
                    defaultValue="17:00"
                  />
                </div>
              </div>
              <p className="text-xs text-gray-400 mt-1">
                Only make calls during this time window
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card className="bg-gray-800 border-gray-700">
        <CardHeader className="pb-2">
          <CardTitle className="text-white text-base flex items-center">
            <CheckCircle2 className="h-4 w-4 mr-2 text-teal-400" />
            Call Script
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            className="bg-gray-700 text-white border-gray-600 min-h-32"
            placeholder="Enter the script for your AI assistant to follow during calls..."
            value={callScript}
            onChange={(e) => setCallScript(e.target.value)}
          />
          <p className="text-xs text-gray-400 mt-2">
            This script will be used by the AI assistant when making calls. You
            can use variables like {"{contact_name}"}, {"{company_name}"}, etc.
          </p>
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button
          className="bg-teal-600 hover:bg-teal-700 text-white"
          onClick={handleSaveAutomation}
          disabled={isUpdating}
        >
          {isUpdating ? (
            <>
              <Clock className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            "Save Automation Settings"
          )}
        </Button>
      </div>
    </div>
  );
}
