import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Loader2, Save, Copy, Trash2, Plus, FileText } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { Campaign } from "@/types/schema";

interface CampaignTemplate {
  id: string;
  name: string;
  description: string;
  campaignData: Partial<Campaign>;
  isSystem: boolean;
  createdAt: Date | string;
}

interface CampaignTemplatesProps {
  onSelectTemplate: (template: CampaignTemplate) => void;
  currentCampaign: Partial<Campaign>;
}

export function CampaignTemplates({
  onSelectTemplate,
  currentCampaign,
}: CampaignTemplatesProps) {
  const [templates, setTemplates] = useState<CampaignTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [newTemplateName, setNewTemplateName] = useState("");
  const [newTemplateDescription, setNewTemplateDescription] = useState("");

  // Fetch templates from the API
  useEffect(() => {
    const fetchTemplates = async () => {
      setIsLoading(true);
      try {
        const { campaignApi } = await import("@/services/campaignApi");
        const response = await campaignApi.getTemplates();

        if (response.success && response.data) {
          // Ensure the data matches our CampaignTemplate interface
          const formattedTemplates: CampaignTemplate[] = response.data.map(
            (template: any) => ({
              id: template.id,
              name: template.name,
              description: template.description || "",
              campaignData: template.campaignData || {},
              isSystem: template.isSystem || false,
              createdAt: template.createdAt || new Date().toISOString(),
            })
          );
          setTemplates(formattedTemplates);
        } else {
          toast({
            title: "Error",
            description: response.error?.message || "Failed to fetch templates",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error fetching templates:", error);
        toast({
          title: "Error",
          description: "Failed to fetch campaign templates",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  // Save current campaign as template
  const saveAsTemplate = async () => {
    if (!newTemplateName) {
      toast({
        title: "Error",
        description: "Template name is required",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      // Create template data
      const templateData = {
        name: newTemplateName,
        description: newTemplateDescription,
        campaignData: {
          ...currentCampaign,
          status: "DRAFT" as
            | "DRAFT"
            | "SCHEDULED"
            | "ACTIVE"
            | "COMPLETED"
            | "CANCELLED"
            | "PAUSED",
          metrics: {
            totalCalls: 0,
            successfulCalls: 0,
            failedCalls: 0,
            averageDuration: 0,
            averageSentiment: 0,
          },
        },
        isSystem: false,
      };

      // Call the API to create the template
      const { campaignApi } = await import("@/services/campaignApi");
      const response = await campaignApi.createTemplate(templateData);

      if (response.success && response.data) {
        // Format the response data to match our CampaignTemplate interface
        const newTemplate: CampaignTemplate = {
          id: response.data.id,
          name: response.data.name,
          description: response.data.description || "",
          campaignData: response.data.campaignData || {},
          isSystem: response.data.isSystem || false,
          createdAt: response.data.createdAt || new Date().toISOString(),
        };
        setTemplates((prev) => [...prev, newTemplate]);

        toast({
          title: "Template Saved",
          description: `Template "${newTemplateName}" has been saved successfully.`,
        });

        // Reset form and close dialog
        setNewTemplateName("");
        setNewTemplateDescription("");
        setShowSaveDialog(false);
      } else {
        toast({
          title: "Error",
          description: response.error?.message || "Failed to save template",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error saving template:", error);
      toast({
        title: "Error",
        description: "Failed to save campaign template",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Delete template
  const deleteTemplate = async (templateId: string) => {
    if (confirm("Are you sure you want to delete this template?")) {
      setIsLoading(true);

      try {
        // Call the API to delete the template
        const { campaignApi } = await import("@/services/campaignApi");
        const response = await campaignApi.deleteTemplate(templateId);

        if (response.success) {
          setTemplates((prev) => prev.filter((t) => t.id !== templateId));

          toast({
            title: "Template Deleted",
            description: "The template has been deleted successfully.",
          });
        } else {
          toast({
            title: "Error",
            description: response.error?.message || "Failed to delete template",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error("Error deleting template:", error);
        toast({
          title: "Error",
          description: "Failed to delete campaign template",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-white">Campaign Templates</h3>
        <Button
          variant="outline"
          size="sm"
          className="bg-gray-700 border-gray-600 text-white"
          onClick={() => setShowSaveDialog(true)}
        >
          <Save className="h-4 w-4 mr-2 text-teal-400" />
          Save as Template
        </Button>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-40">
          <Loader2 className="h-6 w-6 animate-spin text-teal-500" />
          <span className="ml-2 text-teal-500">Loading templates...</span>
        </div>
      ) : templates.length === 0 ? (
        <div className="text-center p-8 bg-gray-800 rounded-md">
          <FileText className="h-12 w-12 mx-auto mb-3 text-gray-600" />
          <h3 className="text-lg font-medium text-white mb-1">
            No Templates Found
          </h3>
          <p className="text-gray-400 mb-4">
            Save your current campaign as a template to reuse it later.
          </p>
          <Button
            variant="outline"
            className="bg-gray-700 border-gray-600 text-white"
            onClick={() => setShowSaveDialog(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Template
          </Button>
        </div>
      ) : (
        <ScrollArea className="h-96 bg-gray-800 rounded-md p-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {templates.map((template) => (
              <Card key={template.id} className="bg-gray-700 border-gray-600">
                <CardHeader className="pb-2">
                  <CardTitle className="text-white text-base flex justify-between items-center">
                    <span>{template.name}</span>
                    {template.isSystem && (
                      <span className="text-xs px-2 py-1 bg-gray-600 rounded-full text-gray-300">
                        System
                      </span>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <p className="text-sm text-gray-300">
                    {template.description}
                  </p>

                  <div className="text-xs text-gray-400">
                    Created:{" "}
                    {template.createdAt instanceof Date
                      ? template.createdAt.toLocaleDateString()
                      : new Date(template.createdAt).toLocaleDateString()}
                  </div>

                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1 bg-gray-600 border-gray-500 text-white"
                      onClick={() => onSelectTemplate(template)}
                    >
                      <Copy className="h-3 w-3 mr-1" />
                      Use Template
                    </Button>

                    {!template.isSystem && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="bg-gray-600 border-gray-500 text-red-400"
                        onClick={() => deleteTemplate(template.id)}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </ScrollArea>
      )}

      <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
        <DialogContent className="bg-gray-900 text-white">
          <DialogHeader>
            <DialogTitle>Save as Template</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div>
              <Label htmlFor="template-name" className="text-white">
                Template Name
              </Label>
              <Input
                id="template-name"
                className="bg-gray-700 text-white border-gray-600"
                placeholder="Enter template name"
                value={newTemplateName}
                onChange={(e) => setNewTemplateName(e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="template-description" className="text-white">
                Description
              </Label>
              <Input
                id="template-description"
                className="bg-gray-700 text-white border-gray-600"
                placeholder="Enter template description"
                value={newTemplateDescription}
                onChange={(e) => setNewTemplateDescription(e.target.value)}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              className="bg-gray-700 border-gray-600 text-white"
              onClick={() => setShowSaveDialog(false)}
            >
              Cancel
            </Button>
            <Button
              className="bg-teal-600 hover:bg-teal-700 text-white"
              onClick={saveAsTemplate}
            >
              Save Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
