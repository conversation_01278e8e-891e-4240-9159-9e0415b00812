// Database Schema Types

// User
export interface User {
  id: string;
  email: string;
  name: string;
  company?: string;
  role: "ADMIN" | "USER";
  phoneNumber?: string;
  createdAt: Date;
  updatedAt: Date;
  publicApiKey: string;
  privateSecretKey: string;
  calEventTypeId?: number;
  calApiKey?: string;
  settings: {
    defaultTransparencyLevel: "none" | "partial" | "full";
    defaultAssistant?: string;
    recordingEnabled: boolean;
    webSearchEnabled: boolean;
    preferredVoice: "male" | "female";
  };
}

// Assistant
export interface Assistant {
  id: string;
  userId: string;
  name: string;
  modes: ("web" | "voice")[];
  firstMessage: string;
  systemPrompt: string;
  provider: string;
  model: string;
  tools: AssistantTool[];
  voice: {
    provider: string;
    voiceId: string;
    settings: {
      speed: number;
      pitch: number;
      stability: number;
      volume: number;
    };
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface AssistantTool {
  id: string;
  type: "calendar" | "scraping" | "sms" | "email";
  config: Record<string, any>;
  isEnabled: boolean;
  name: string;
}

// Contact
export interface Contact {
  id: string;
  userId: string;
  name: string;
  email: string;
  phone: string;
  type: "business" | "personal";
  transparencyLevel: "none" | "partial" | "full";
  subcategory?: string;
  customSubcategory?: string;
  campaignId?: string;
  tags: string[];
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  lastContactedAt?: Date;
}

// Call/Conversation
export interface Call {
  id: string;
  userId: string;
  contactId: string;
  assistantId: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  status: "scheduled" | "in-progress" | "completed" | "failed";
  recording?: {
    url: string;
    duration: number;
  };
  transcript: CallTranscript[];
  goals: CallGoal[];
  metrics: {
    averageSentiment: number;
    sentimentTimeline: SentimentPoint[];
    whisperEffectiveness: number;
    goalCompletion: number;
  };
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CallTranscript {
  id: string;
  timestamp: Date;
  speaker: "ai" | "user";
  message: string;
  sentiment?: number;
}

export interface CallGoal {
  id: string;
  title: string;
  progress: number;
  completed: boolean;
  aiPrompt: string;
  resources: {
    urls: string[];
    files: string[];
  };
}

export interface SentimentPoint {
  timestamp: Date;
  value: number;
  trigger?: string;
}

// Whisper Template
export interface WhisperTemplate {
  id: string;
  userId: string;
  name: string;
  type: "business" | "personal";
  systemPrompt: string;
  editablePrompt: string;
  isSystem: boolean;
  isHidden: boolean;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

// Campaign
export interface Campaign {
  id: string;
  userId: string;
  name: string;
  description?: string;
  startDate: Date;
  endDate?: Date;
  status:
    | "DRAFT"
    | "SCHEDULED"
    | "ACTIVE"
    | "COMPLETED"
    | "CANCELLED"
    | "PAUSED";
  contacts: Contact[]; // Now returns full contact objects
  goals: CampaignGoal[];
  metrics: {
    totalCalls: number;
    successfulCalls: number;
    failedCalls: number;
    averageDuration: number;
    averageSentiment: number;
  };
  automationSettings?: {
    schedule?: {
      date: string;
      time: string;
      recurring?: {
        pattern: string;
      } | null;
    } | null;
    callSettings?: {
      maxAttempts: number;
      retryDelay: number;
    };
  } | null;
  callScript?: string;
  assistantId?: string;
  phoneNumberId?: string;
  channelSettings?: ChannelSettings;
  teamMembers?: CampaignTeamMember[];
  comments?: CampaignComment[];
  template?: CampaignTemplate;
  createdAt: Date;
  updatedAt: Date;
}

export interface CampaignGoal {
  id: string;
  campaignId: string;
  title: string;
  description?: string;
  target: number;
  progress: number;
  completed: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChannelSettings {
  id: string;
  campaignId: string;
  voice?: {
    enabled: boolean;
    message?: string;
    delay?: number;
  } | null;
  sms?: {
    enabled: boolean;
    message?: string;
    delay?: number;
    sendTime?: string;
  } | null;
  email?: {
    enabled: boolean;
    template?: string;
    delay?: number;
    sendTime?: string;
  } | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface CampaignTeamMember {
  id: string;
  campaignId: string;
  userId: string;
  role: "OWNER" | "ADMIN" | "EDITOR" | "VIEWER";
  invitedEmail?: string;
  status: "PENDING" | "ACCEPTED" | "DECLINED";
  lastActive?: Date;
  createdAt: Date;
  updatedAt: Date;
  user: {
    id: string;
    name: string;
    email: string;
  };
}

export interface CampaignComment {
  id: string;
  campaignId: string;
  userId: string;
  content: string;
  attachments: string[];
  createdAt: Date;
  updatedAt: Date;
  user: {
    id: string;
    name: string;
    email: string;
  };
}

export interface CampaignTemplate {
  id: string;
  userId?: string;
  name: string;
  description?: string;
  isSystem: boolean;
  campaignData: Partial<Campaign>;
  createdAt: Date;
  updatedAt: Date;
}

// Resource
export interface Resource {
  id: string;
  userId: string;
  title: string;
  type: "documentation" | "guide" | "tutorial" | "whitepaper" | "video";
  url: string;
  description?: string;
  tags: string[];
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Common Types
export type TransparencyLevel =
  | "Full Disclosure"
  | "Partial Disclosure"
  | "No Disclosure";

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}

// Pagination
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Query Parameters
export interface QueryParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  search?: string;
  filters?: Record<string, any>;
}

// Assistant Where Input
export type AssistantWhereInput = {
  userId?: string;
  OR?: Array<{
    name?: { contains: string; mode: "default" | "insensitive" };
    systemPrompt?: { contains: string; mode: "default" | "insensitive" };
  }>;
};

type ModelArchitecture = {
  modality: string;
  tokenizer: string;
  instruct_type: string | null;
};

type Pricing = {
  prompt: string;
  completion: string;
  image: string;
  request: string;
  input_cache_read: string;
  input_cache_write: string;
  web_search: string;
  internal_reasoning: string;
};

type TopProvider = {
  context_length: number;
  max_completion_tokens: number | null;
  is_moderated: boolean;
};

export type GPTModel = {
  id: string;
  name: string;
  created: number;
  description: string;
  context_length: number;
  architecture: ModelArchitecture;
  pricing: Pricing;
  top_provider: TopProvider;
  per_request_limits: unknown | null;
};
