# TAlkai247 Daily

A modern AI-powered communication platform that helps you manage and automate your daily conversations.

## Client Setup

### Prerequisites

- Node.js (v20.x recommended)
- npm

### Environment Variables

Create a `.env` file in the `server` directory with the following variables:

```env
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/talkai247"

# JWT
JWT_SECRET="your-secret-key-here"
JWT_EXPIRES_IN="24h"

# OpenRouter
OPENROUTER_API_KEY="your-openrouter-api-key"
OPENROUTER_BASE_URL="https://openrouter.ai/api/v1"
```

### Database Setup

1. Create a PostgreSQL database named `talkai247`
2. Run Prisma migrations:

```bash
cd client
```

### Installation & Running

1. Install dependencies:

```bash
cd client
npm install
```

2. Start the development client:

```bash
npm run dev
```

The client will run on `http://localhost:3000`.

### Managing the client

#### Starting the client

You can start the client in two ways:

1. Using npm (recommended for development):

```bash
npm run dev
```

#### Troubleshooting Client Issues

If you encounter the "Port 3000 is already in use" error:

1. Find the process using port 3000:

```bash
lsof -i :3000
```

2. Kill the existing process (replace [PID] with the process ID from step 1):

```bash
kill -9 [PID]
```

3. Start the server again using one of the methods above.

### Authentication Endpoints

This will create a fresh database with the default schema and no data.

## Client Setup

[Client setup instructions to be added]

## License

[License information to be added]
