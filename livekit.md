

# **Architecting a Real-Time AI Whisper Feature with LiveKit and Twilio: A Comprehensive Implementation Blueprint**

## **Section 1: Foundational Architecture: Bridging Telephony and Real-Time Communication**

The successful implementation of a sophisticated real-time communication feature, such as the proposed AI-powered whisper service, hinges on a robust and meticulously configured foundation. This initial section details the critical architectural decisions and procedures for bridging the Public Switched Telephone Network (PSTN)—the domain of the external phone 'Caller'—with the modern, IP-based WebRTC environment managed by LiveKit. Establishing a seamless and reliable link between these two disparate networks is the non-negotiable first step upon which all subsequent functionality will be built.

### **1.1. Strategic Choice: Twilio Elastic SIP Trunking vs. TwiML**

To integrate inbound telephone calls into the LiveKit ecosystem, <PERSON>wilio presents two primary methodologies: Programmable Voice using TwiML (Twilio Markup Language) and the more direct Elastic SIP Trunking.1 While TwiML Bins offer a seemingly straightforward approach for forwarding calls by executing a simple XML script in response to an incoming call event, this method introduces an additional layer of abstraction.2 For a feature as complex and interactive as the whisper service, which demands granular control over call state, signaling, and error handling, this abstraction can become a liability.  
Therefore, for a production-grade, scalable, and future-proof system, **Twilio's Elastic SIP Trunking is the superior architectural choice**. Elastic SIP Trunking establishes a direct, persistent, and standards-based Session Initiation Protocol (SIP) connection between Twilio's carrier network and the LiveKit SIP service. This direct integration provides a cleaner and more transparent data path, reducing latency and simplifying diagnostics. It ensures that the full range of SIP signaling is available to the application, which is crucial for advanced features like call transfers (SIP REFER) that may be required in future iterations of the product.2 By choosing Elastic SIP Trunking, the architecture avoids the operational indirection of TwiML, resulting in a more robust and manageable foundation.

### **1.2. Step-by-Step Configuration of Twilio Elastic SIP Trunking**

Configuring the SIP trunk is a precise process that involves setting up resources in Twilio and ensuring they are correctly pointing to the LiveKit infrastructure. While these steps can be performed through the Twilio Console UI, using the Twilio CLI is the recommended practice for automation, repeatability, and integration into an infrastructure-as-code (IaC) workflow.1 Several community examples demonstrate automating this setup with scripts, which represents a best practice.4  
The configuration process is as follows:

1. **Purchase a Phone Number:** Acquire a phone number from Twilio that will serve as the entry point for inbound calls. This can be done via the Twilio Console or CLI.1  
2. **Create the SIP Trunk:** A new Elastic SIP Trunk must be created. A critical requirement is that its domain name must end in pstn.twilio.com. This can be accomplished with the twilio api trunking v1 trunks create command.1  
3. **Configure Inbound Call Routing (Origination):** The trunk must be configured to route incoming calls (Origination) to the LiveKit SIP service. This is achieved by setting the trunk's **Origination URI** to the unique SIP URI provided by the LiveKit project (e.g., sip:\<your\_livekit\_sip\_host\>). This tells Twilio where to send the SIP INVITE request when a call is received on the associated phone number.1  
4. **Configure Outbound Call Settings (Termination):** To enable the system to make outbound calls (e.g., the AI Assistant initiating a call), the trunk's Termination settings must be configured. This involves two key steps:  
   * Note the **Termination SIP URI** provided by Twilio (e.g., my-trunk.pstn.twilio.com). This is the address the LiveKit service will send outbound calls to.  
   * Create a **Credential List** in Twilio with a unique username and password. This list must be associated with the trunk's Termination settings for authentication, ensuring that only authorized requests from the LiveKit service can initiate outbound calls.1  
5. **Associate Phone Number and Trunk:** The final step in Twilio is to link the purchased phone number to the newly configured SIP trunk. This association activates the routing rules, directing any call to that number through the trunk to the LiveKit service.1

### **1.3. Configuring LiveKit Trunks and Dispatch Rules**

Once Twilio is configured, the LiveKit project must be made aware of this new telephony bridge. This is accomplished by creating corresponding trunk configurations and dispatch rules within LiveKit, which serve as the internal representation of the external provider's setup.8

1. **Create LiveKit Inbound Trunk:** An inbound trunk must be registered in LiveKit to authorize incoming calls from Twilio. This is done by creating an inbound-trunk.json configuration file that specifies, at a minimum, the Twilio phone number(s) that are permitted to initiate calls into the system. The lk sip inbound create CLI command is used to register this configuration.8  
2. **Create LiveKit Outbound Trunk:** Similarly, an outbound trunk is required for making calls. An outbound-trunk.json file is created, specifying the Twilio trunk's Termination SIP URI as the address, the "from" number (the Twilio number), and the authentication auth\_username and auth\_password that match the credentials created in the Twilio Credential List. This is registered using lk sip outbound create.8  
3. **Create a Dispatch Rule:** A dispatch rule is the critical piece of application logic that instructs LiveKit on how to handle a validated inbound call.11 For the whisper feature, which requires each phone call to be a unique, isolated session, the  
   dispatchRuleIndividual is the ideal choice. This rule type, when configured with a roomPrefix (e.g., "call-"), automatically creates a new, uniquely named LiveKit room for every single inbound call.8 This prevents any possibility of crosstalk between separate user sessions.

This deliberate separation of the **Provider Trunk** (in Twilio), the **LiveKit Trunk** (the internal representation), and the **Dispatch Rule** (the business logic) forms a highly modular and resilient architectural pattern.15 It effectively decouples the physical telephony layer from the application's core functionality. This design means that the telephony provider could be changed in the future (e.g., from Twilio to Telnyx or Plivo 16) by simply updating the LiveKit Trunk configurations, with zero changes required to the application code that manages the complex interactions within the LiveKit room. This abstraction is a hallmark of a well-architected system, providing significant long-term flexibility and maintainability.

## **Section 2: Core Session Management: The Secure Three-Participant Room**

With the telephony bridge established, the focus shifts to the server-side logic that governs the real-time session itself. This section details the architecture for creating and managing the secure three-participant LiveKit room, with a strong emphasis on controlling access and defining capabilities through a robust, server-authoritative permissions model.

### **2.1. A Deep Dive into Participant Identity and Permissions**

LiveKit's security model is built upon a fine-grained, token-based access control system. A participant's capabilities within a room are not determined by the client application but are authoritatively defined by grants encoded within a JSON Web Token (JWT). This token is generated by the application's secure backend and passed to the client, which then uses it to authenticate and connect to the LiveKit server.18  
The primary grants relevant to this project are canPublish (the ability to send media), canSubscribe (the ability to receive media), and canPublishData (the ability to send arbitrary data messages).18 By generating tokens with specific combinations of these grants on the backend, the server enforces the principle of least privilege, ensuring participants can only perform actions they are explicitly allowed to.  
A particularly powerful and relevant permission for this architecture is the hidden grant. When a participant's token includes hidden: true, they can join a room, publish and subscribe to tracks as their other permissions allow, but they will not appear in the participant lists returned by the client SDKs, nor will their joining or leaving trigger standard participant events for other clients.18 This makes the  
hidden grant the ideal mechanism for the AI Assistant. It allows the AI to function as a fully-fledged participant—a "ghost in the machine"—without its presence being known to the external 'Caller', thus preserving the illusion of a standard two-party conversation.

### **2.2. Architecting Granular Access Control with JWTs**

The application backend is responsible for minting a unique, short-lived JWT for each of the three participants (User, Caller, AI Assistant) immediately before they join a room. The LiveKit server SDKs for languages like Python and Node.js provide convenient helper classes such as AccessToken and VideoGrants to streamline this process.18  
The permissions encoded in each token must be dynamically determined based on the participant's role and the specific whisper scenario being enacted. This requires the backend to have logic that, upon initiating a session, can identify the room name, the participant's identity, and their intended role to generate the correct set of grants. To ensure clarity and prevent implementation errors, the following matrix defines the precise JWT grants required for each role in each scenario.

| Participant Role | Whisper Scenario | roomJoin | canPublish | canSubscribe | canPublishData | hidden | Rationale |
| :---- | :---- | :---- | :---- | :---- | :---- | :---- | :---- |
| **User** | AI-to-User | true | true | true | true | false | The primary user. Needs to speak with the Caller and hear both the Caller and the AI. Can send/receive data for UI control. |
| **Caller** | AI-to-User | true | true | true | false | false | The external party. Needs to speak with and hear the User. canSubscribe is true, but server rules will restrict what they hear. Data publishing is disabled. |
| **AI Assistant** | AI-to-User | true | true | true | true | true | Listens to the conversation to provide context, speaks whispers to the User. Hidden from the Caller. |
| **User** | User-to-AI | true | true | true | true | false | Whispers instructions to the AI. Listens to the AI's conversation with the Caller. |
| **Caller** | User-to-AI | true | true | true | false | false | The external party. Needs to speak with and hear the AI. Server rules restrict audio flow. |
| **AI Assistant** | User-to-AI | true | true | true | true | true | Listens to both the User's whispers and the Caller's speech. Speaks to the Caller. Hidden from the Caller. |

### **2.3. Programmatic Room Lifecycle and State Management**

The application's backend will use the RoomServiceClient, available in all LiveKit server SDKs, as the primary interface for managing the lifecycle of communication sessions.18 When an inbound call arrives or a user initiates an outbound call, the backend will programmatically call  
roomClient.createRoom() to instantiate a new session. This call should include the emptyTimeout parameter, which is a crucial operational setting that instructs the LiveKit server to automatically terminate the room and clean up all associated resources after a specified period of inactivity (e.g., 10 minutes), preventing orphaned rooms.18  
Beyond creation and destruction, the RoomServiceClient provides essential administrative capabilities for monitoring and moderation, such as listRooms, getParticipant, and removeParticipant.18  
While the initial JWT grants set a participant's baseline capabilities, LiveKit's architecture provides a mechanism for modifying these permissions dynamically during a live session. The UpdateParticipant server API method allows a backend process with roomAdmin privileges to alter a participant's permissions on the fly.18 This is distinct from managing audio subscriptions;  
UpdateSubscriptions controls *what a participant can hear*, whereas UpdateParticipant controls *what a participant is fundamentally allowed to do* (e.g., publish media at all).  
For instance, if a future feature required the User to be able to completely silence the AI, the backend could issue an UpdateParticipant call to set the AI's canPublish grant to false. When this change is made, the LiveKit server broadcasts a ParticipantPermissionChanged event to all clients in the room. The User's client application can listen for this event and update its UI accordingly, perhaps by showing a visual indicator that the AI is now in a "listen-only" mode. This capability for real-time, server-enforced permission updates provides a sophisticated layer of state management that goes far beyond simple client-side mute toggles.

## **Section 3: The AI Assistant: Implementation as a Headless Real-Time Participant**

The AI Assistant is not an external service that merely interacts with an API; it is a first-class, real-time participant within the LiveKit ecosystem. Architecting it as such is fundamental to achieving the low-latency, conversational experience required. This section defines the technical blueprint for the AI Assistant, treating it as a headless, server-side client that programmatically joins rooms to listen, process, and speak.

### **3.1. Architectural Choice: Leveraging the LiveKit Agents Framework**

The AI Assistant is, by definition, a "headless client"—a software program that connects to a LiveKit room without a graphical user interface. It must programmatically handle the entire WebRTC connection lifecycle, manage media tracks, and process data in real time. For this specific purpose, LiveKit provides the **Agents Framework**, a specialized SDK available for Python and Node.js designed explicitly for building server-side, programmable participants.19  
This framework abstracts away the significant complexities of raw WebRTC and PeerConnection management, offering high-level APIs for connecting to rooms, iterating over media streams, and publishing new tracks. This allows developers to focus on the AI pipeline logic rather than the underlying real-time transport infrastructure.19  
A critical decision at this stage is the choice of programming language for the agent. While both Python and Node.js are supported, the AI and Machine Learning landscape is overwhelmingly dominated by Python. It offers an unparalleled ecosystem of mature libraries for Speech-to-Text (STT), Natural Language Processing (NLP), and Text-to-Speech (TTS), including direct integrations with services like OpenAI, Deepgram, and Cartesia, often available as simple plugins for the LiveKit Agents framework.23 Therefore,  
**Python is the strongly recommended language for implementing the AI Assistant**. This choice aligns the project with the broader AI community's standards and provides the most direct path to leveraging state-of-the-art models and tools.21

### **3.2. Ingesting and Processing Real-Time Audio Streams**

The AI Assistant's ability to understand the conversation as it happens is predicated on its ability to ingest and process audio with minimal latency. The LiveKit Python SDK facilitates this through the AudioStream class, which elegantly presents the audio from a subscribed track as a Python AsyncIterator.25  
The agent's workflow will be as follows:

1. Upon joining a room, the agent will subscribe to the audio tracks of the participants it needs to listen to (e.g., the 'Caller' and/or the 'User'), as dictated by the server-enforced subscription rules.  
2. For each subscribed track, it will instantiate an AudioStream object.  
3. The agent will then asynchronously iterate over this stream. Each iteration yields an AudioFrame object, which contains a small chunk of raw PCM audio data (e.g., 20-100 milliseconds worth).25

This frame-by-frame processing is the key to achieving low latency. The agent does not wait for a speaker to pause before beginning transcription. Instead, it continuously feeds these small audio chunks into a streaming STT service. This allows the AI to begin understanding user intent within milliseconds of speech onset, enabling natural-feeling interruptions and rapid responses, which are hallmarks of advanced conversational AI.22  
To further enhance the quality of the input for the STT engine, especially in real-world scenarios where the 'Caller' may be in a noisy environment (e.g., a car, a coffee shop), it is highly recommended to enable server-side audio processing. LiveKit provides an enhanced noise and background voice cancellation (BVC) feature, powered by Krisp, as a readily available plugin for the Python SDK. Applying this filter to the inbound AudioStream will significantly improve the accuracy of the resulting transcription, leading to more reliable AI performance.28

### **3.3. Synthesizing and Publishing AI-Generated Audio**

After the AI's internal logic (e.g., an LLM) determines a response, that text must be converted back into an audio stream and played into the room for the intended recipient.  
The process is the reverse of ingestion:

1. The text response is sent to a streaming TTS service (e.g., ElevenLabs, Cartesia 23), which returns an asynchronous stream of audio chunks.  
2. The AI agent creates a LocalAudioTrack using an AudioSource. This effectively creates a software-defined, virtual microphone for the agent.26  
3. The agent publishes this LocalAudioTrack to the room.  
4. As audio chunks are received from the TTS service, the agent pushes them into the AudioSource, which transmits them through the published track to the subscribed listeners.26

The LiveKit Python SDK offers capabilities beyond this simple 1-in-1-out audio flow. The agent can manage multiple audio tracks simultaneously—subscribing to several participants while publishing its own speech. For more advanced use cases, the SDK includes an AudioMixer class.30 This powerful utility can programmatically take multiple input audio streams, mix them into a single output stream, and then publish that result. This could be used, for example, to play a subtle "thinking" sound effect to the User while the AI is processing an LLM request, and to mix this sound with the AI's primary audio output.31 This capability transforms the AI agent from a simple participant into a sophisticated, server-side audio processing node, opening the door for highly dynamic and complex interactive behaviors.

## **Section 4: The Whisper Mechanism: A Definitive Guide to Selective Audio Routing**

This section addresses the core intellectual property of the feature: the "whisper" mechanism itself. The ability to control precisely who hears whom within a multi-participant room is not a client-side feature but a fundamental server-side security and state management challenge. This guide provides a definitive blueprint for implementing this selective audio routing.

### **4.1. The Core Principle: Server-Enforced Subscriptions**

LiveKit provides two distinct models for managing which media tracks a participant receives: client-side selective subscription and server-side subscription management. In the client-side model, a developer can set autoSubscribe: false in the connection options and then programmatically call publication.setSubscribed(true) on the specific tracks the client wishes to receive.18 While flexible, this approach is fundamentally insecure for a privacy-sensitive feature like whispering. A malicious or compromised client could simply ignore the application's logic and subscribe to all available tracks, completely breaking the privacy model.  
Therefore, the only robust and secure method for implementing the whisper feature is to use **server-enforced subscriptions**. This is accomplished via the UpdateSubscriptions method in the LiveKit Server API.18 This API allows the application's backend server, acting as a trusted administrator, to forcibly set the complete list of track subscriptions for any participant in a room. This server-side declaration overrides any and all client-side requests or preferences, making the backend the single source of truth for the audio flow.  
From an architectural perspective, the implementation of the whisper feature can be viewed as a dynamic graph management problem. The participants in the LiveKit room are the nodes of the graph, and the audio tracks represent potential directed edges between them. A standard conference call is a fully connected graph where an edge exists from every node to every other node. The whisper feature requires a dynamically configured, partially connected graph. The application server's primary role in this system is to act as the graph manager, using the UpdateSubscriptions API to add or remove these audio edges in real-time to enforce the precise communication topology required by the active whisper scenario. This powerful abstraction separates the logical concept of a "room" from the physical flow of media within it, enabling complex and secure communication patterns.

### **4.2. Implementation of Whisper Scenario 1: AI-to-User Communication**

This is the primary whisper mode where the AI provides suggestions to the User during their conversation with the Caller.

* **Goal:** The User and Caller can converse naturally. The AI listens to the conversation and provides suggestions (whispers) that are audible *only* to the User.  
* **Initial State:** A three-participant LiveKit room is created containing the User, the Caller (from the SIP trunk), and the hidden AI Assistant. All participants have published their audio tracks.  
* **Server Actions (via UpdateSubscriptions API):** The backend server will execute the following subscription rule updates immediately after all participants have joined:  
  1. **Target: The User.** The server updates the User's subscriptions to include the track\_sid of the **Caller's** audio track and the track\_sid of the **AI Assistant's** audio track.  
  2. **Target: The Caller.** The server updates the Caller's subscriptions to include *only* the track\_sid of the **User's** audio track. The AI's track is explicitly excluded.  
  3. **Target: The AI Assistant.** The server updates the AI's subscriptions to include the track\_sid of the **Caller's** audio track and the **User's** audio track. This allows the AI to hear the full conversation to generate relevant, context-aware suggestions.  
* **Resulting Audio Flow:**  
  * **User Hears:** Caller \+ AI Assistant.  
  * **Caller Hears:** User ONLY.  
  * **AI Assistant Hears:** Caller \+ User.

### **4.3. Implementation of Whisper Scenario 2: User-to-AI Communication (Reverse)**

This is the reverse scenario, where the AI is having a conversation with the Caller, and the User provides real-time coaching or instructions to the AI.

* **Goal:** The AI and Caller can converse naturally. The User can listen to their conversation and provide instructions (whispers) that are audible *only* to the AI.  
* **Initial State:** A three-participant LiveKit room is created containing the User, the Caller, and the hidden AI Assistant. All participants have published their audio tracks.  
* **Server Actions (via UpdateSubscriptions API):** The backend server will execute the following subscription rule updates:  
  1. **Target: The User.** The server updates the User's subscriptions to include *only* the track\_sid of the **AI Assistant's** audio track. This allows the User to monitor the AI's side of the conversation.  
  2. **Target: The Caller.** The server updates the Caller's subscriptions to include *only* the track\_sid of the **AI Assistant's** audio track.  
  3. **Target: The AI Assistant.** The server updates the AI's subscriptions to include the track\_sid of the **Caller's** audio track and the track\_sid of the **User's** audio track. This allows the AI to hear the Caller's responses and the User's whispered instructions.  
* **Resulting Audio Flow:**  
  * **User Hears:** AI Assistant ONLY.  
  * **Caller Hears:** AI Assistant ONLY.  
  * **AI Assistant Hears:** Caller \+ User.

To provide absolute clarity for the development team, the following table specifies the exact subscription rules.

| Whisper Scenario | Target Participant (Subscriber) | Subscribe To Tracks From (Publishers) |
| :---- | :---- | :---- |
| **AI-to-User** | User | Caller, AI Assistant |
| **AI-to-User** | Caller | User |
| **AI-to-User** | AI Assistant | User, Caller |
| **User-to-AI** | User | AI Assistant |
| **User-to-AI** | Caller | AI Assistant |
| **User-to-AI** | AI Assistant | User, Caller |

## **Section 5: Driving AI Behavior: Integrating Goals and Real-Time Triggers**

For the AI Assistant to be more than a generic chatbot, its behavior must be driven by the specific business or personal objectives of the call. This requires a system that can translate high-level goals into concrete, real-time actions for the AI. This section details the architecture for this dynamic instruction and control layer.

### **5.1. Translating Backend Logic into Actionable AI Instructions**

The application's design includes a backend database with schemas for Goals (e.g., "Handle Client Objections Smoothly") and Contacts, linked by a many-to-many relationship.18 The user interface allows for the selection of these goals, which are associated with predefined prompt templates and, crucially, "Custom Whisper Triggers" such as "mentions of pricing" or "client hesitation".18  
This system is not static; it is fundamentally an event-driven architecture. The workflow is as follows:

1. When a call is initiated, the application backend retrieves the active Goals and associated Triggers for the specific Contact involved in the call.  
2. This "initial context" (the objectives, keywords, and response strategies) must be delivered to the unique AI Agent instance that is handling this specific call session.  
3. The AI Agent, using its real-time STT pipeline, continuously analyzes the conversation transcript.  
4. The agent's core logic compares the incoming text against the list of trigger phrases it received in its initial context.  
5. A match constitutes a real-time event.32 This event then triggers the agent to execute the associated action—for example, looking up the corresponding prompt template from the goal and generating a whisper for the user, such as "Remind them of the long-term value and offer a case study to show ROI".18

This architecture requires the AI Agent to be stateful, holding the unique context for its session for the entire duration of the call. The process forms a tight, continuous loop of: Listen \-\> Transcribe \-\> Analyze for Triggers \-\> Act. This is a classic pattern for building responsive, event-driven AI systems.34

### **5.2. Architecting a Real-Time Instruction Channel**

A communication channel is required to deliver the initial context and any potential mid-call updates (e.g., if the user changes the active goal via the UI) from the application backend to the AI Agent. Two primary architectural options exist for this channel:

1. **External Communication Channel:** The AI Agent could establish a separate WebSocket or Server-Sent Events (SSE) connection back to an endpoint on the application's backend server.36  
2. **Internal LiveKit Data Channel:** The system could leverage LiveKit's built-in capabilities for sending arbitrary data between participants.18

While an external WebSocket or SSE stream is a viable option, it introduces significant architectural complexity. It would require a separate endpoint to be developed and secured, and its own authentication, connection management, and error-handling logic would need to be implemented.38 This creates a parallel communication system that could suffer from state desynchronization; for instance, the WebSocket might be connected while the core LiveKit media connection has failed, leading to a fragmented and difficult-to-debug state.  
Therefore, **using LiveKit's built-in data channels is the superior and recommended architecture**. This approach avoids the overhead and complexity of managing a second connection. All communication, both media and control data, is multiplexed over the single, secure, and reliable WebRTC connection that is already established for the AI Agent. Authentication is handled by the initial join token, and connection reliability is managed by WebRTC's underlying ICE and transport mechanisms.  
The LiveKit Server API provides a sendData method, which allows the trusted application backend to directly publish a data payload to a specific participant (in this case, the AI Agent) within a room. This is the simplest, most elegant, and most robust solution. It adheres to the sound architectural principle of leveraging the capabilities of the primary framework before introducing external dependencies and their associated complexities.

## **Section 6: Client-Side Implementation: Building the Interactive Experience**

The front-end applications for both web and mobile are the primary interfaces through which the user interacts with the whisper feature. While the complex audio routing is managed by the server, the client is responsible for capturing the user's audio, rendering the received audio, and providing a clear, intuitive user interface that accurately reflects the real-time state of the call.

### **6.1. Web Application Blueprint using the LiveKit JavaScript SDK**

The livekit-client JavaScript SDK is the cornerstone of the web application. The implementation will follow a clear, event-driven pattern.18

1. **Connection and Initialization:**  
   * The client will first make an authenticated request to the application backend to fetch a JWT access token for the current user and the specific room.  
   * It will then instantiate a new Room() object. It is critically important to include autoSubscribe: false in the RoomOptions. This explicitly cedes control over track subscriptions to the backend server, which is essential for the whisper mechanism to function securely.18  
   * The client connects to the session using await room.connect(url, token).  
2. **Publishing Media:**  
   * Once connected, the client will publish the user's microphone to the room. The SDK simplifies this with the high-level helper method: await room.localParticipant.setMicrophoneEnabled(true). This handles the browser's permission prompts, track creation, and publishing negotiation.18  
3. **UI and Event Handling:**  
   * The user interface must be designed to be reactive, updating its state based on events from the LiveKit server rather than maintaining its own assumptions about the call state. This ensures the UI is always synchronized with the server-enforced reality. Key event handlers are detailed in Section 6.3.

### **6.2. Mobile Application Strategy for iOS (Swift) and Android (Kotlin)**

LiveKit provides a consistent and powerful development experience across mobile platforms with its native SDKs for iOS (Swift) and Android (Kotlin), as well as UI component libraries (SwiftUI and Jetpack Compose) that significantly accelerate development.40 The availability of comprehensive starter applications and tutorials provides a solid foundation for the mobile development effort.42  
The core application logic on mobile will mirror the web implementation precisely:

1. **Connection:** Fetch a token from the backend, connect to the room with autoSubscribe: false.  
2. **Permissions:** Handle platform-specific runtime permission requests for the microphone (and camera, if needed). This involves configuring the Info.plist file on iOS with an NSMicrophoneUsageDescription 40 and the  
   AndroidManifest.xml file on Android with the RECORD\_AUDIO permission.44  
3. **Publishing:** Use the native SDK's methods to enable and publish the local microphone track.  
4. **Rendering:** Utilize the native UI components provided by the SDKs to render media. On iOS, this involves using the VideoView (even for audio-only participants, to handle participant display).43 On Android, this is typically done with a  
   SurfaceViewRenderer.46 Subscribed audio tracks are generally played automatically by the SDKs.

The most significant advantage of the LiveKit ecosystem is the consistency of its architectural model across all platforms. The core object model (Room, Participant, Track, Publication) and the eventing system (TrackSubscribed, ActiveSpeakersChanged, etc.) are conceptually identical in the JavaScript, Swift, and Kotlin SDKs.18 This means a single backend can serve all clients, and the client-side logic, though written in different languages, will follow the exact same state machine and event-handling patterns. This architectural symmetry dramatically reduces the cognitive load for a cross-platform team, enables parallel development, and simplifies long-term maintenance.

### **6.3. Mastering the Real-Time UI: Handling Events for a Dynamic Experience**

A responsive and intuitive UI is critical for the user to understand and control the complex state of a whisper call. The client application must implement robust listeners for several key real-time events broadcast by the LiveKit server 18:

* **TrackSubscribed:** This event fires when the server has permitted the client to subscribe to a new track. The client's handler for this event should attach the received audio track to an appropriate audio playback element.  
* **ActiveSpeakersChanged:** The server continuously monitors voice activity and sends this event with an array of the currently speaking participants. The UI should use this to provide real-time visual feedback, such as highlighting the avatar of the person who is talking, whether it's the User, the Caller, or the AI.  
* **ParticipantPermissionChanged:** Should the server dynamically alter a participant's permissions mid-call (as discussed in Section 2.3), this event allows the UI to react accordingly, for example, by disabling a "publish" button if the user's canPublish grant is revoked.  
* **DataReceived:** The client will listen for this event to receive data payloads, such as real-time transcriptions sent from the AI Assistant, and display them in the UI.

The UI for the whisper feature must provide clear and unambiguous feedback to the User. A simple binary mute button is insufficient. The user's microphone has at least three distinct states relative to other participants: 1\) Open to all, 2\) Muted to all, and 3\) Whispering (live to one participant, muted to another). The UI must visually represent this complex state. For example, a "Whisper to AI" button could change the microphone icon to a special "whisper" state. When this button is pressed, the client application must not only update its local UI state but also send a signal to the backend. This signal triggers the backend to execute the necessary UpdateSubscriptions API call, which in turn changes the audio routing graph for the entire session. This demonstrates that the client UI is not merely a passive view but an active controller that initiates authoritative state changes on the server.

## **Section 7: Comprehensive Implementation Roadmap and Strategic Recommendations**

This final section synthesizes the preceding analysis into a strategic implementation plan. It outlines a phased development approach to mitigate risk, addresses critical non-functional requirements for a production system, and provides a summary of the key architectural decisions.

### **7.1. A Phased Development and Deployment Plan**

Attempting to build the entire system in a single, monolithic phase is a high-risk endeavor. A phased approach is strongly recommended to de-risk the project, allow for iterative feedback, and deliver value incrementally.

* **Phase 1: Foundational Telephony and Room Integration.**  
  * **Objective:** Establish the core connectivity between PSTN and WebRTC.  
  * **Key Tasks:** Configure the Twilio Elastic SIP Trunk. Implement the LiveKit inbound/outbound trunks and the dispatchRuleIndividual dispatch rule. Build a minimal client that can join a room initiated by an inbound phone call. The "AI Assistant" can be a simple bot placeholder that joins the room but has no logic.  
  * **Success Criteria:** A phone call to the Twilio number successfully creates a three-participant LiveKit room where basic, open audio flows between all parties.  
* **Phase 2: Implementation of AI-to-User Whisper.**  
  * **Objective:** Build and integrate the AI Assistant and implement the primary whisper scenario.  
  * **Key Tasks:** Develop the Python-based AI Agent using the LiveKit Agents framework. Integrate basic STT and TTS services. Implement the server-side UpdateSubscriptions logic for the AI-to-User audio graph (Section 4.2).  
  * **Success Criteria:** The User and Caller can hold a conversation, and the AI can send whispers that are audible only to the User.  
* **Phase 3: Implementation of User-to-AI Whisper and UI Controls.**  
  * **Objective:** Implement the reverse whisper scenario and the client-side controls to switch between modes.  
  * **Key Tasks:** Develop the UI components for initiating the User-to-AI whisper mode. Implement the backend endpoint that the client calls to trigger this mode. Write the server-side UpdateSubscriptions logic for the reverse audio graph (Section 4.3).  
  * **Success Criteria:** The User can press a button to enter a mode where their speech is routed only to the AI, while the AI's speech is routed to the Caller.  
* **Phase 4: Integration of Dynamic Goals and Real-Time Triggers.**  
  * **Objective:** Make the AI's behavior intelligent and context-aware.  
  * **Key Tasks:** Connect the AI Agent to the application's backend database to retrieve Goals and Triggers at the start of a session.18 Implement the real-time instruction channel (via LiveKit data channels) for context delivery. Implement the trigger-detection logic within the AI agent.18  
  * **Success Criteria:** The AI's whispered suggestions are no longer generic but are dynamically generated based on the pre-selected goals for the call and real-time conversational cues.

### **7.2. Critical Considerations for Scalability, Security, and Data Privacy**

* **Scalability:** The LiveKit SFU (Selective Forwarding Unit) architecture is designed to be horizontally scalable, allowing for the addition of more server nodes to handle increased load.18 The AI Agent workers must also be designed for scalability. Deploying them as a containerized service (e.g., using Docker) on an orchestration platform like Kubernetes is a standard production pattern. This allows the number of agent instances to be automatically scaled up or down based on the number of concurrent calls.  
* **Security:** The security of the system relies on the strict enforcement of the principles outlined in this document. All API keys, secrets, and credentials must be stored in a secure secret management system (e.g., AWS Secrets Manager, HashiCorp Vault) and must never be exposed on the client-side. The use of short-lived JWTs with the minimum necessary permissions for each participant is paramount.  
* **Data Privacy:** Telephone conversations and their transcriptions are highly sensitive and may contain Personally Identifiable Information (PII). A comprehensive data governance plan is essential. If call recording is a future requirement (which can be implemented using LiveKit Egress 50), the plan must address where recordings and transcripts are stored, the encryption methods used, and the access control policies governing them. The auditing and history logging capabilities mentioned in the database schema are a good first step towards this.18

### **7.3. Final Architectural Recommendations for Optimal Performance and Reliability**

The architecture detailed in this report is designed to be robust, scalable, and maintainable. The key technological and strategic recommendations are summarized below.

| Component | Recommended Technology / Approach | Rationale |
| :---- | :---- | :---- |
| **Real-Time Communications** | LiveKit (Cloud or Self-Hosted) | Provides a scalable SFU, comprehensive multi-platform SDKs, and server-side APIs for granular control.18 |
| **Telephony Bridge** | Twilio Elastic SIP Trunking | Offers a direct, robust, and standards-based connection to the PSTN, superior to TwiML for complex applications.1 |
| **AI Agent Runtime** | LiveKit Agents Framework (Python) | Purpose-built for creating headless, real-time participants. Python provides the best ecosystem for AI/ML integration.19 |
| **Whisper Mechanism** | Server-Enforced UpdateSubscriptions API | The only secure and non-bypassable method for enforcing selective audio routing and ensuring privacy.18 |
| **AI Instruction Channel** | LiveKit Data Channels (sendData API) | Simplifies architecture by multiplexing control data over the existing secure WebRTC connection, avoiding a separate WebSocket/SSE channel.18 |
| **Web Frontend** | LiveKit JavaScript SDK | Mature SDK for all modern browsers, enabling the creation of reactive, event-driven user interfaces.18 |
| **iOS Application** | LiveKit Swift SDK & SwiftUI Components | Provides native performance and deep integration with the Apple ecosystem, with components to accelerate UI development.40 |
| **Android Application** | LiveKit Kotlin SDK & Jetpack Compose Components | Provides native performance and deep integration with the Android ecosystem, with components for modern UI development.46 |

In conclusion, the entire system—from the client-side UI to the server-side AI agent—should be designed with an event-driven mindset. Components should not make assumptions about the state of the system but should instead be built to react to events and state changes broadcast by the LiveKit server. This decoupled approach, where the server is the single source of truth for permissions and media flow, will result in the most resilient, secure, and scalable architecture for this innovative real-time communication feature.

#### **Works cited**

1. Create and configure a Twilio SIP trunk \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/sip/quickstarts/configuring-twilio-trunk/](https://docs.livekit.io/sip/quickstarts/configuring-twilio-trunk/)  
2. Inbound calls with Twilio Voice \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/sip/accepting-calls-twilio-voice/](https://docs.livekit.io/sip/accepting-calls-twilio-voice/)  
3. Use LiveKit \+ Twilio to build a phone AI agent \- YouTube, accessed June 23, 2025, [https://www.youtube.com/watch?v=2HmqSXHYMJ8](https://www.youtube.com/watch?v=2HmqSXHYMJ8)  
4. How to Build Your Own AI-Powered Voice Agent with LiveKit and Twillio: Step-by-Step Implementation Guide \- DEV Community, accessed June 23, 2025, [https://dev.to/joshua\_ab5669801069c289ed/how-to-build-your-own-ai-powered-voice-agent-with-livekit-and-twillio-step-by-step-implementation-2i8k](https://dev.to/joshua_ab5669801069c289ed/how-to-build-your-own-ai-powered-voice-agent-with-livekit-and-twillio-step-by-step-implementation-2i8k)  
5. This is the guide to show the method to build your own AI-Powered voice agent with LiveKit and Twillio \- GitHub, accessed June 23, 2025, [https://github.com/cycle-sync-ai/livekit-voice-ai-agent-setup](https://github.com/cycle-sync-ai/livekit-voice-ai-agent-setup)  
6. A simple example to have a LiveKit Agent answer a SIP call \- GitHub, accessed June 23, 2025, [https://github.com/livekit-examples/livekit-sip-agent-example](https://github.com/livekit-examples/livekit-sip-agent-example)  
7. Outbound Agent with LiveKit \- Cerebrium, accessed June 23, 2025, [https://docs.cerebrium.ai/v4/examples/livekit-outbound-agent](https://docs.cerebrium.ai/v4/examples/livekit-outbound-agent)  
8. SIP trunk setup \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/sip/quickstarts/configuring-sip-trunk/](https://docs.livekit.io/sip/quickstarts/configuring-sip-trunk/)  
9. SIP to WebRTC bridge for LiveKit \- GitHub, accessed June 23, 2025, [https://github.com/livekit/sip](https://github.com/livekit/sip)  
10. SIP inbound trunk \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/sip/trunk-inbound/](https://docs.livekit.io/sip/trunk-inbound/)  
11. Accepting incoming calls \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/agents/v0/quickstarts/inbound-calls/](https://docs.livekit.io/agents/v0/quickstarts/inbound-calls/)  
12. Making calls using SIP \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/agents/v0/quickstarts/outbound-calls/](https://docs.livekit.io/agents/v0/quickstarts/outbound-calls/)  
13. SIP outbound trunk \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/sip/trunk-outbound/](https://docs.livekit.io/sip/trunk-outbound/)  
14. Accepting inbound calls \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/sip/accepting-calls/](https://docs.livekit.io/sip/accepting-calls/)  
15. SIP overview \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/sip/](https://docs.livekit.io/sip/)  
16. Create and configure a Plivo SIP trunk \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/sip/quickstarts/configuring-plivo-trunk/](https://docs.livekit.io/sip/quickstarts/configuring-plivo-trunk/)  
17. Create and configure Telnyx SIP trunk \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/sip/quickstarts/configuring-telnyx-trunk/](https://docs.livekit.io/sip/quickstarts/configuring-telnyx-trunk/)  
18. livekit\_room\_track\_management\_guide.pdf  
19. LiveKit Agents, accessed June 23, 2025, [https://docs.livekit.io/agents/](https://docs.livekit.io/agents/)  
20. livekit/agents \- NPM, accessed June 23, 2025, [https://www.npmjs.com/package/@livekit/agents](https://www.npmjs.com/package/@livekit/agents)  
21. livekit/agents: A powerful framework for building realtime voice AI agents 🎙️ \- GitHub, accessed June 23, 2025, [https://github.com/livekit/agents](https://github.com/livekit/agents)  
22. Building voice agents | LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/agents/voice-agent/](https://docs.livekit.io/agents/voice-agent/)  
23. LiveKit Agents integrations, accessed June 23, 2025, [https://docs.livekit.io/agents/integrations/](https://docs.livekit.io/agents/integrations/)  
24. LiveKit real-time and server SDKs for Python \- GitHub, accessed June 23, 2025, [https://github.com/livekit/python-sdks](https://github.com/livekit/python-sdks)  
25. livekit.rtc.audio\_stream API documentation, accessed June 23, 2025, [https://docs.livekit.io/reference/python/v1/livekit/rtc/audio\_stream.html](https://docs.livekit.io/reference/python/v1/livekit/rtc/audio_stream.html)  
26. Receiving and publishing tracks \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/agents/v0/build/tracks/](https://docs.livekit.io/agents/v0/build/tracks/)  
27. livekit.rtc API documentation, accessed June 23, 2025, [https://docs.livekit.io/reference/python/livekit/rtc/index.html](https://docs.livekit.io/reference/python/livekit/rtc/index.html)  
28. Enhanced noise cancellation \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/home/<USER>/noise-cancellation/](https://docs.livekit.io/home/<USER>/noise-cancellation/)  
29. README.md \- livekit/python-sdks \- GitHub, accessed June 23, 2025, [https://github.com/livekit/python-sdks/blob/main/README.md](https://github.com/livekit/python-sdks/blob/main/README.md)  
30. livekit.rtc.audio\_mixer API documentation, accessed June 23, 2025, [https://docs.livekit.io/reference/python/v1/livekit/rtc/audio\_mixer.html](https://docs.livekit.io/reference/python/v1/livekit/rtc/audio_mixer.html)  
31. Agent speech and audio \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/agents/build/audio/](https://docs.livekit.io/agents/build/audio/)  
32. How the Eventdex AI Event Chat Assistant is Reshaping Event Engagement in 2025, accessed June 23, 2025, [https://www.eventdex.com/blog/eventdex-ai-event-chat-assistant/](https://www.eventdex.com/blog/eventdex-ai-event-chat-assistant/)  
33. How Apache Kafka and Flink Power Event-Driven Agentic AI in Real Time \- Kai Waehner, accessed June 23, 2025, [https://www.kai-waehner.de/blog/2025/04/14/how-apache-kafka-and-flink-power-event-driven-agentic-ai-in-real-time/](https://www.kai-waehner.de/blog/2025/04/14/how-apache-kafka-and-flink-power-event-driven-agentic-ai-in-real-time/)  
34. Building a real-time AI tutoring assistant \- DEV Community, accessed June 23, 2025, [https://dev.to/aws-builders/building-a-real-time-ai-tutoring-assistant-1b7h](https://dev.to/aws-builders/building-a-real-time-ai-tutoring-assistant-1b7h)  
35. Building the Ideal AI Agent: From Async Event Streams to Context-Aware State Management, accessed June 23, 2025, [https://dev.to/louis-sanna/building-the-ideal-ai-agent-from-async-event-streams-to-context-aware-state-management-33](https://dev.to/louis-sanna/building-the-ideal-ai-agent-from-async-event-streams-to-context-aware-state-management-33)  
36. Build voice AI with real-time media streaming WebSockets \- Telnyx, accessed June 23, 2025, [https://telnyx.com/resources/media-streaming-websocket](https://telnyx.com/resources/media-streaming-websocket)  
37. HTTP and Server-Sent Events · Cloudflare Agents docs, accessed June 23, 2025, [https://developers.cloudflare.com/agents/api-reference/http-sse/](https://developers.cloudflare.com/agents/api-reference/http-sse/)  
38. WebSockets and Queues: Real-Time Web Communication \- Alibaba Cloud, accessed June 23, 2025, [https://www.alibabacloud.com/tech-news/a/message\_queue/gugz0vwofw-websockets-and-queues-real-time-web-communication](https://www.alibabacloud.com/tech-news/a/message_queue/gugz0vwofw-websockets-and-queues-real-time-web-communication)  
39. Websockets for real-time updates \- Appsmith docs, accessed June 23, 2025, [https://docs.appsmith.com/build-apps/how-to-guides/set-up-websockets](https://docs.appsmith.com/build-apps/how-to-guides/set-up-websockets)  
40. Swift quickstart \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/home/<USER>/swift/](https://docs.livekit.io/home/<USER>/swift/)  
41. Android Components | LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/reference/components/android/](https://docs.livekit.io/reference/components/android/)  
42. Web and mobile frontends \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/agents/start/frontend/](https://docs.livekit.io/agents/start/frontend/)  
43. iOS Tutorial \- LiveKit tutorials, accessed June 23, 2025, [https://livekit-tutorials.openvidu.io/tutorials/application-client/ios/](https://livekit-tutorials.openvidu.io/tutorials/application-client/ios/)  
44. Android Tutorial \- LiveKit tutorials, accessed June 23, 2025, [https://livekit-tutorials.openvidu.io/tutorials/application-client/android/](https://livekit-tutorials.openvidu.io/tutorials/application-client/android/)  
45. VideoView | Documentation \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/client-sdk-swift/documentation/livekit/videoview/](https://docs.livekit.io/client-sdk-swift/documentation/livekit/videoview/)  
46. GitHub \- livekit/client-sdk-android, accessed June 23, 2025, [https://github.com/livekit/client-sdk-android](https://github.com/livekit/client-sdk-android)  
47. Documentation \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/reference/client-sdk-swift/documentation/livekit/](https://docs.livekit.io/reference/client-sdk-swift/documentation/livekit/)  
48. livekit-android-sdk, accessed June 23, 2025, [https://docs.livekit.io/reference/client-sdk-android/index.html](https://docs.livekit.io/reference/client-sdk-android/index.html)  
49. liwsy/livekit-server \- Gitee, accessed June 23, 2025, [https://gitee.com/hemsl/livekit-server](https://gitee.com/hemsl/livekit-server)  
50. Custom recording templates \- LiveKit Docs, accessed June 23, 2025, [https://docs.livekit.io/home/<USER>/custom-template/](https://docs.livekit.io/home/<USER>/custom-template/)  
51. livekit/livekit: End-to-end stack for WebRTC. SFU media server and SDKs. \- GitHub, accessed June 23, 2025, [https://github.com/livekit/livekit](https://github.com/livekit/livekit)  
52. LiveKit JS Client SDK \- v2.13.8, accessed June 23, 2025, [https://docs.livekit.io/reference/client-sdk-js/](https://docs.livekit.io/reference/client-sdk-js/)