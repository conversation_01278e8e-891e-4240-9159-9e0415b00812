# Real-Time AI Whisper Feature - Deployment Guide

Complete deployment guide for the Real-Time AI Whisper Feature with LiveKit and Twilio integration.

## 🚀 Quick Start

### Prerequisites Checklist

- [ ] Node.js 18+ and Python 3.8+
- [ ] PostgreSQL database
- [ ] LiveKit Cloud account or self-hosted instance
- [ ] Twilio account with SIP capabilities
- [ ] OpenAI API key
- [ ] Deepgram API key
- [ ] ElevenLabs API key (optional, for TTS)

## 📋 Step-by-Step Deployment

### 1. Database Setup

```bash
cd server
cp .env.example .env
# Edit .env with your database credentials
npx prisma migrate deploy
npx prisma generate
```

### 2. Server Configuration

Update `server/.env`:

```env
# Database
DATABASE_URL="postgresql://user:password@localhost:5432/talkai247"

# LiveKit
LIVEKIT_URL="wss://your-instance.livekit.cloud"
LIVEKIT_API_KEY="your_api_key"
LIVEKIT_API_SECRET="your_api_secret"

# Twilio
TWILIO_ACCOUNT_SID="your_account_sid"
TWILIO_AUTH_TOKEN="your_auth_token"
TWILIO_PHONE_NUMBER="+**********"
TWILIO_SIP_DOMAIN="your-domain.pstn.twilio.com"
TWILIO_SIP_USERNAME="your_sip_username"
TWILIO_SIP_PASSWORD="your_sip_password"

# JWT
JWT_SECRET="your_jwt_secret"
```

Start the server:
```bash
npm install
npm run dev
```

### 3. Python AI Agent Setup

```bash
cd agent-python
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
cp .env.example .env
```

Update `agent-python/.env`:

```env
# LiveKit (same as server)
LIVEKIT_URL="wss://your-instance.livekit.cloud"
LIVEKIT_API_KEY="your_api_key"
LIVEKIT_API_SECRET="your_api_secret"

# AI Services
OPENAI_API_KEY="your_openai_key"
DEEPGRAM_API_KEY="your_deepgram_key"
ELEVENLABS_API_KEY="your_elevenlabs_key"

# Server API
SERVER_API_URL="http://localhost:3030/api/v1"
DATABASE_URL="postgresql://user:password@localhost:5432/talkai247"
```

Start the agent:
```bash
python start_agent.py
```

### 4. Client Configuration

```bash
cd client
npm install
```

Update client environment variables for LiveKit and API endpoints.

Start the client:
```bash
npm run dev
```

### 5. SIP Configuration (Admin Required)

Initialize SIP configuration via API:

```bash
curl -X POST http://localhost:3030/api/v1/whisper/sip/initialize \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN" \
  -H "Content-Type: application/json"
```

Verify SIP status:
```bash
curl -X GET http://localhost:3030/api/v1/whisper/sip/status \
  -H "Authorization: Bearer YOUR_ADMIN_JWT_TOKEN"
```

## 🧪 Testing the Deployment

### Run Integration Tests

```bash
# Install test dependencies
npm install

# Run comprehensive tests
npm run test:whisper:dev
```

### Manual Testing Checklist

- [ ] User registration and login
- [ ] Contact creation
- [ ] Whisper call initiation
- [ ] Three-participant room creation
- [ ] AI agent connection
- [ ] Whisper mode switching
- [ ] Audio routing verification
- [ ] Session analytics

## 🐳 Docker Deployment

### Server and Database

```yaml
# docker-compose.yml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: talkai247
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  server:
    build: ./server
    environment:
      DATABASE_URL: ****************************************/talkai247
      # Add other environment variables
    ports:
      - "3030:3030"
    depends_on:
      - postgres

volumes:
  postgres_data:
```

### Python AI Agent

```bash
cd agent-python
docker-compose up -d
```

## 🔧 Production Configuration

### Environment Variables

Create production environment files:

```bash
# server/.env.production
NODE_ENV=production
DATABASE_URL="***********************************************/talkai247_prod"
LIVEKIT_URL="wss://prod-instance.livekit.cloud"
# ... other production values

# agent-python/.env.production
LOG_LEVEL=INFO
LOG_FORMAT=json
# ... other production values
```

### Security Considerations

1. **JWT Secrets**: Use strong, unique JWT secrets
2. **Database**: Enable SSL and use connection pooling
3. **API Keys**: Rotate keys regularly and use environment variables
4. **Network**: Use HTTPS/WSS in production
5. **Firewall**: Restrict access to necessary ports only

### Monitoring and Logging

1. **Application Logs**: Configure structured logging
2. **Database Monitoring**: Monitor connection pools and query performance
3. **LiveKit Metrics**: Monitor room creation and participant counts
4. **AI Agent Health**: Monitor agent connectivity and response times

## 📊 Performance Optimization

### Database Optimization

```sql
-- Add indexes for whisper queries
CREATE INDEX idx_calls_whisper ON "Call" ("isWhisperCall", "userId");
CREATE INDEX idx_whisper_sessions_room ON "WhisperSession" ("livekitRoomName");
CREATE INDEX idx_whisper_interactions_session ON "WhisperInteraction" ("sessionId", "timestamp");
```

### Caching Strategy

1. **Goal Caching**: Cache frequently accessed goals
2. **Trigger Caching**: Cache trigger patterns
3. **Session State**: Use Redis for session state management

### Scaling Considerations

1. **Horizontal Scaling**: Deploy multiple AI agent instances
2. **Load Balancing**: Use load balancers for server instances
3. **Database Scaling**: Consider read replicas for analytics queries

## 🚨 Troubleshooting

### Common Issues

1. **AI Agent Connection Failed**
   ```bash
   # Check LiveKit credentials
   python -c "from config import config; config.validate()"
   
   # Check network connectivity
   curl -I $LIVEKIT_URL
   ```

2. **SIP Configuration Errors**
   ```bash
   # Verify Twilio credentials
   curl -u $TWILIO_ACCOUNT_SID:$TWILIO_AUTH_TOKEN \
     https://api.twilio.com/2010-04-01/Accounts/$TWILIO_ACCOUNT_SID.json
   ```

3. **Database Connection Issues**
   ```bash
   # Test database connection
   npx prisma db pull
   ```

### Debug Mode

Enable debug logging:

```env
# Server
LOG_LEVEL=debug

# Python Agent
LOG_LEVEL=DEBUG
LOG_FORMAT=console
```

### Health Checks

```bash
# Server health
curl http://localhost:3030/health

# Agent health
docker exec whisper-ai-agent python -c "import sys; sys.exit(0)"
```

## 📈 Monitoring Dashboard

### Key Metrics to Monitor

1. **Call Metrics**
   - Active whisper calls
   - Average call duration
   - Success rate

2. **AI Agent Metrics**
   - Response time
   - Trigger accuracy
   - Agent uptime

3. **System Metrics**
   - Database performance
   - Memory usage
   - Network latency

### Alerting

Set up alerts for:
- AI agent disconnections
- High response times (>2s)
- Database connection failures
- SIP trunk errors

## 🔄 Maintenance

### Regular Tasks

1. **Database Cleanup**: Archive old sessions and interactions
2. **Log Rotation**: Rotate and archive application logs
3. **Dependency Updates**: Keep dependencies up to date
4. **Security Patches**: Apply security updates promptly

### Backup Strategy

1. **Database Backups**: Daily automated backups
2. **Configuration Backups**: Version control all configuration
3. **Disaster Recovery**: Test recovery procedures regularly

## 📞 Support

For deployment issues:

1. Check the troubleshooting guide above
2. Review application logs
3. Verify all environment variables
4. Test individual components separately
5. Consult the comprehensive test results

## 🎯 Success Criteria

Deployment is successful when:

- [ ] All integration tests pass
- [ ] Three-participant rooms are created correctly
- [ ] AI agent connects and responds within 2 seconds
- [ ] Whisper modes switch correctly
- [ ] Audio routing works as expected
- [ ] Session analytics are captured
- [ ] No critical errors in logs

Your Real-Time AI Whisper Feature is now ready for production! 🎉
