{"name": "talkai-proxy-server", "version": "1.0.0", "description": "Proxy server for TalkAI to handle CORS and API requests", "type": "module", "private": true, "main": "index.js", "scripts": {"start": "node ./src/index.js", "dev": "nodemon ./src/index.js", "db:migrate": "prisma migrate deploy", "db:seed": "prisma db seed"}, "prisma": {"seed": "node prisma/seed.js"}, "dependencies": {"@deepgram/sdk": "^3.0.0", "@prisma/client": "^6.0.1", "bcryptjs": "^2.4.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "livekit-server-sdk": "^2.10.2", "node-fetch": "^3.3.2", "openai": "^4.98.0", "twilio": "^5.5.2", "zod": "^3.22.4"}, "devDependencies": {"nodemon": "^3.0.2", "prisma": "^6.0.1", "typescript": "^5.3.3"}}