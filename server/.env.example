# Server
PORT=
NODE_ENV=

# Database
DATABASE_URL=

# JWT => node => require('crypto').randomBytes(32).toString('base64')
JWT_SECRET=
JWT_REFRESH_SECRET=
JWT_EXPIRES_IN=
JWT_REFRESH_TOKEN_EXPIRES_IN=

# ENCRYPTION_KEY Genarate => node => require('crypto').randomBytes(256).toString('base64')
ENCRYPTION_KEY=

# OpenRouter
OPENROUTER_API_KEY=
OPENROUTER_BASE_URL=

# CARTESIA
CARTESIA_API_KEY=

# PLAYHT
PLAYHT_API_KEY=
PLAYHT_USER_ID=

# ELEVENLABS
ELEVENLABS_API_KEY=

# DEEPGRAM
DEEPGRAM_API_KEY=

# LiveKit
LIVEKIT_URL=
LIVEKIT_API_KEY=
LIVEKIT_API_SECRET=

# Twilio
TWILIO_ACCOUNT_SID=
TWILIO_AUTH_TOKEN=
TWILIO_PHONE_NUMBER=
TWILIO_APPLICATION_SID=

# Twilio Stream element configuration
USE_STREAM_ELEMENT=true

# For development only - use Twimlets for TwiML
USE_TWIMLETS=false

# Public URL for Twilio callbacks (e.g., ngrok URL)
PUBLIC_URL=