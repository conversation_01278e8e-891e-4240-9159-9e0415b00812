import express from "express";
import cors from "cors";
import cookieParser from "cookie-parser";
import dotenv from "dotenv";
import path from "path";
import { fileURLToPath } from "url";
import { dirname } from "path";
import routes from "./app/routes/index.js";
import config from "./config/config.js";

// Get the directory path
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables from parent directory's .env file
dotenv.config({ path: path.join(__dirname, "..", ".env") });
const app = express();
const port = config.server.port || 3030;

// Configure CORS to be more permissive during development
// const corsOptions = {
//   origin: function (origin, callback) {
//     // Allow requests with no origin (like mobile apps or curl requests)
//     if (!origin) return callback(null, true);

//     // Allow any localhost origin
//     if (
//       (origin.match(/^http:\/\/localhost(:\d+)?$/),
//       origin.match(/^https?:\/\/(app\.)?talkai\.site$/))
//     ) {
//       callback(null, true);
//     } else {
//       callback(new Error("Not allowed by CORS"));
//     }
//   },
//   methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
//   allowedHeaders: [
//     "Content-Type",
//     "Authorization",
//     "X-API-Key",
//     "Cartesia-Version",
//   ],
// };
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps, curl requests, or server-to-server)
    if (!origin) return callback(null, true);

    const allowedOrigins = [
      // Local development
      /^http:\/\/localhost(:\d+)?$/,
      /^http:\/\/127\.0\.0\.1(:\d+)?$/,

      // Your production domains
      /^https?:\/\/(app\.)?talkai\.site$/,
      /^https?:\/\/(www\.)?talkai\.com$/,
      /^https?:\/\/(b407-27-147-200-7\.)?ngrok-free\.app$/,

      // Add any other domains or patterns you need
    ];

    // Check if the origin matches any of the allowed patterns
    const isAllowed = allowedOrigins.some((regex) => regex.test(origin));

    if (isAllowed) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked for origin: ${origin}`);
      callback(new Error("Not allowed by CORS"));
    }
  },
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-API-Key",
    "Cartesia-Version",
    "Accept",
    "X-Requested-With",
  ],
  credentials: true, // Enable if you need to support cookies/auth headers
  optionsSuccessStatus: 200, // Some legacy browsers choke on 204
};

app.use(cors(corsOptions));
app.use(cookieParser());
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

app.use("/api/v1", routes);

// Error handling middleware
app.use((err, req, res, next) => {
  console.error("Error:", err);
  res.status(500).json({ error: err.message });
});

app
  .listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
  })
  .on("error", (error) => {
    if (error.code === "EADDRINUSE") {
      console.error(
        `Port ${port} is already in use. Please ensure no other server is running.`
      );
      process.exit(1);
    } else {
      console.error("Server error:", error);
      process.exit(1);
    }
  });
