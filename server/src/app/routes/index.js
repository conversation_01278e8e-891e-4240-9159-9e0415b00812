import express from "express";
import { AuthRoute } from "../modules/auth/auth.route.js";

import { UserRoute } from "../modules/user/user.route.js";
import { AssistantRoute } from "../modules/assistant/assistant.route.js";
import { TemplateRoute } from "../modules/template/template.route.js";
import { LivekitRoute } from "../modules/livekit/livekit.route.js";
import { OpenrouterRoute } from "../modules/openrouter/openrouter.route.js";
import { DeepgramRoute } from "../modules/deepgram/deepgram.route.js";
import { CartesiaRoute } from "../modules/cartesia/cartesia.route.js";
import { PlayhtRoute } from "../modules/playht/playht.route.js";
import { PublicRoute } from "../modules/public/public.route.js";
import WhisperRoute from "../modules/whisper/whisper.route.js";
import { ContactRoute } from "../modules/contact/contact.route.js";
import { TwilioRoute } from "../modules/twilio/twilio.route.js";
import { CampaignRoute } from "../modules/campaign/campaign.route.js";
import { DirectCallRoute } from "../modules/directCall/directCall.route.js";
import { WhisperAgentRoute } from "../modules/directCall/whisperAgent.route.js";

const router = express.Router();

const moduleRoutes = [
  {
    path: "/auth",
    route: AuthRoute,
  },
  {
    path: "/user",
    route: UserRoute,
  },
  {
    path: "/assistants",
    route: AssistantRoute,
  },
  {
    path: "/templates",
    route: TemplateRoute,
  },
  {
    path: "/livekit",
    route: LivekitRoute,
  },
  {
    path: "/openrouter",
    route: OpenrouterRoute,
  },
  {
    path: "/deepgram",
    route: DeepgramRoute,
  },
  {
    path: "/cartesia",
    route: CartesiaRoute,
  },
  {
    path: "/playht",
    route: PlayhtRoute,
  },
  {
    path: "/public",
    route: PublicRoute,
  },
  {
    path: "/whisper",
    route: WhisperRoute,
  },
  {
    path: "/contacts",
    route: ContactRoute,
  },
  {
    path: "/campaigns",
    route: CampaignRoute,
  },
  {
    path: "/twilio",
    route: TwilioRoute,
  },
  {
    path: "/direct-call",
    route: DirectCallRoute,
  },
  {
    path: "/whisper-agent",
    route: WhisperAgentRoute,
  },
];

moduleRoutes.forEach((route) => router.use(route.path, route.route));
export default router;
