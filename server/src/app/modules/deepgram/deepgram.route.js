import express from "express";

import { authenticateJWT } from "../../../middleware/authMiddleware.js";
import {
  getDeepgramHealth,
  getDeepgramSpeech,
  getDeepgramVoices,
} from "./deepgram.controller.js";

const router = express.Router();

router.post("/speech", authenticateJWT, getDeepgramSpeech);
router.get("/voices", authenticateJWT, getDeepgramVoices);
router.get("/health", authenticateJWT, getDeepgramHealth);
// router.get("/me", authenticateJWT, getCurrentUser);

export const DeepgramRoute = router;
