import config from "../../../config/config.js";

// Check if we have the Deepgram API key
const deepgramApiKey = config.apis.deepgram.apiKey;
if (!deepgramApiKey) {
  console.error("DEEPGRAM_API_KEY environment variable is not set");
  process.exit(1);
}
export const getDeepgramSpeech = async (req, res) => {
  try {
    const { text, voice } = req.body;

    if (!text || !voice) {
      return res
        .status(400)
        .json({ error: "Missing required parameters: text and voice" });
    }

    console.log("Generating speech:", { text, voice });

    try {
      const response = await fetch("https://api.deepgram.com/v1/speak", {
        method: "POST",
        headers: {
          Authorization: `Token ${deepgramApiKey}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          text: text,
          model_id: voice,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Deepgram API error response:", errorText);
        throw new Error(`Deepgram API error: ${response.status} ${errorText}`);
      }

      const audioBuffer = await response.arrayBuffer();
      console.log("Got Deepgram response");

      res.set("Content-Type", "audio/mpeg");
      res.send(Buffer.from(audioBuffer));
    } catch (deepgramError) {
      console.error("Deepgram API error:", deepgramError);
      res.status(500).json({
        error: "Deepgram API error",
        details: deepgramError.message,
        apiKey: deepgramApiKey ? "present" : "missing",
      });
    }
  } catch (error) {
    console.error("Error generating speech:", error);
    res.status(500).json({ error: error.message });
  }
};
export const getDeepgramVoices = async (req, res) => {
  try {
    console.log("Fetching Deepgram voices...");
    console.log(
      "Using Deepgram API Key:",
      deepgramApiKey ? `${deepgramApiKey.slice(0, 4)}...` : "missing"
    );

    const response = await fetch("https://api.deepgram.com/v1/models", {
      headers: {
        Authorization: `Token ${deepgramApiKey}`,
      },
    });

    console.log("Deepgram API Response Status:", response.status);
    console.log(
      "Deepgram API Response Headers:",
      Object.fromEntries(response.headers.entries())
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Deepgram API Error Response:", errorText);
      throw new Error(
        `Failed to fetch voices: ${response.statusText}. ${errorText}`
      );
    }

    const data = await response.json();
    // console.log("Deepgram API Response Data:", JSON.stringify(data, null, 2));

    const voices =
      data.tts?.map((voice) => ({
        model_id: voice.canonical_name,
        name: voice.name,
        language: voice.language,
        gender: voice.metadata?.tags?.includes("masculine") ? "male" : "female",
        description: `${voice.metadata?.accent || ""} accent`,
        preview_url: voice.metadata?.sample || "",
        provider: "Deepgram",
      })) || [];

    console.log(`Found ${voices.length} Deepgram voices`);
    res.json(voices);
  } catch (error) {
    console.error("Error fetching Deepgram voices:", error);
    res.status(500).json({ error: error.message });
  }
};

export const getDeepgramHealth = (req, res) => {
  res.json({
    status: "ok",
    deepgramApiKey: deepgramApiKey ? "present" : "missing",
  });
};
