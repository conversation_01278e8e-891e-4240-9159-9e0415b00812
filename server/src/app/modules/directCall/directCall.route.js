import express from "express";
import {
  initiateDirectCall,
  generateDirectCallTwiML,
  endDirectCall,
  handleDirectCallStatus,
  generateClientToken,
  clientTwiml,
  getDirectCall,
  streamEvents,
} from "./directCall.controller.js";
import { authenticateJWT } from "../../../middleware/authMiddleware.js";

const router = express.Router();

// Protected routes
router.post("/", authenticateJWT, initiateDirectCall);
router.post("/end", authenticateJWT, endDirectCall);
router.get("/token", authenticateJWT, generateClientToken);
// Add this new route for Twilio stream events
router.post("/stream-events", streamEvents);

// Add this new route
router.get("/livekit-info/:callSid", authenticateJWT, getDirectCall);
// Public routes for Twilio callbacks
router.get("/twiml/:callId", generateDirectCallTwiML);
router.post("/twiml/:callId", generateDirectCallTwiML);
router.post("/status", handleDirectCallStatus);

// Add a route for client-initiated calls
router.post("/client-twiml", clientTwiml);

export const DirectCallRoute = router;
