import express from "express";
import { whisperAgent } from "./whisperAgent.service.js";
import { authenticateJWT } from "../../../middleware/authMiddleware.js";

const router = express.Router();

// Start monitoring a room
router.post("/start", authenticateJWT, async (req, res) => {
  try {
    const { roomName } = req.body;

    if (!roomName) {
      return res.status(400).json({
        success: false,
        error: { code: "INVALID_INPUT", message: "Room name is required" },
      });
    }

    await whisperAgent.processRoomAudio(roomName);

    res.json({
      success: true,
      data: { message: "Whisper agent started monitoring room" },
    });
  } catch (error) {
    console.error("Error starting whisper agent:", error);
    res.status(500).json({
      success: false,
      error: { code: "SERVER_ERROR", message: "Failed to start whisper agent" },
    });
  }
});

// Stop monitoring a room
router.post("/stop", authenticateJWT, async (req, res) => {
  try {
    const { roomName } = req.body;

    if (!roomName) {
      return res.status(400).json({
        success: false,
        error: { code: "INVALID_INPUT", message: "Room name is required" },
      });
    }

    whisperAgent.stopMonitoring(roomName);

    res.json({
      success: true,
      data: { message: "Whisper agent stopped monitoring room" },
    });
  } catch (error) {
    console.error("Error stopping whisper agent:", error);
    res.status(500).json({
      success: false,
      error: { code: "SERVER_ERROR", message: "Failed to stop whisper agent" },
    });
  }
});

export const WhisperAgentRoute = router;
