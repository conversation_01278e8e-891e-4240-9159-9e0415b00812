import { RoomServiceClient } from "livekit-server-sdk";
import { OpenAI } from "openai";
import config from "../../../config/config.js";

class WhisperAgent {
  constructor() {
    this.roomService = new RoomServiceClient(
      config.liveKit.url,
      config.liveKit.apiKey,
      config.liveKit.apiSecret
    );

    this.openai = new OpenAI({
      apiKey: config.apis.openAPI.apiKey,
    });

    this.activeRooms = new Map();
  }

  async processRoomAudio(roomName) {
    try {
      // Get participants in the room
      const participants = await this.roomService.listParticipants(roomName);

      // Find customer participant (you'll need to identify them)
      const customerParticipant = participants.find((p) =>
        p.identity.startsWith("customer_")
      );

      if (customerParticipant) {
        // Here you would process the audio tracks
        // In a real implementation, you'd subscribe to the audio tracks
        // and process them in real-time

        // For now, we'll simulate processing
        setInterval(async () => {
          // Generate a mock suggestion
          const suggestion = await this.generateSuggestion(
            "Customer asked about pricing"
          );

          // Send suggestion to agent
          this.sendSuggestion(roomName, suggestion);
        }, 15000); // Every 15 seconds
      }
    } catch (error) {
      console.error("Error processing room audio:", error);
    }
  }

  async generateSuggestion(context) {
    try {
      const response = await this.openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content:
              "You are a sales assistant. Provide short, actionable suggestions to help close the deal.",
          },
          {
            role: "user",
            content: context,
          },
        ],
        max_tokens: 100,
      });

      return response.choices[0].message.content;
    } catch (error) {
      console.error("Error generating suggestion:", error);
      return "Error generating suggestion";
    }
  }

  async sendSuggestion(roomName, suggestion) {
    try {
      await this.roomService.sendData({
        roomName,
        data: JSON.stringify({
          type: "suggestion",
          message: suggestion,
        }),
        topic: "whisper",
      });
    } catch (error) {
      console.error("Error sending suggestion:", error);
    }
  }
}

export const whisperAgent = new WhisperAgent();
