import { prisma } from "../../../lib/prisma.js";
import twilioService from "../../../services/twilioService.js";
import config from "../../../config/config.js";
import { whisperAgent } from "./whisperAgent.service.js";
import { AccessToken } from "livekit-server-sdk";

/**
 * Initiate a direct call between the web browser and a phone number
 * This function now supports two modes:
 * 1. Server-initiated calls (legacy mode)
 * 2. Client-initiated calls (new mode) where the browser initiates the call directly
 */
export const initiateDirectCall = async (req, res) => {
  try {
    const userId = req.user.id;
    const { contactId, callSid } = req.body;
    console.log("callSid", callSid);
    if (!contactId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Contact ID is required",
        },
      });
    }

    // Check if contact exists and belongs to user
    const contact = await prisma.contact.findFirst({
      where: {
        id: contactId,
        userId,
      },
    });

    if (!contact) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Contact not found or not owned by user",
        },
      });
    }

    // Find a default assistant for the user
    let assistant = await prisma.assistant.findFirst({
      where: {
        userId,
      },
    });

    // If no assistant exists, we need to handle this case
    if (!assistant) {
      return res.status(400).json({
        success: false,
        error: {
          code: "ASSISTANT_REQUIRED",
          message:
            "No assistant found for this user. Please create an assistant first.",
        },
      });
    }
    // Generate LiveKit room name and token
    const roomName = `call_${userId}_${contactId}_${Date.now()}`;
    const participantIdentity = `agent_${userId}`;

    const API_KEY = config.liveKit.apiKey;
    const API_SECRET = config.liveKit.apiSecret;
    const at = new AccessToken(API_KEY, API_SECRET, {
      identity: participantIdentity,
      ttl: "60m",
    });

    at.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: false, // Agent only listens
      canSubscribe: true,
      canPublishData: true, // For sending suggestions
    });

    const agentToken = at.toJwt();
    // Create a call record in the database
    const call = await prisma.call.create({
      data: {
        userId,
        contactId,
        assistantId: assistant.id, // Using the user's assistant
        startTime: new Date(),
        status: "IN_PROGRESS",
        transcript: [],
        goals: [],
        metrics: {},
        // If we have a callSid from the client, use it
        twilioSid: callSid,
      },
    });

    // If callSid is provided, this is a client-initiated call
    // We just need to record it, not make a new call
    if (callSid) {
      console.log(`Client-initiated call with SID ${callSid} recorded`);

      // Return success response
      return res.json({
        success: true,
        data: {
          callId: call.id,
          twilioSid: callSid,
          twilioStatus: "in-progress", // Assume the call is in progress
          contactName: contact.name,
          contactPhone: contact.phone,
          liveKit: {
            url: config.liveKit.url,
            room: roomName,
            token: agentToken,
          },
        },
      });
    }

    // Get the base URL for callbacks
    // For development, we need a publicly accessible URL
    let baseUrl;

    // Check if we're using a tunneling service like ngrok
    if (process.env.PUBLIC_URL) {
      // Use the configured public URL
      baseUrl = process.env.PUBLIC_URL;
      console.log(`Using configured PUBLIC_URL: ${baseUrl}`);
    } else if (
      req.headers["x-forwarded-host"] &&
      req.headers["x-forwarded-host"].includes("ngrok")
    ) {
      // If using ngrok, extract the ngrok URL
      const protocol = "https";
      const host = req.headers["x-forwarded-host"];
      baseUrl = `${protocol}://${host}`;
      console.log(`Detected ngrok URL: ${baseUrl}`);
    } else {
      // Fallback to the request URL (which might not be publicly accessible)
      const protocol = req.headers["x-forwarded-proto"] || req.protocol;
      const host = req.headers["x-forwarded-host"] || req.get("host");
      baseUrl = `${protocol}://${host}`;
      console.log(
        `Using request URL: ${baseUrl} (may not be publicly accessible)`
      );
    }

    // Generate TwiML for a direct call
    let twimlUrl = `${baseUrl}/api/v1/direct-call/twiml/${call.id}`;

    // For development without a public URL, use Twilio's echo service as a fallback
    if (
      process.env.NODE_ENV !== "production" &&
      process.env.USE_TWIMLETS === "true"
    ) {
      const twiml = `
        <Response>
          <Say>Hello, you are receiving a call from a web browser.</Say>
          <Say>Please wait while we connect you.</Say>
          <Dial callerId="${config.twilio.phoneNumber}">
            <Client>browser-${userId}</Client>
          </Dial>
          <Say>The call has ended. Thank you for using our service.</Say>
        </Response>
      `;
      const encodedTwiml = encodeURIComponent(twiml);
      const twimletUrl = `https://twimlets.com/echo?Twiml=${encodedTwiml}`;
      console.log(`Using Twimlets echo service: ${twimletUrl}`);
      twimlUrl = twimletUrl;
    }

    // Initiate the outbound call using Twilio
    const twilioCall = await twilioService.makeOutboundCall(
      contact.phone,
      twimlUrl
    );

    // Update the call record with Twilio SID
    await prisma.call.update({
      where: { id: call.id },
      data: {
        twilioSid: twilioCall.sid,
      },
    });

    // Return success response
    res.json({
      success: true,
      data: {
        callId: call.id,
        twilioSid: twilioCall.sid,
        twilioStatus: twilioCall.status,
        contactName: contact.name,
        contactPhone: contact.phone,
      },
    });
  } catch (error) {
    console.error("Error initiating direct call:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to initiate direct call",
        details: error.message,
      },
    });
  }
};

export const getDirectCall = async (req, res) => {
  try {
    const { callSid } = req.params;
    console.log("callSid", callSid);
    // Find the call in database
    const call = await prisma.call.findFirst({
      where: { twilioSid: callSid },
      // select: { liveKitRoom: true, liveKitToken: true },
    });
    console.log("call", call);
    if (!call || !call.liveKitRoom || !call.liveKitToken) {
      return res.status(404).json({
        success: false,
        error: { code: "NOT_FOUND", message: "LiveKit info not found" },
      });
    }

    res.json({
      success: true,
      data: {
        liveKit: {
          url: config.liveKit.url,
          room: call.liveKitRoom,
          token: call.liveKitToken,
        },
      },
    });
  } catch (error) {
    console.error("Error getting LiveKit info:", error);
    res.status(500).json({
      success: false,
      error: { code: "SERVER_ERROR", message: "Failed to get LiveKit info" },
    });
  }
};

export const clientTwiml = async (req, res) => {
  console.log("Client-initiated call TwiML request received");
  console.log("Request body:", req.body);

  // Generate TwiML for a direct call from browser to phone
  const twiml = `
    <Response>
      <Dial callerId="${config.twilio.phoneNumber}" record="record-from-answer">
        <Number>${req.body.To}</Number>
      </Dial>
      <Say>The call has ended. Thank you for using our service.</Say>
    </Response>
  `;

  console.log("Generated TwiML for client-initiated call:", twiml);

  res.set("Content-Type", "text/xml");
  res.send(twiml);
};

/**
 * Generate TwiML for a direct call
 */
export const generateDirectCallTwiML = async (req, res) => {
  try {
    console.log(
      `Direct call TwiML request received for call ID: ${req.params.callId}`
    );
    console.log(`Request query params:`, req.query);
    console.log(`Request body:`, req.body);

    const { callId } = req.params;

    // Get the call details
    const call = await prisma.call.findUnique({
      where: { id: callId },
      include: {
        contact: true,
      },
    });

    if (!call) {
      console.error(`Call not found with ID: ${callId}`);
      // Return a simple TwiML response
      res.set("Content-Type", "text/xml");
      return res.send(`
        <Response>
          <Say>Sorry, there was an error with this call. The call ID was not found.</Say>
        </Response>
      `);
    }

    // Generate TwiML for a direct call
    // For a direct call from browser to phone, we need to:
    // 1. Answer the outbound call that Twilio made to the phone number
    // 2. Connect that call to the browser client
    const twiml = `
      <Response>
        <Say>Hello, you are receiving a call from a web browser.</Say>
        <Say>Please wait while we connect you.</Say>
        <Dial callerId="${config.twilio.phoneNumber}">
          <Client>browser-${call.userId}</Client>
        </Dial>
        <Say>The call has ended. Thank you for using our service.</Say>
      </Response>
    `;

    console.log("Generated TwiML for direct call:", twiml);

    res.set("Content-Type", "text/xml");
    res.send(twiml);
  } catch (error) {
    console.error("Error generating direct call TwiML:", error);
    res.set("Content-Type", "text/xml");
    res.send(`
      <Response>
        <Say>Sorry, there was an error processing this call.</Say>
      </Response>
    `);
  }
};

export const streamEvents = async (req, res) => {
  try {
    console.log("Twilio stream event received:", req.body);

    // Verify this is coming from Twilio
    const signature = req.headers["x-twilio-signature"];
    const url = `${config.server.apiURL}/direct-call/stream-events`;
    const params = req.body;

    if (!twilioService.validateRequest(signature, url, params)) {
      console.warn("Invalid Twilio signature");
      return res.status(403).send("Invalid signature");
    }

    const { room, event, streamSid } = req.body;

    if (event === "start") {
      // Stream started - notify whisper agent
      await whisperAgent.processRoomAudio(room);
    } else if (event === "stop") {
      // Stream ended - clean up
      whisperAgent.stopMonitoring(room);
    }

    res.status(200).send("OK");
  } catch (error) {
    console.error("Error handling stream event:", error);
    res.status(500).send("Error processing event");
  }
};

/**
 * End a direct call
 */
export const endDirectCall = async (req, res) => {
  try {
    const userId = req.user.id;
    console.log(`Attempting to end call for user ${userId}`);

    // Find the active call for this user
    const activeCall = await prisma.call.findFirst({
      where: {
        userId,
        status: "IN_PROGRESS",
      },
    });

    if (!activeCall) {
      console.log(`No active call found for user ${userId}`);
      // Even if no active call is found, return success to allow the UI to reset
      return res.json({
        success: true,
        data: {
          message: "No active call found, but UI state has been reset",
        },
      });
    }

    console.log(
      `Found active call with ID ${activeCall.id} and Twilio SID ${activeCall.twilioSid}`
    );

    // End the Twilio call if we have a SID
    if (activeCall.twilioSid) {
      try {
        console.log(
          `Attempting to end Twilio call with SID ${activeCall.twilioSid}`
        );
        const twilioResponse = await twilioService.endCall(
          activeCall.twilioSid
        );
        console.log(`Twilio call end response:`, twilioResponse);
      } catch (error) {
        console.error(
          `Error ending Twilio call with SID ${activeCall.twilioSid}:`,
          error
        );
        // Continue even if Twilio call ending fails
      }
    } else {
      console.log(`No Twilio SID found for call ${activeCall.id}`);
    }

    // Update the call record
    const endTime = new Date();
    const duration = Math.round(
      (endTime.getTime() - new Date(activeCall.startTime).getTime()) / 1000
    );

    console.log(`Updating call record with duration ${duration} seconds`);
    await prisma.call.update({
      where: { id: activeCall.id },
      data: {
        status: "COMPLETED",
        endTime,
        duration,
      },
    });

    // Return success response
    res.json({
      success: true,
      data: {
        callId: activeCall.id,
        duration,
        status: "COMPLETED",
      },
    });
  } catch (error) {
    console.error("Error ending direct call:", error);
    // Still return a 200 response to allow the UI to reset
    res.json({
      success: true,
      data: {
        message: "Error occurred, but UI state has been reset",
        error: error.message,
      },
    });
  }
};

/**
 * Handle call status updates from Twilio
 */
export const handleDirectCallStatus = async (req, res) => {
  try {
    console.log("Direct call status update received:", req.body);
    const { CallSid, CallStatus } = req.body;

    // Always respond with 200 OK to Twilio, even if we have issues processing
    // This prevents Twilio from retrying and potentially causing more issues

    if (!CallSid) {
      console.warn("CallSid is missing in status callback");
      return res.status(200).send("OK");
    }

    // Find the call with this Twilio SID
    const call = await prisma.call.findFirst({
      where: {
        twilioSid: CallSid,
      },
    });

    if (!call) {
      console.warn(`Call not found with Twilio SID: ${CallSid}`);
      return res.status(200).send("OK");
    }

    // Update the call status based on Twilio status
    if (
      CallStatus === "completed" ||
      CallStatus === "busy" ||
      CallStatus === "failed" ||
      CallStatus === "no-answer" ||
      CallStatus === "canceled"
    ) {
      const endTime = new Date();
      const duration = Math.round(
        (endTime.getTime() - new Date(call.startTime).getTime()) / 1000
      );

      await prisma.call.update({
        where: { id: call.id },
        data: {
          status: "COMPLETED",
          endTime,
          duration,
        },
      });
    }

    res.status(200).send("OK");
  } catch (error) {
    console.error("Error handling direct call status:", error);
    // Still return 200 OK to Twilio even if we have an error
    console.error(
      "Error processing status update, but returning 200 OK to Twilio"
    );
    res.status(200).send("OK");
  }
};

/**
 * Generate a Twilio capability token for browser client
 */
export const generateClientToken = async (req, res) => {
  try {
    const userId = req.user.id;

    // Generate a Twilio Client capability token
    const token = twilioService.generateClientToken(`browser-${userId}`);

    res.json({
      success: true,
      data: {
        token,
        identity: `browser-${userId}`,
      },
    });
  } catch (error) {
    console.error("Error generating client token:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to generate client token",
        details: error.message,
      },
    });
  }
};
