import express from "express";
import { authenticateJWT } from "../../../middleware/authMiddleware.js";
import {
  getAllContacts,
  getContactById,
  createContact,
  updateContact,
  deleteContact,
  bulkDeleteContacts,
  searchContacts,
  mergeContacts
} from "./contact.controller.js";

const router = express.Router();

// All routes require authentication
router.use(authenticateJWT);

// Get all contacts
router.get("/", getAllContacts);

// Search contacts
router.get("/search", searchContacts);

// Get a single contact
router.get("/:id", getContactById);

// Create a new contact
router.post("/", createContact);

// Update a contact
router.put("/:id", updateContact);

// Delete a contact
router.delete("/:id", deleteContact);

// Bulk delete contacts
router.post("/bulk-delete", bulkDeleteContacts);

// Merge contacts
router.post("/merge", mergeContacts);

export const ContactRoute = router;
