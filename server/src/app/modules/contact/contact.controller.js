import { prisma } from "../../../lib/prisma.js";

// Get all contacts for a user
export const getAllContacts = async (req, res) => {
  try {
    const userId = req.user.id;

    const contacts = await prisma.contact.findMany({
      where: { userId },
      orderBy: { updatedAt: "desc" },
    });

    res.status(200).json(contacts);
  } catch (error) {
    console.error("Error fetching contacts:", error);
    res.status(500).json({
      error: "Failed to fetch contacts",
      details: error.message,
    });
  }
};

// Get a single contact by ID
export const getContactById = async (req, res) => {
  try {
    const userId = req.user.id;
    const contactId = req.params.id;

    const contact = await prisma.contact.findFirst({
      where: {
        id: contactId,
        userId,
      },
    });

    if (!contact) {
      return res.status(404).json({ error: "Contact not found" });
    }

    res.status(200).json(contact);
  } catch (error) {
    console.error("Error fetching contact:", error);
    res.status(500).json({
      error: "Failed to fetch contact",
      details: error.message,
    });
  }
};

// Create a new contact
export const createContact = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      name,
      email,
      phone,
      type,
      transparencyLevel,
      subcategory,
      customSubcategory,
      campaignId,
      tags = [],
      notes,
    } = req.body;

    // Validate required fields
    if (!name || !email || !phone) {
      return res
        .status(400)
        .json({ error: "Name, email, and phone are required" });
    }

    // Map the type and transparencyLevel to match the enum values in the database
    const contactType =
      type?.toUpperCase() === "PERSONAL" ? "PERSONAL" : "BUSINESS";
    const transparency =
      transparencyLevel?.toUpperCase() === "FULL"
        ? "FULL"
        : transparencyLevel?.toUpperCase() === "PARTIAL"
        ? "PARTIAL"
        : "NONE";

    const newContact = await prisma.contact.create({
      data: {
        userId,
        name,
        email,
        phone,
        type: contactType,
        transparencyLevel: transparency,
        subcategory,
        customSubcategory,
        campaignId,
        tags,
        notes,
      },
    });

    res.status(201).json(newContact);
  } catch (error) {
    console.error("Error creating contact:", error);
    res.status(500).json({
      error: "Failed to create contact",
      details: error.message,
    });
  }
};

// Update an existing contact
export const updateContact = async (req, res) => {
  try {
    const userId = req.user.id;
    const contactId = req.params.id;
    console.log("req.body", req.body);
    const {
      name,
      email,
      phone,
      type,
      transparencyLevel,
      subcategory,
      customSubcategory,
      campaignId,
      tags,
      notes,
      campaignName,
    } = req.body;

    // Check if contact exists and belongs to user
    const existingContact = await prisma.contact.findFirst({
      where: {
        id: contactId,
        userId,
      },
    });

    if (!existingContact) {
      return res.status(404).json({ error: "Contact not found" });
    }

    // Map the type and transparencyLevel to match the enum values in the database
    const contactType =
      type?.toUpperCase() === "PERSONAL" ? "PERSONAL" : "BUSINESS";
    const transparency =
      transparencyLevel?.toUpperCase() === "FULL"
        ? "FULL"
        : transparencyLevel?.toUpperCase() === "PARTIAL"
        ? "PARTIAL"
        : "NONE";

    const updatedContact = await prisma.contact.update(
      {
        where: { id: contactId },
        data: {
          name,
          email,
          phone,
          type: contactType,
          transparencyLevel: transparency,
          subcategory,
          customSubcategory,
          campaignId,
          tags,
          notes,
          updatedAt: new Date(),
        },
      },
      {
        include: {
          campaign: true,
        },
      }
    );

    res.status(200).json(updatedContact);
  } catch (error) {
    console.error("Error updating contact:", error);
    res.status(500).json({
      error: "Failed to update contact",
      details: error.message,
    });
  }
};

// Delete a contact
export const deleteContact = async (req, res) => {
  try {
    const userId = req.user.id;
    const contactId = req.params.id;

    // Check if contact exists and belongs to user
    const existingContact = await prisma.contact.findFirst({
      where: {
        id: contactId,
        userId,
      },
    });

    if (!existingContact) {
      return res.status(404).json({ error: "Contact not found" });
    }

    await prisma.contact.delete({
      where: { id: contactId },
    });

    res.status(200).json({ message: "Contact deleted successfully" });
  } catch (error) {
    console.error("Error deleting contact:", error);
    res.status(500).json({
      error: "Failed to delete contact",
      details: error.message,
    });
  }
};

// Bulk delete contacts
export const bulkDeleteContacts = async (req, res) => {
  try {
    const userId = req.user.id;
    const { contactIds } = req.body;

    if (!contactIds || !Array.isArray(contactIds) || contactIds.length === 0) {
      return res.status(400).json({ error: "Contact IDs array is required" });
    }

    // Check if all contacts belong to the user
    const contacts = await prisma.contact.findMany({
      where: {
        id: { in: contactIds },
        userId,
      },
    });

    if (contacts.length !== contactIds.length) {
      return res.status(403).json({
        error: "Some contacts were not found or do not belong to you",
      });
    }

    // Delete all contacts
    await prisma.contact.deleteMany({
      where: {
        id: { in: contactIds },
        userId,
      },
    });

    res
      .status(200)
      .json({ message: `${contacts.length} contacts deleted successfully` });
  } catch (error) {
    console.error("Error bulk deleting contacts:", error);
    res.status(500).json({
      error: "Failed to delete contacts",
      details: error.message,
    });
  }
};

// Search contacts
export const searchContacts = async (req, res) => {
  try {
    const userId = req.user.id;
    const { query, type, subcategory } = req.query;

    const whereClause = { userId };

    // Add search conditions if query is provided
    if (query) {
      whereClause.OR = [
        { name: { contains: query, mode: "insensitive" } },
        { email: { contains: query, mode: "insensitive" } },
        { phone: { contains: query, mode: "insensitive" } },
      ];
    }

    // Add type filter if provided
    if (type) {
      whereClause.type = type.toUpperCase();
    }

    // Add subcategory filter if provided
    if (subcategory) {
      whereClause.subcategory = subcategory;
    }

    const contacts = await prisma.contact.findMany({
      where: whereClause,
      orderBy: { updatedAt: "desc" },
    });

    res.status(200).json(contacts);
  } catch (error) {
    console.error("Error searching contacts:", error);
    res.status(500).json({
      error: "Failed to search contacts",
      details: error.message,
    });
  }
};

// Merge contacts
export const mergeContacts = async (req, res) => {
  try {
    const userId = req.user.id;
    const { primaryContactId, secondaryContactIds } = req.body;

    if (
      !primaryContactId ||
      !secondaryContactIds ||
      !Array.isArray(secondaryContactIds) ||
      secondaryContactIds.length === 0
    ) {
      return res.status(400).json({
        error:
          "Primary contact ID and secondary contact IDs array are required",
      });
    }

    // Check if primary contact exists and belongs to user
    const primaryContact = await prisma.contact.findFirst({
      where: {
        id: primaryContactId,
        userId,
      },
    });

    if (!primaryContact) {
      return res.status(404).json({ error: "Primary contact not found" });
    }

    // Check if all secondary contacts exist and belong to user
    const secondaryContacts = await prisma.contact.findMany({
      where: {
        id: { in: secondaryContactIds },
        userId,
      },
    });

    if (secondaryContacts.length !== secondaryContactIds.length) {
      return res.status(403).json({
        error: "Some secondary contacts were not found or do not belong to you",
      });
    }

    // Update calls to point to the primary contact
    await prisma.call.updateMany({
      where: {
        contactId: { in: secondaryContactIds },
        userId,
      },
      data: { contactId: primaryContactId },
    });

    // Delete secondary contacts
    await prisma.contact.deleteMany({
      where: {
        id: { in: secondaryContactIds },
        userId,
      },
    });

    // Get updated primary contact
    const updatedPrimaryContact = await prisma.contact.findUnique({
      where: { id: primaryContactId },
    });

    res.status(200).json({
      message: "Contacts merged successfully",
      contact: updatedPrimaryContact,
    });
  } catch (error) {
    console.error("Error merging contacts:", error);
    res.status(500).json({
      error: "Failed to merge contacts",
      details: error.message,
    });
  }
};
