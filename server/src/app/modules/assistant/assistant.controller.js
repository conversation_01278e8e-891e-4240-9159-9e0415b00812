import { prisma } from "../../../lib/prisma.js";

// Get all assistants
export const getAllAssistants = async (req, res) => {
  try {
    const assistants = await prisma.assistant.findMany({
      orderBy: { createdAt: "desc" },
    });
    res.json({ success: true, data: assistants });
  } catch (error) {
    console.error("Error fetching assistants:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch assistants",
        details: error.message,
      },
    });
  }
};

// Create assistant
export const createAssistant = async (req, res) => {
  try {
    console.log("Creating assistant:", req.body);
    console.log("req.user.id", req.user.id);
    // First, get a valid user from the database
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
    });
    if (!user) {
      return res.status(400).json({
        success: false,
        error: {
          code: "NO_USER_FOUND",
          message: "No user found in the database. Please create a user first.",
        },
      });
    }

    // Destructure all potential fields from req.body, including templateId
    const {
      templateId,
      userPhoneNumberId, // New field for phone number assignment
      name,
      modes,
      firstMessage,
      systemPrompt,
      provider,
      model,
      tools,
      voice,
      isActive, // Explicitly include isActive
      // Deprecated fields (template, voiceProvider, voiceId, volume) are ignored by not destructuring them
      ...rest // Collect any other fields, though they won't be used directly in assistant creation
    } = req.body;

    let newAssistantData = {};

    // 3. Fetch and Apply Template (if templateId is provided)
    if (templateId) {
      const assistantTemplate = await prisma.assistantTemplate.findUnique({
        where: { id: templateId },
      });

      if (!assistantTemplate) {
        return res.status(404).json({
          success: false,
          error: {
            code: "TEMPLATE_NOT_FOUND",
            message: "Assistant template not found.",
          },
        });
      }

      // Check template accessibility (system or user-owned)
      if (
        assistantTemplate.type !== "system" &&
        assistantTemplate.createdBy !== req.user.id
      ) {
        return res.status(403).json({
          success: false,
          error: {
            code: "TEMPLATE_FORBIDDEN",
            message: "Access to this assistant template is forbidden.",
          },
        });
      }

      // Populate newAssistantData with fields from the template
      // Assuming template fields have the same names as assistant fields
      newAssistantData = {
        name: assistantTemplate.name,
        modes: assistantTemplate.modes,
        firstMessage: assistantTemplate.firstMessage,
        systemPrompt: assistantTemplate.systemPrompt,
        provider: assistantTemplate.provider,
        model: assistantTemplate.model,
        tools: assistantTemplate.tools,
        voice: assistantTemplate.voice,
        // isActive from template can also be considered if it exists on the template model
      };
    }

    // 4. Override with Request Body Data
    if (name !== undefined) newAssistantData.name = name;
    if (modes !== undefined) newAssistantData.modes = modes;
    if (firstMessage !== undefined) newAssistantData.firstMessage = firstMessage;
    if (systemPrompt !== undefined) newAssistantData.systemPrompt = systemPrompt;
    if (provider !== undefined) newAssistantData.provider = provider;
    if (model !== undefined) newAssistantData.model = model;
    if (tools !== undefined) newAssistantData.tools = tools;
    if (voice !== undefined) newAssistantData.voice = voice;
    if (isActive !== undefined) {
      newAssistantData.isActive = isActive;
    } else if (newAssistantData.isActive === undefined) {
      newAssistantData.isActive = true; // Default if not in template and not in body
    }


    // 5. Add Core Fields & Defaults
    newAssistantData.userId = user.id; // user.id is already validated
    if (userPhoneNumberId) {
      const phoneNumberRecord = await prisma.userPhoneNumber.findFirst({
        where: { id: userPhoneNumberId, userId: req.user.id },
      });
      if (!phoneNumberRecord) {
        return res.status(400).json({
          success: false,
          error: {
            code: "INVALID_PHONE_NUMBER_ID",
            message:
              "Invalid or inaccessible userPhoneNumberId provided.",
          },
        });
      }
      newAssistantData.userPhoneNumberId = userPhoneNumberId;
    }

    if (newAssistantData.modes === undefined) newAssistantData.modes = ["web"];
    if (newAssistantData.tools === undefined) newAssistantData.tools = [];
    if (newAssistantData.voice === undefined) newAssistantData.voice = {};


    // 6. Validate Required Fields on newAssistantData
    if (
      !newAssistantData.name ||
      !newAssistantData.firstMessage ||
      !newAssistantData.systemPrompt ||
      !newAssistantData.provider ||
      !newAssistantData.model
    ) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_REQUIRED_FIELDS",
          message:
            "Missing required fields after template and body merge: name, firstMessage, systemPrompt, provider, and model are required.",
        },
      });
    }

    // 7. Create Assistant
    const assistant = await prisma.assistant.create({
      data: newAssistantData,
    });

    console.log("Assistant created successfully with template integration:", assistant);
    res.json({ success: true, data: assistant });
  } catch (error) {
    console.error("Error creating assistant:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to create assistant",
        details: error.message,
      },
    });
  }
};

// Get assistant by ID
export const getSingleAssistant = async (req, res) => {
  try {
    const assistant = await prisma.assistant.findUnique({
      where: { id: req.params.id },
    });

    if (!assistant) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Assistant not found",
        },
      });
    }

    res.json({ success: true, data: assistant });
  } catch (error) {
    console.error("Error fetching assistant:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch assistant",
        details: error.message,
      },
    });
  }
};

// Update assistant
export const updateAssistant = async (req, res) => {
  try {
    console.log("Updating assistant:", req.params.id, req.body);

    const assistant = await prisma.assistant.findUnique({
      where: { id: req.params.id },
    });

    if (!assistant) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Assistant not found",
        },
      });
    }

    // Extract only the fields that are in the Prisma schema
    const {
      userPhoneNumberId, // For phone number assignment
      name,
      modes,
      firstMessage,
      systemPrompt,
      provider,
      model,
      tools,
      voice,
      isActive,
      // Deprecated fields are ignored
      ...rest
    } = req.body;

    let updateData = {
      name,
      modes: modes || undefined, // Keep existing if not provided, or use default if creating
      firstMessage,
      systemPrompt,
      provider,
      model,
      tools: tools || undefined,
      voice: voice || undefined,
      isActive: isActive !== undefined ? isActive : undefined,
    };

    // Remove undefined fields to avoid overwriting existing data with nulls unintentionally
    Object.keys(updateData).forEach(key => updateData[key] === undefined && delete updateData[key]);


    // Validate required fields if they are being set
    // (e.g. if name is in updateData and it's empty, that's an error)
    if (updateData.name === "" || updateData.firstMessage === "" || updateData.systemPrompt === "" || updateData.provider === "" || updateData.model === "") {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_REQUIRED_FIELDS_UPDATE",
          message: "Required fields cannot be empty if provided for update.",
        },
      });
    }

    // Handle userPhoneNumberId separately: can be string ID or null to unassign
    if (userPhoneNumberId !== undefined) { // Check if userPhoneNumberId was part of the request
      if (userPhoneNumberId === null) {
        updateData.userPhoneNumberId = null; // Unassign
      } else {
        const phoneNumberRecord = await prisma.userPhoneNumber.findFirst({
          where: { id: userPhoneNumberId, userId: req.user.id },
        });
        if (!phoneNumberRecord) {
          return res.status(400).json({
            success: false,
            error: {
              code: "INVALID_PHONE_NUMBER_ID",
              message:
                "Invalid or inaccessible userPhoneNumberId provided for update.",
            },
          });
        }
        updateData.userPhoneNumberId = userPhoneNumberId; // Assign new number
      }
    }


    const updatedAssistant = await prisma.assistant.update({
      where: { id: req.params.id },
      data: updateData,
    });

    console.log("Assistant updated successfully:", updatedAssistant);
    res.json({ success: true, data: updatedAssistant });
  } catch (error) {
    console.error("Error updating assistant:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to update assistant",
        details: error.message,
      },
    });
  }
};

// Delete assistant
export const deleteAssistant = async (req, res) => {
  try {
    console.log("Deleting assistant:", req.params.id);
    const assistant = await prisma.assistant.findUnique({
      where: { id: req.params.id },
    });

    if (!assistant) {
      console.log("Assistant not found:", req.params.id);
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Assistant not found",
        },
      });
    }

    await prisma.assistant.delete({
      where: { id: req.params.id },
    });

    console.log("Assistant deleted successfully:", req.params.id);
    res.json({ success: true, data: null });
  } catch (error) {
    console.error("Error deleting assistant:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to delete assistant",
        details: error.message,
      },
    });
  }
};
