import express from "express";

import {
  createAssistant,
  deleteAssistant,
  getAllAssistants,
  getSingleAssistant,
  updateAssistant,
} from "./assistant.controller.js";
import { authenticateJWT } from "../../../middleware/authMiddleware.js";

const router = express.Router();

// Protected routes
router.put("/:id", authenticateJWT, updateAssistant);
router.delete("/:id", authenticateJWT, deleteAssistant);
router.get("/:id", authenticateJWT, getSingleAssistant);
router.post("/", authenticateJWT, createAssistant);
router.get("/", authenticateJWT, getAllAssistants);

export const AssistantRoute = router;
