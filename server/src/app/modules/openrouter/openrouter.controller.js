import config from "../../../config/config.js";

// Check if we have the OpenRouter API key
const openrouterApiKey = config.apis.openRouter.apiKey;
if (!openrouterApiKey) {
  console.warn("OPENROUTER_API_KEY environment variable is not set");
}

export const getOpenRouterModels = async (req, res) => {
  try {
    const response = await fetch("https://openrouter.ai/api/v1/models", {
      headers: {
        Authorization: `Bearer ${openrouterApiKey}`,
        "HTTP-Referer": "http://localhost:3030",
        "X-Title": "TAlkai247",
      },
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API returned ${response.status}`);
    }

    const data = await response.json();
    res.json({ data: data.data });
  } catch (error) {
    console.error("Error fetching models:", error);
    res.status(500).json({
      error: {
        code: "OPENROUTER_API_ERROR",
        message: error.message,
      },
    });
  }
};
export const getOpenRouterAllModels = async (req, res) => {
  try {
    console.log("Fetching OpenRouter models");

    if (!openrouterApiKey) {
      throw new Error("OpenRouter API key is not configured");
    }

    const response = await fetch("https://openrouter.ai/api/v1/models/all", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${openrouterApiKey}`,
        "HTTP-Referer": "https://talkai247.com",
        "X-Title": "Talkai247",
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("OpenRouter API error response:", errorText);
      throw new Error(`OpenRouter API error: ${response.status} ${errorText}`);
    }

    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error("Error fetching OpenRouter models:", error);
    res.status(500).json({ error: error.message });
  }
};

export const createOpenRouterChat = async (req, res) => {
  try {
    console.log("Proxying OpenRouter chat request");

    if (!openrouterApiKey) {
      throw new Error("OpenRouter API key is not configured");
    }

    const response = await fetch(
      "https://openrouter.ai/api/v1/chat/completions",
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${openrouterApiKey}`,
          "Content-Type": "application/json",
          "HTTP-Referer": "https://talkai247.com",
          "X-Title": "Talkai247",
        },
        body: JSON.stringify(req.body),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("OpenRouter API error response:", errorText);
      throw new Error(`OpenRouter API error: ${response.status} ${errorText}`);
    }

    const data = await response.json();
    res.json(data);
  } catch (error) {
    console.error("Error in OpenRouter chat request:", error);
    res.status(500).json({ error: error.message });
  }
};
