import express from "express";

import { authenticateJWT } from "../../../middleware/authMiddleware.js";
import {
  createOpenRouterChat,
  getOpenRouterAllModels,
  getOpenRouterModels,
} from "./openrouter.controller.js";

const router = express.Router();

router.get("/models", authenticateJWT, getOpenRouterModels);
router.post("/chat", authenticateJWT, createOpenRouterChat);
router.get("/all-models", authenticateJWT, getOpenRouterAllModels);
// router.get("/me", authenticateJWT, getCurrentUser);

export const OpenrouterRoute = router;
