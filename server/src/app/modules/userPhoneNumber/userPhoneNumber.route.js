import express from "express";
import { authenticateJWT } from "../../../middleware/authMiddleware.js"; // Assuming you have this
import {
  listUserPhoneNumbers,
  updateUserPhoneNumber,
  deleteUserPhoneNumber,
} from "./userPhoneNumber.controller.js";

const router = express.Router();

// GET /api/user-phone-numbers - List all phone numbers for the authenticated user
router.get("/", authenticateJWT, listUserPhoneNumbers);

// PUT /api/user-phone-numbers/:phoneNumberId - Update a specific phone number
router.put("/:phoneNumberId", authenticateJWT, updateUserPhoneNumber);

// DELETE /api/user-phone-numbers/:phoneNumberId - Delete a specific phone number
router.delete("/:phoneNumberId", authenticateJWT, deleteUserPhoneNumber);

export const UserPhoneNumberRoute = router;
