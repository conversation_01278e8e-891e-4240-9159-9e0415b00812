import { prisma } from "../../../lib/prisma.js";
import twilioService from "../../../services/twilioService.js";

// List User Phone Numbers with basic pagination
export const listUserPhoneNumbers = async (req, res) => {
  const userId = req.user.id;
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const skip = (page - 1) * limit;

  try {
    const [numbers, totalCount] = await prisma.$transaction([
      prisma.userPhoneNumber.findMany({
        where: { userId },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      prisma.userPhoneNumber.count({ where: { userId } }),
    ]);

    res.json({
      success: true,
      data: numbers,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error listing user phone numbers:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to list phone numbers.",
      },
    });
  }
};

// Update User Phone Number (friendlyName, isDefault)
export const updateUserPhoneNumber = async (req, res) => {
  const userId = req.user.id;
  const { phoneNumberId } = req.params;
  const { friendlyName, isDefault } = req.body;

  if (friendlyName === undefined && isDefault === undefined) {
    return res.status(400).json({
      success: false,
      error: {
        code: "BAD_REQUEST",
        message: "No update fields provided (friendlyName or isDefault).",
      },
    });
  }

  try {
    const numberToUpdate = await prisma.userPhoneNumber.findFirst({
      where: { id: phoneNumberId, userId },
    });

    if (!numberToUpdate) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message:
            "Phone number not found or you do not have permission to update it.",
        },
      });
    }

    let updatedNumber;

    if (isDefault === true) {
      updatedNumber = await prisma.$transaction(async (tx) => {
        // Set all other numbers for this user to isDefault: false
        await tx.userPhoneNumber.updateMany({
          where: {
            userId,
            id: { not: phoneNumberId },
          },
          data: { isDefault: false },
        });

        // Set the target number to isDefault: true
        return tx.userPhoneNumber.update({
          where: { id: phoneNumberId },
          data: {
            friendlyName: friendlyName !== undefined ? friendlyName : numberToUpdate.friendlyName,
            isDefault: true,
          },
        });
      });
    } else {
      // If isDefault is explicitly false or not provided, just update normally
      updatedNumber = await prisma.userPhoneNumber.update({
        where: { id: phoneNumberId },
        data: {
          friendlyName: friendlyName !== undefined ? friendlyName : numberToUpdate.friendlyName,
          isDefault: isDefault !== undefined ? isDefault : numberToUpdate.isDefault,
        },
      });
    }

    res.json({ success: true, data: updatedNumber });
  } catch (error) {
    console.error("Error updating user phone number:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update phone number.",
      },
    });
  }
};

// Delete User Phone Number
export const deleteUserPhoneNumber = async (req, res) => {
  const userId = req.user.id;
  const { phoneNumberId } = req.params;

  try {
    const numberToDelete = await prisma.userPhoneNumber.findFirst({
      where: { id: phoneNumberId, userId },
    });

    if (!numberToDelete) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message:
            "Phone number not found or you do not have permission to delete it.",
        },
      });
    }

    if (numberToDelete.providerId) {
      // Assuming providerId stores the Twilio SID for the phone number
      const releaseResult = await twilioService.releasePhoneNumber(
        numberToDelete.providerId
      );
      if (!releaseResult.success) {
        // Log the error but proceed to delete from DB if Twilio says it's already gone or invalid
        console.warn(
          `Twilio release warning for SID ${numberToDelete.providerId}: ${releaseResult.message}. Proceeding with DB deletion.`
        );
      }
    } else {
      console.warn(`No providerId (Twilio SID) found for UserPhoneNumber ${phoneNumberId}, skipping Twilio release.`);
    }

    await prisma.userPhoneNumber.delete({
      where: { id: phoneNumberId },
    });

    res.json({
      success: true,
      message: "Phone number deleted successfully.",
    });
  } catch (error) {
    console.error("Error deleting user phone number:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to delete phone number.",
        details: error.message,
      },
    });
  }
};
