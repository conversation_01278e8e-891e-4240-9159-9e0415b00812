import express from "express";
import {
  initiateOutboundCall,
  generateTwiML,
  handleCallStatus,
  endTwilioCall,
  debugTwilioConfig,
  generateSimpleTwiML,
} from "./twilio.controller.js";
import { authenticateJWT } from "../../../middleware/authMiddleware.js";

const router = express.Router();

// Protected routes
router.post("/call", authenticateJWT, initiateOutboundCall);
router.post("/calls/:callId/end", authenticateJWT, endTwilioCall);

// Public routes for Twilio callbacks
// Support both GET and POST for TwiML endpoints since <PERSON><PERSON><PERSON> can use either
router.get("/twiml/:callId", generateTwiML);
router.post("/twiml/:callId", generateTwiML); // Add POST support
router.post("/twiml/:callId/status", handleCallStatus);

// Simple TwiML endpoint for testing - support both GET and POST
router.get("/simple-twiml", generateSimpleTwiML);
router.post("/simple-twiml", generateSimpleTwiML); // Add POST support

// Debug route - only for development
if (process.env.NODE_ENV !== "production") {
  router.get("/debug", authenticateJWT, debugTwilioConfig);
}

export const TwilioRoute = router;
