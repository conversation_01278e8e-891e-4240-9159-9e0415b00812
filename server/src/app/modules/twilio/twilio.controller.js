import { prisma } from "../../../lib/prisma.js";
import config from "../../../config/config.js";
import twilioService from "../../../services/twilioService.js";
import { AccessToken } from "livekit-server-sdk";

/**
 * Initiate an outbound call using Twilio
 */
export const initiateOutboundCall = async (req, res) => {
  try {
    const userId = req.user.id;
    const { contactId } = req.body;

    if (!contactId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Contact ID is required",
        },
      });
    }

    // Check if contact exists and belongs to user
    const contact = await prisma.contact.findFirst({
      where: {
        id: contactId,
        userId,
      },
    });

    if (!contact) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Contact not found or not owned by user",
        },
      });
    }

    // Find or create a default assistant for whisper calls
    let defaultAssistant = await prisma.assistant.findFirst({
      where: {
        userId,
        name: "Whisper Assistant",
      },
    });

    if (!defaultAssistant) {
      // Create a default assistant for this user
      defaultAssistant = await prisma.assistant.create({
        data: {
          userId,
          name: "Whisper Assistant",
          modes: ["voice"],
          firstMessage: "Hello, I'm your Whisper Assistant.",
          systemPrompt:
            "You are a helpful assistant that provides whisper advice during calls.",
          provider: "openai",
          model: "gpt-4",
          tools: [],
          voice: {
            provider: "elevenlabs",
            voiceId: "default",
            settings: {
              speed: 1.0,
              pitch: 1.0,
              stability: 0.5,
              volume: 1.0,
            },
          },
          isActive: true,
        },
      });
    }

    // Generate a unique room name for this call
    // Include the contact's phone number in the room name for Dial functionality
    // Format the phone number to ensure it's in E.164 format for Twilio
    const formattedPhone = twilioService.formatPhoneNumber(contact.phone);
    const roomName = `whisper_${userId}_${contactId}_${formattedPhone}`;
    const participantIdentity = `user_${userId}_${Date.now()}`;

    // Create LiveKit token for the user
    const API_KEY = config.liveKit.apiKey;
    const API_SECRET = config.liveKit.apiSecret;
    const LIVEKIT_URL = config.liveKit.url;

    const at = new AccessToken(API_KEY, API_SECRET, {
      identity: participantIdentity,
      ttl: "60m", // 1 hour token
      metadata: JSON.stringify({
        userId,
        contactId,
        contactName: contact.name,
        contactType: contact.type,
        timestamp: new Date().toISOString(),
      }),
    });

    // Add permissions to the token
    const grant = {
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canPublishData: true,
      canSubscribe: true,
    };
    at.addGrant(grant);

    // Generate the JWT token
    const participantToken = await at.toJwt();

    // Create a call record in the database
    const call = await prisma.call.create({
      data: {
        userId,
        contactId,
        assistantId: defaultAssistant.id,
        startTime: new Date(),
        status: "IN_PROGRESS",
        transcript: [],
        goals: [],
        metrics: {
          averageSentiment: 0,
          sentimentTimeline: [],
          whisperEffectiveness: 0,
          goalCompletion: 0,
        },
      },
    });

    // Generate the callback URL for Twilio
    // For local development, we need to use a publicly accessible URL
    // In production, we can use the server's URL
    let callbackUrl;

    // Get the server's base URL
    const protocol = req.headers["x-forwarded-proto"] || req.protocol || "http";
    const host =
      req.headers["x-forwarded-host"] || req.get("host") || "localhost:3030";
    const baseUrl = process.env.PUBLIC_URL; //`${protocol}://${host}`;

    // Check if we're in development mode
    if (process.env.NODE_ENV !== "production") {
      // In development, we have two options:

      // Option 1: Use our simple TwiML endpoint (if the server is publicly accessible)
      callbackUrl = `${baseUrl}/api/v1/twilio/simple-twiml`;
      console.log(`Using simple TwiML endpoint: ${callbackUrl}`);
      console.log(`Note: Twilio will make a POST request to this endpoint`);

      // Option 2: Use Twilio's echo service (if our server is not publicly accessible)
      // Uncomment this and comment out Option 1 if your server is not publicly accessible

      // If you're getting errors with Option 1, try this instead:
      if (process.env.USE_TWIMLETS === "true") {
        callbackUrl = `https://twimlets.com/echo?Twiml=${encodeURIComponent(`
          <Response>
            <Say>Welcome to the test call with the AI assistant.</Say>
            <Pause length="2"/>
            <Say>I'm your AI assistant for this call. I can hear both sides of the conversation but only you can hear me.</Say>
            <Pause length="2"/>
            <Say>The call is now active. I'll provide suggestions as needed.</Say>
            <Pause length="5"/>
            <Say>For example, if the other person asks about pricing, I might suggest mentioning our competitive rates.</Say>
            <Pause length="20"/>
            <Gather timeout="10" numDigits="1">
              <Say>Press 1 to hear a sample suggestion, or any other key to continue.</Say>
            </Gather>
            <Say>Here's a suggestion: Remember to highlight our 24/7 customer support as a key benefit.</Say>
            <Pause length="15"/>
            <Say>The call will continue. Press any key at any time to end the call.</Say>
            <Gather numDigits="1" timeout="120"/>
            <Say>Thank you for testing the AI assistant. Goodbye.</Say>
          </Response>
        `)}`;
        console.log(`Using Twimlets echo service: ${callbackUrl}`);
      }
    } else {
      // In production, use the actual call-specific TwiML endpoint
      callbackUrl = `${baseUrl}/api/v1/twilio/twiml/${call.id}`;
      console.log(`Using call-specific TwiML endpoint: ${callbackUrl}`);
    }

    // Initiate the outbound call using Twilio
    const twilioCall = await twilioService.makeOutboundCall(
      contact.phone,
      callbackUrl
    );

    // Update the call record with Twilio SID
    await prisma.call.update({
      where: { id: call.id },
      data: {
        twilioSid: twilioCall.sid,
      },
    });

    res.json({
      success: true,
      data: {
        serverUrl: LIVEKIT_URL,
        roomName,
        participantToken,
        participantName: participantIdentity,
        callId: call.id,
        twilioSid: twilioCall.sid,
        twilioStatus: twilioCall.status,
      },
    });
  } catch (error) {
    console.error("Error initiating outbound call:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to initiate outbound call",
      },
    });
  }
};

/**
 * Generate TwiML for connecting a call to LiveKit
 */
export const generateTwiML = async (req, res) => {
  try {
    console.log(`TwiML request received for call ID: ${req.params.callId}`);
    console.log(`Request query params:`, req.query);
    console.log(`Request body:`, req.body);

    const { callId } = req.params;

    // Get the call details
    const call = await prisma.call.findUnique({
      where: { id: callId },
    });

    if (!call) {
      console.error(`Call not found with ID: ${callId}`);
      // Instead of returning JSON (which Twilio can't process), return a simple TwiML response
      res.set("Content-Type", "text/xml");
      return res.send(`
        <Response>
          <Say>Sorry, there was an error with this call. The call ID was not found.</Say>
        </Response>
      `);
    }

    console.log(`Call found:`, {
      id: call.id,
      userId: call.userId,
      contactId: call.contactId,
      status: call.status,
      startTime: call.startTime,
    });

    // Get the contact information for this call
    const contact = await prisma.contact.findFirst({
      where: {
        id: call.contactId,
        userId: call.userId,
      },
    });

    if (!contact) {
      console.error(`Contact not found for call ID: ${callId}`);
      // Return a simple TwiML response
      res.set("Content-Type", "text/xml");
      return res.send(`
        <Response>
          <Say>Sorry, there was an error with this call. The contact information was not found.</Say>
        </Response>
      `);
    }

    // Extract room name and participant identity
    // Format the phone number to ensure it's in E.164 format for Twilio
    const formattedPhone = twilioService.formatPhoneNumber(contact.phone);
    const roomName = `whisper_${call.userId}_${call.contactId}_${formattedPhone}`;
    const participantIdentity = `user_${call.userId}_${new Date(
      call.startTime
    ).getTime()}`;

    console.log(
      `Generating TwiML with roomName: ${roomName}, participantIdentity: ${participantIdentity}`
    );
    console.log(`Contact phone: ${contact.phone}`);

    // Generate TwiML
    const twiml = twilioService.generateConnectTwiML(
      roomName,
      participantIdentity
    );

    console.log(`Generated TwiML: ${twiml.trim()}`);

    // Set content type to XML
    res.set("Content-Type", "text/xml");
    res.send(twiml);
  } catch (error) {
    console.error("Error generating TwiML:", error);

    // Return a simple TwiML response instead of JSON
    res.set("Content-Type", "text/xml");
    res.send(`
      <Response>
        <Say>Sorry, there was an error processing this call. Please try again later.</Say>
      </Response>
    `);
  }
};

/**
 * Handle Twilio call status callbacks
 */
export const handleCallStatus = async (req, res) => {
  try {
    const { callId } = req.params;

    console.log(`Call status update for call ID: ${callId}`);
    console.log(`Request body:`, req.body);

    const { CallSid, CallStatus, CallDuration } = req.body;

    // Get the call from the database
    const call = await prisma.call.findUnique({
      where: { id: callId },
    });

    if (!call) {
      console.error(`Call not found with ID: ${callId}`);
      return res.status(200).send("OK"); // Still return 200 to Twilio
    }

    console.log(
      `Call ${CallSid} status update: ${CallStatus}, duration: ${
        CallDuration || "unknown"
      }`
    );

    // Update call status in the database
    if (
      CallStatus === "completed" ||
      CallStatus === "failed" ||
      CallStatus === "busy" ||
      CallStatus === "no-answer" ||
      CallStatus === "canceled"
    ) {
      await prisma.call.update({
        where: { id: callId },
        data: {
          status: "COMPLETED",
          endTime: new Date(),
          duration: CallDuration ? parseInt(CallDuration, 10) : 0,
        },
      });

      console.log(`Call ${CallSid} marked as completed in database`);
    } else if (CallStatus === "in-progress") {
      // Update the call status to in progress
      await prisma.call.update({
        where: { id: callId },
        data: {
          status: "IN_PROGRESS",
        },
      });

      console.log(`Call ${CallSid} marked as in-progress in database`);
    }

    // Always return a 200 OK to Twilio
    res.status(200).send("OK");
  } catch (error) {
    console.error("Error handling call status:", error);
    // Still return 200 to Twilio to prevent retries
    res.status(200).send("OK");
  }
};

/**
 * End an active Twilio call
 */
export const endTwilioCall = async (req, res) => {
  try {
    const userId = req.user.id;
    const { callId } = req.params;

    // Check if call exists and belongs to user
    const call = await prisma.call.findFirst({
      where: {
        id: callId,
        userId,
        status: "IN_PROGRESS",
      },
    });

    if (!call) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Active call not found or not owned by user",
        },
      });
    }

    // End the Twilio call
    if (call.twilioSid) {
      await twilioService.endCall(call.twilioSid);
    }

    // Calculate call duration
    const endTime = new Date();
    const duration = Math.floor(
      (endTime.getTime() - call.startTime.getTime()) / 1000
    ); // Duration in seconds

    // Update call record
    const updatedCall = await prisma.call.update({
      where: { id: callId },
      data: {
        endTime,
        duration,
        status: "COMPLETED",
      },
    });

    res.json({
      success: true,
      data: {
        callId: updatedCall.id,
        duration: updatedCall.duration,
        status: updatedCall.status,
      },
    });
  } catch (error) {
    console.error("Error ending Twilio call:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to end call",
      },
    });
  }
};

/**
 * Debug endpoint to check Twilio configuration
 * This is for development only and should be removed in production
 */
export const debugTwilioConfig = async (req, res) => {
  try {
    // Only allow in development mode
    if (process.env.NODE_ENV === "production") {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "Debug endpoints are not available in production",
        },
      });
    }

    // Check if Twilio credentials are configured
    const twilioConfig = {
      accountSid: config.twilio.accountSid
        ? `${config.twilio.accountSid.substring(0, 5)}...`
        : "Not set",
      authToken: config.twilio.authToken ? "Set (hidden)" : "Not set",
      phoneNumber: config.twilio.phoneNumber || "Not set",
      isConfigured: twilioService.isConfigured,
    };

    // Get server information
    const serverInfo = {
      protocol: req.protocol,
      host: req.get("host"),
      originalUrl: req.originalUrl,
      baseUrl: req.baseUrl,
      path: req.path,
      headers: {
        host: req.headers.host,
        "x-forwarded-host": req.headers["x-forwarded-host"],
        "x-forwarded-proto": req.headers["x-forwarded-proto"],
      },
    };

    res.json({
      success: true,
      data: {
        twilioConfig,
        serverInfo,
        nodeEnv: process.env.NODE_ENV,
        requestTime: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("Error in debug endpoint:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get debug information",
      },
    });
  }
};

/**
 * Generate a simple TwiML response for testing
 * This endpoint doesn't require any database access and is useful for testing
 */
export const generateSimpleTwiML = async (req, res) => {
  try {
    console.log("Simple TwiML request received");
    console.log(`Request query params:`, req.query);
    console.log(`Request body:`, req.body);

    // Extract the phone number from the request if available
    let phoneNumber = null;

    // Try to get the phone number from the request
    if (req.body && req.body.To) {
      phoneNumber = req.body.To;
    } else if (req.query && req.query.To) {
      phoneNumber = req.query.To;
    }

    console.log(`Phone number from request: ${phoneNumber || "Not available"}`);

    // Generate a TwiML response that simulates a three-way call with AI assistant
    let twiml;

    if (phoneNumber) {
      // If we have a phone number, create a simulated three-way call
      twiml = `
        <Response>
          <Say>Welcome to the test call with AI assistance.</Say>
          <Pause length="2"/>
          <Say>I'm your AI assistant for this call. I can hear both sides of the conversation but only you can hear me.</Say>
          <Pause length="2"/>
          <Dial callerId="${config.twilio.phoneNumber}">
            <Number>${phoneNumber}</Number>
          </Dial>
          <Say>The call has ended. Thank you for using our service.</Say>
        </Response>
      `;
    } else {
      // If no phone number is available, use a simple TwiML response
      twiml = `
        <Response>
          <Say>Welcome to the test call with the AI assistant.</Say>
          <Pause length="2"/>
          <Say>I'm your AI assistant for this call. I can hear both sides of the conversation but only you can hear me.</Say>
          <Pause length="2"/>
          <Say>The call is now active. I'll provide suggestions as needed.</Say>
          <Pause length="5"/>
          <Say>For example, if the other person asks about pricing, I might suggest mentioning our competitive rates.</Say>
          <Pause length="20"/>
          <Gather action="" method="POST" timeout="10" numDigits="1">
            <Say>Press 1 to hear a sample suggestion, or any other key to continue.</Say>
          </Gather>
          <Say>Here's a suggestion: Remember to highlight our 24/7 customer support as a key benefit.</Say>
          <Pause length="15"/>
          <Say>The call will continue. Press any key at any time to end the call.</Say>
          <Gather numDigits="1" timeout="120"/>
          <Say>Thank you for testing the AI assistant. Goodbye.</Say>
        </Response>
      `;
    }

    console.log(`Generated simple TwiML: ${twiml.trim()}`);

    // Set content type to XML
    res.set("Content-Type", "text/xml");
    res.send(twiml);
  } catch (error) {
    console.error("Error generating simple TwiML:", error);

    // Return a simple TwiML response even in case of error
    res.set("Content-Type", "text/xml");
    res.send(`
      <Response>
        <Say>Sorry, there was an error processing this call. Please try again later.</Say>
      </Response>
    `);
  }
};
