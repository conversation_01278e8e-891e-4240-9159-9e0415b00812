import express from "express";
import { authenticateJWT } from "../../../middleware/authMiddleware.js";
import {
  getAllCampaigns,
  getCampaignById,
  createCampaign,
  updateCampaign,
  deleteCampaign,
  updateCampaignGoals,
  addTeamMember,
  removeTeamMember,
  addComment,
  getCampaignTemplates,
  createCampaignTemplate,
  deleteCampaignTemplate,
  updateChannelSettings,
} from "./campaign.controller.js";

const router = express.Router();

// All routes require authentication
router.use(authenticateJWT);

// Template routes
router.get("/templates", getCampaignTemplates);
router.post("/templates", createCampaignTemplate);
router.delete("/templates/:id", deleteCampaignTemplate);

// Campaign routes
router.get("/", getAllCampaigns);
router.get("/:id", getCampaignById);
router.post("/", createCampaign);
router.put("/:id", updateCampaign);
router.delete("/:id", deleteCampaign);

// Campaign goals routes
router.put("/:id/goals", updateCampaignGoals);

// Team member routes
router.post("/:id/team", addTeamMember);
router.delete("/:id/team/:memberId", removeTeamMember);

// Comment routes
router.post("/:id/comments", addComment);

// Channel settings routes
router.put("/:id/channels", updateChannelSettings);

export const CampaignRoute = router;
