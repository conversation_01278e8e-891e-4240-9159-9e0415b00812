import { prisma } from "../../../lib/prisma.js";

/**
 * Get all campaigns for a user
 * @param {string} userId - The user ID
 * @returns {Promise<Array>} - List of campaigns
 */
export const getAllCampaignsForUser = async (userId) => {
  return await prisma.campaign.findMany({
    where: {
      OR: [
        { userId },
        {
          teamMembers: {
            some: {
              userId,
              status: "ACCEPTED",
            },
          },
        },
      ],
    },
    orderBy: { updatedAt: "desc" },
    include: {
      contacts: {
        select: {
          id: true,
          name: true,
          phone: true,
          email: true,
        },
      },
      goals: true,
      channelSettings: true,
      teamMembers: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
      comments: {
        orderBy: {
          createdAt: "desc",
        },
        take: 5,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
    },
  });
};

/**
 * Get a campaign by ID
 * @param {string} campaignId - The campaign ID
 * @param {string} userId - The user ID
 * @returns {Promise<Object>} - Campaign data
 */
export const getCampaignById = async (campaignId, userId) => {
  return await prisma.campaign.findFirst({
    where: {
      id: campaignId,
      OR: [
        { userId },
        {
          teamMembers: {
            some: {
              userId,
              status: "ACCEPTED",
            },
          },
        },
      ],
    },
    include: {
      contacts: {
        select: {
          id: true,
          name: true,
          phone: true,
          email: true,
        },
      },
      goals: true,
      channelSettings: true,
      teamMembers: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
      comments: {
        orderBy: {
          createdAt: "desc",
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
    },
  });
};

/**
 * Create a new campaign
 * @param {Object} campaignData - The campaign data
 * @returns {Promise<Object>} - Created campaign
 */
export const createCampaign = async (campaignData) => {
  const {
    userId,
    name,
    description,
    startDate,
    endDate,
    status,
    contacts = [],
    goals = [],
    metrics,
    automationSettings,
    callScript,
    assistantId,
    phoneNumberId,
    channelSettings,
    templateId,
  } = campaignData;

  // TODO: When initiating calls for this campaign, if `assistantId` is set,
  // fetch the assistant. If the assistant has a `userPhoneNumberId`,
  // fetch that UserPhoneNumber's `phoneNumber` and pass it as `fromPhoneNumber`
  // to `twilioService.makeOutboundCall`.

  // Create the campaign
  const newCampaign = await prisma.campaign.create({
    data: {
      userId,
      name,
      description,
      startDate: new Date(startDate),
      endDate: endDate ? new Date(endDate) : null,
      status,
      metrics,
      automationSettings,
      callScript,
      assistantId,
      phoneNumberId,
      templateId,
      contacts: {
        connect: contacts.map((contactId) => ({ id: contactId })),
      },
      // Add the current user as the owner
      teamMembers: {
        create: {
          userId,
          role: "OWNER",
          status: "ACCEPTED",
        },
      },
    },
  });

  // Create goals if provided
  if (goals && goals.length > 0) {
    await prisma.campaignGoal.createMany({
      data: goals.map((goal) => ({
        campaignId: newCampaign.id,
        title: goal.title,
        description: goal.description || null,
        target: goal.target,
        progress: goal.progress || 0,
        completed: goal.completed || false,
      })),
    });
  }

  // Create channel settings if provided
  if (channelSettings) {
    await prisma.channelSettings.create({
      data: {
        campaignId: newCampaign.id,
        voice: channelSettings.voice || null,
        sms: channelSettings.sms || null,
        email: channelSettings.email || null,
      },
    });
  }

  // Get the created campaign with all related data
  return await prisma.campaign.findUnique({
    where: { id: newCampaign.id },
    include: {
      contacts: {
        select: {
          id: true,
          name: true,
          phone: true,
          email: true,
        },
      },
      goals: true,
      channelSettings: true,
      teamMembers: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
    },
  });
};

/**
 * Update a campaign
 * @param {string} campaignId - The campaign ID
 * @param {Object} campaignData - The campaign data to update
 * @returns {Promise<Object>} - Updated campaign
 */
export const updateCampaign = async (campaignId, campaignData) => {
  const {
    name,
    description,
    startDate,
    endDate,
    status,
    contacts,
    goals,
    metrics,
    automationSettings,
    callScript,
    assistantId,
    phoneNumberId,
    channelSettings,
  } = campaignData;

  // Prepare update data
  const updateData = {};

  if (name !== undefined) updateData.name = name;
  if (description !== undefined) updateData.description = description;
  if (startDate !== undefined) updateData.startDate = new Date(startDate);
  if (endDate !== undefined)
    updateData.endDate = endDate ? new Date(endDate) : null;
  if (status !== undefined) updateData.status = status;
  if (metrics !== undefined) updateData.metrics = metrics;
  if (automationSettings !== undefined)
    updateData.automationSettings = automationSettings;
  if (callScript !== undefined) updateData.callScript = callScript;
  if (assistantId !== undefined) updateData.assistantId = assistantId;
  if (phoneNumberId !== undefined) updateData.phoneNumberId = phoneNumberId;

  // Update the campaign
  await prisma.campaign.update({
    where: { id: campaignId },
    data: updateData,
  });

  // Update contacts if provided
  if (contacts !== undefined) {
    // First disconnect all existing contacts
    await prisma.campaign.update({
      where: { id: campaignId },
      data: {
        contacts: {
          set: [], // Disconnect all contacts
        },
      },
    });

    // Then connect the new contacts
    if (contacts.length > 0) {
      await prisma.campaign.update({
        where: { id: campaignId },
        data: {
          contacts: {
            connect: contacts.map((contactId) => ({ id: contactId })),
          },
        },
      });
    }
  }

  // Update goals if provided
  if (goals !== undefined) {
    // Delete existing goals
    await prisma.campaignGoal.deleteMany({
      where: { campaignId },
    });

    // Create new goals
    if (goals.length > 0) {
      await prisma.campaignGoal.createMany({
        data: goals.map((goal) => ({
          campaignId,
          title: goal.title,
          description: goal.description || null,
          target: goal.target,
          progress: goal.progress || 0,
          completed: goal.completed || false,
        })),
      });
    }
  }

  // Update channel settings if provided
  if (channelSettings !== undefined) {
    // Check if channel settings exist
    const existingSettings = await prisma.channelSettings.findUnique({
      where: { campaignId },
    });

    if (existingSettings) {
      // Update existing settings
      await prisma.channelSettings.update({
        where: { campaignId },
        data: {
          voice: channelSettings.voice || null,
          sms: channelSettings.sms || null,
          email: channelSettings.email || null,
        },
      });
    } else {
      // Create new settings
      await prisma.channelSettings.create({
        data: {
          campaignId,
          voice: channelSettings.voice || null,
          sms: channelSettings.sms || null,
          email: channelSettings.email || null,
        },
      });
    }
  }

  // Get the updated campaign with all related data
  return await prisma.campaign.findUnique({
    where: { id: campaignId },
    include: {
      contacts: {
        select: {
          id: true,
          name: true,
          phone: true,
          email: true,
        },
      },
      goals: true,
      channelSettings: true,
      teamMembers: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
    },
  });
};

/**
 * Delete a campaign
 * @param {string} campaignId - The campaign ID
 * @returns {Promise<void>}
 */
export const deleteCampaign = async (campaignId) => {
  // First disconnect all contacts from the campaign
  await prisma.campaign.update({
    where: { id: campaignId },
    data: {
      contacts: {
        set: [], // Disconnect all contacts
      },
    },
  });

  // Then delete the campaign
  await prisma.campaign.delete({
    where: { id: campaignId },
  });
};

/**
 * Update campaign goals
 * @param {string} campaignId - The campaign ID
 * @param {Array} goals - The goals data
 * @returns {Promise<Array>} - Updated goals
 */
export const updateCampaignGoals = async (campaignId, goals) => {
  // Delete existing goals
  await prisma.campaignGoal.deleteMany({
    where: { campaignId },
  });

  // Create new goals
  if (goals && goals.length > 0) {
    await prisma.campaignGoal.createMany({
      data: goals.map((goal) => ({
        campaignId,
        title: goal.title,
        description: goal.description || null,
        target: goal.target,
        progress: goal.progress || 0,
        completed: goal.completed || false,
      })),
    });
  }

  // Get the updated goals
  return await prisma.campaignGoal.findMany({
    where: { campaignId },
  });
};

/**
 * Add team member to campaign
 * @param {string} campaignId - The campaign ID
 * @param {string} email - The email of the user to add
 * @param {string} role - The role to assign
 * @param {string} inviterId - The ID of the user sending the invitation
 * @returns {Promise<Object>} - Created team member
 */
export const addTeamMember = async (campaignId, email, role, inviterId) => {
  // Check if the user exists
  const user = await prisma.user.findUnique({
    where: { email },
  });

  if (user) {
    // Check if the user is already a team member
    const existingMember = await prisma.campaignTeamMember.findFirst({
      where: {
        campaignId,
        userId: user.id,
      },
    });

    if (existingMember) {
      throw new Error("User is already a team member");
    }

    // Add the user as a team member
    return await prisma.campaignTeamMember.create({
      data: {
        campaignId,
        userId: user.id,
        role,
        status: "ACCEPTED", // Auto-accept for now, in a real app this would be PENDING
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });
  } else {
    // Create an invitation for a non-existing user
    const teamMember = await prisma.campaignTeamMember.create({
      data: {
        campaignId,
        userId: inviterId, // Temporarily assign to the inviter
        role,
        invitedEmail: email,
        status: "PENDING",
      },
    });

    return {
      ...teamMember,
      user: {
        email,
        name: email.split("@")[0], // Use part of email as name
      },
    };
  }
};

/**
 * Remove team member from campaign
 * @param {string} memberId - The team member ID
 * @returns {Promise<void>}
 */
export const removeTeamMember = async (memberId) => {
  // Get the team member
  const teamMember = await prisma.campaignTeamMember.findUnique({
    where: { id: memberId },
  });

  if (!teamMember) {
    throw new Error("Team member not found");
  }

  // Prevent removing the owner
  if (teamMember.role === "OWNER") {
    throw new Error("Cannot remove the campaign owner");
  }

  // Remove the team member
  await prisma.campaignTeamMember.delete({
    where: { id: memberId },
  });
};

/**
 * Add comment to campaign
 * @param {string} campaignId - The campaign ID
 * @param {string} userId - The user ID
 * @param {string} content - The comment content
 * @param {Array} attachments - Optional attachments
 * @returns {Promise<Object>} - Created comment
 */
export const addComment = async (campaignId, userId, content, attachments) => {
  return await prisma.campaignComment.create({
    data: {
      campaignId,
      userId,
      content,
      attachments: attachments || [],
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });
};

/**
 * Get campaign templates
 * @param {string} userId - The user ID
 * @returns {Promise<Array>} - List of templates
 */
export const getCampaignTemplates = async (userId) => {
  return await prisma.campaignTemplate.findMany({
    where: {
      OR: [{ userId }, { isSystem: true }],
    },
    orderBy: {
      createdAt: "desc",
    },
  });
};

/**
 * Create campaign template
 * @param {string} userId - The user ID
 * @param {string} name - The template name
 * @param {string} description - The template description
 * @param {Object} campaignData - The campaign data
 * @returns {Promise<Object>} - Created template
 */
export const createCampaignTemplate = async (
  userId,
  name,
  description,
  campaignData
) => {
  return await prisma.campaignTemplate.create({
    data: {
      userId,
      name,
      description,
      campaignData,
      isSystem: false,
    },
  });
};

/**
 * Delete campaign template
 * @param {string} templateId - The template ID
 * @param {string} userId - The user ID
 * @returns {Promise<void>}
 */
export const deleteCampaignTemplate = async (templateId, userId) => {
  // Check if the template exists and belongs to the user
  const template = await prisma.campaignTemplate.findFirst({
    where: {
      id: templateId,
      userId,
      isSystem: false,
    },
  });

  if (!template) {
    throw new Error(
      "Template not found or you do not have permission to delete it"
    );
  }

  // Delete the template
  await prisma.campaignTemplate.delete({
    where: { id: templateId },
  });
};

/**
 * Update channel settings
 * @param {string} campaignId - The campaign ID
 * @param {Object} settings - The channel settings
 * @returns {Promise<Object>} - Updated channel settings
 */
export const updateChannelSettings = async (campaignId, settings) => {
  const { voice, sms, email } = settings;

  // Check if channel settings exist
  const existingSettings = await prisma.channelSettings.findUnique({
    where: { campaignId },
  });

  if (existingSettings) {
    // Update existing settings
    return await prisma.channelSettings.update({
      where: { campaignId },
      data: {
        voice: voice || null,
        sms: sms || null,
        email: email || null,
      },
    });
  } else {
    // Create new settings
    return await prisma.channelSettings.create({
      data: {
        campaignId,
        voice: voice || null,
        sms: sms || null,
        email: email || null,
      },
    });
  }
};
