import { prisma } from "../../../lib/prisma.js";
import * as campaignService from "./campaign.service.js";

// Get all campaigns for a user
export const getAllCampaigns = async (req, res) => {
  try {
    const userId = req.user.id;

    const campaigns = await campaignService.getAllCampaignsForUser(userId);

    res.status(200).json({
      success: true,
      data: campaigns,
    });
  } catch (error) {
    console.error("Error fetching campaigns:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch campaigns",
        details: error.message,
      },
    });
  }
};

// Get a single campaign by ID
export const getCampaignById = async (req, res) => {
  try {
    const userId = req.user.id;
    const campaignId = req.params.id;

    const campaign = await campaignService.getCampaignById(campaignId, userId);

    if (!campaign) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Campaign not found",
        },
      });
    }

    res.status(200).json({
      success: true,
      data: campaign,
    });
  } catch (error) {
    console.error("Error fetching campaign:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch campaign",
        details: error.message,
      },
    });
  }
};

// Create a new campaign
export const createCampaign = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      name,
      description,
      startDate,
      endDate,
      status,
      contacts = [],
      goals = [],
      metrics = {
        totalCalls: 0,
        successfulCalls: 0,
        failedCalls: 0,
        averageDuration: 0,
        averageSentiment: 0,
      },
      automationSettings,
      callScript,
      assistantId,
      phoneNumberId,
      channelSettings,
      templateId,
    } = req.body;

    // Validate required fields
    if (!name || !startDate || !status) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Name, start date, and status are required",
        },
      });
    }

    // Map the status to match the enum values in the database
    const campaignStatus = status.toUpperCase();
    if (
      ![
        "DRAFT",
        "SCHEDULED",
        "ACTIVE",
        "COMPLETED",
        "CANCELLED",
        "PAUSED",
      ].includes(campaignStatus)
    ) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Invalid status value",
        },
      });
    }

    // Create the campaign using the service
    const campaignData = {
      userId,
      name,
      description,
      startDate,
      endDate,
      status: campaignStatus,
      contacts,
      goals,
      metrics,
      automationSettings,
      callScript,
      assistantId,
      phoneNumberId,
      channelSettings,
      templateId,
    };

    const createdCampaign = await campaignService.createCampaign(campaignData);

    res.status(201).json({
      success: true,
      data: createdCampaign,
    });
  } catch (error) {
    console.error("Error creating campaign:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to create campaign",
        details: error.message,
      },
    });
  }
};

// Update an existing campaign
export const updateCampaign = async (req, res) => {
  try {
    const userId = req.user.id;
    const campaignId = req.params.id;
    const {
      name,
      description,
      startDate,
      endDate,
      status,
      contacts,
      goals,
      metrics,
      automationSettings,
      callScript,
      assistantId,
      phoneNumberId,
      channelSettings,
    } = req.body;

    // Check if campaign exists and belongs to user or user is a team member with edit rights
    const existingCampaign = await prisma.campaign.findFirst({
      where: {
        id: campaignId,
        OR: [
          { userId },
          {
            teamMembers: {
              some: {
                userId,
                role: { in: ["OWNER", "ADMIN", "EDITOR"] },
                status: "ACCEPTED",
              },
            },
          },
        ],
      },
    });

    if (!existingCampaign) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message:
            "Campaign not found or you don't have permission to update it",
        },
      });
    }

    // Validate status if provided
    if (status !== undefined) {
      const campaignStatus = status.toUpperCase();
      if (
        ![
          "DRAFT",
          "SCHEDULED",
          "ACTIVE",
          "COMPLETED",
          "CANCELLED",
          "PAUSED",
        ].includes(campaignStatus)
      ) {
        return res.status(400).json({
          success: false,
          error: {
            code: "INVALID_INPUT",
            message: "Invalid status value",
          },
        });
      }
    }

    // Update the campaign using the service
    const campaignData = {
      name,
      description,
      startDate,
      endDate,
      status: status ? status.toUpperCase() : undefined,
      contacts,
      goals,
      metrics,
      automationSettings,
      callScript,
      assistantId,
      phoneNumberId,
      channelSettings,
    };

    const updatedCampaign = await campaignService.updateCampaign(
      campaignId,
      campaignData
    );

    res.status(200).json({
      success: true,
      data: updatedCampaign,
    });
  } catch (error) {
    console.error("Error updating campaign:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to update campaign",
        details: error.message,
      },
    });
  }
};

// Delete a campaign
export const deleteCampaign = async (req, res) => {
  try {
    const userId = req.user.id;
    const campaignId = req.params.id;

    // Check if campaign exists and belongs to user or user is a team member with delete rights
    const existingCampaign = await prisma.campaign.findFirst({
      where: {
        id: campaignId,
        OR: [
          { userId },
          {
            teamMembers: {
              some: {
                userId,
                role: { in: ["OWNER", "ADMIN"] },
                status: "ACCEPTED",
              },
            },
          },
        ],
      },
    });

    if (!existingCampaign) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message:
            "Campaign not found or you don't have permission to delete it",
        },
      });
    }

    // Delete the campaign using the service
    await campaignService.deleteCampaign(campaignId);

    res.status(200).json({
      success: true,
      message: "Campaign deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting campaign:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to delete campaign",
        details: error.message,
      },
    });
  }
};

// Update campaign goals
export const updateCampaignGoals = async (req, res) => {
  try {
    const userId = req.user.id;
    const campaignId = req.params.id;
    const { goals } = req.body;

    // Check if the user has permission to update the campaign
    const campaign = await prisma.campaign.findFirst({
      where: {
        id: campaignId,
        OR: [
          { userId },
          {
            teamMembers: {
              some: {
                userId,
                role: { in: ["OWNER", "ADMIN", "EDITOR"] },
                status: "ACCEPTED",
              },
            },
          },
        ],
      },
    });

    if (!campaign) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message:
            "Campaign not found or you don't have permission to update it",
        },
      });
    }

    // Update goals using the service
    const updatedGoals = await campaignService.updateCampaignGoals(
      campaignId,
      goals
    );

    res.status(200).json({
      success: true,
      data: updatedGoals,
    });
  } catch (error) {
    console.error("Error updating campaign goals:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to update campaign goals",
        details: error.message,
      },
    });
  }
};

// Add team member to campaign
export const addTeamMember = async (req, res) => {
  try {
    const userId = req.user.id;
    const campaignId = req.params.id;
    const { email, role } = req.body;

    // Check if the user has permission to add team members
    const campaign = await prisma.campaign.findFirst({
      where: {
        id: campaignId,
        OR: [
          { userId },
          {
            teamMembers: {
              some: {
                userId,
                role: { in: ["OWNER", "ADMIN"] },
                status: "ACCEPTED",
              },
            },
          },
        ],
      },
    });

    if (!campaign) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message:
            "Campaign not found or you don't have permission to add team members",
        },
      });
    }

    try {
      // Add team member using the service
      const teamMember = await campaignService.addTeamMember(
        campaignId,
        email,
        role,
        userId
      );

      res.status(201).json({
        success: true,
        data: teamMember,
      });
    } catch (err) {
      if (err.message === "User is already a team member") {
        return res.status(400).json({
          success: false,
          error: {
            code: "INVALID_INPUT",
            message: err.message,
          },
        });
      }
      throw err;
    }
  } catch (error) {
    console.error("Error adding team member:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to add team member",
        details: error.message,
      },
    });
  }
};

// Remove team member from campaign
export const removeTeamMember = async (req, res) => {
  try {
    const userId = req.user.id;
    const campaignId = req.params.id;
    const memberId = req.params.memberId;

    // Check if the user has permission to remove team members
    const campaign = await prisma.campaign.findFirst({
      where: {
        id: campaignId,
        OR: [
          { userId },
          {
            teamMembers: {
              some: {
                userId,
                role: { in: ["OWNER", "ADMIN"] },
                status: "ACCEPTED",
              },
            },
          },
        ],
      },
    });

    if (!campaign) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message:
            "Campaign not found or you don't have permission to remove team members",
        },
      });
    }

    try {
      // Remove team member using the service
      await campaignService.removeTeamMember(memberId);

      res.status(200).json({
        success: true,
        message: "Team member removed successfully",
      });
    } catch (err) {
      if (err.message === "Team member not found") {
        return res.status(404).json({
          success: false,
          error: {
            code: "NOT_FOUND",
            message: err.message,
          },
        });
      } else if (err.message === "Cannot remove the campaign owner") {
        return res.status(400).json({
          success: false,
          error: {
            code: "INVALID_INPUT",
            message: err.message,
          },
        });
      }
      throw err;
    }
  } catch (error) {
    console.error("Error removing team member:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to remove team member",
        details: error.message,
      },
    });
  }
};

// Add comment to campaign
export const addComment = async (req, res) => {
  try {
    const userId = req.user.id;
    const campaignId = req.params.id;
    const { content, attachments } = req.body;

    // Check if the user has permission to comment on the campaign
    const campaign = await prisma.campaign.findFirst({
      where: {
        id: campaignId,
        OR: [
          { userId },
          {
            teamMembers: {
              some: {
                userId,
                status: "ACCEPTED",
              },
            },
          },
        ],
      },
    });

    if (!campaign) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Campaign not found or you don't have permission to comment",
        },
      });
    }

    // Add the comment using the service
    const comment = await campaignService.addComment(
      campaignId,
      userId,
      content,
      attachments
    );

    res.status(201).json({
      success: true,
      data: comment,
    });
  } catch (error) {
    console.error("Error adding comment:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to add comment",
        details: error.message,
      },
    });
  }
};

// Get campaign templates
export const getCampaignTemplates = async (req, res) => {
  try {
    const userId = req.user.id;
    console.log("templates", userId);

    // Get templates using the service
    const templates = await campaignService.getCampaignTemplates(userId);

    res.status(200).json({
      success: true,
      data: templates,
    });
  } catch (error) {
    console.error("Error getting campaign templates:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to get campaign templates",
        details: error.message,
      },
    });
  }
};

// Create campaign template
export const createCampaignTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const { name, description, campaignData } = req.body;

    // Create template using the service
    const template = await campaignService.createCampaignTemplate(
      userId,
      name,
      description,
      campaignData
    );

    res.status(201).json({
      success: true,
      data: template,
    });
  } catch (error) {
    console.error("Error creating campaign template:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to create campaign template",
        details: error.message,
      },
    });
  }
};

// Delete campaign template
export const deleteCampaignTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const templateId = req.params.id;

    try {
      // Delete template using the service
      await campaignService.deleteCampaignTemplate(templateId, userId);

      res.status(200).json({
        success: true,
        message: "Template deleted successfully",
      });
    } catch (err) {
      if (err.message.includes("Template not found")) {
        return res.status(404).json({
          success: false,
          error: {
            code: "NOT_FOUND",
            message: err.message,
          },
        });
      }
      throw err;
    }
  } catch (error) {
    console.error("Error deleting campaign template:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to delete campaign template",
        details: error.message,
      },
    });
  }
};

// Update channel settings
export const updateChannelSettings = async (req, res) => {
  try {
    const userId = req.user.id;
    const campaignId = req.params.id;
    const { voice, sms, email } = req.body;

    // Check if the user has permission to update the campaign
    const campaign = await prisma.campaign.findFirst({
      where: {
        id: campaignId,
        OR: [
          { userId },
          {
            teamMembers: {
              some: {
                userId,
                role: { in: ["OWNER", "ADMIN", "EDITOR"] },
                status: "ACCEPTED",
              },
            },
          },
        ],
      },
    });

    if (!campaign) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message:
            "Campaign not found or you don't have permission to update it",
        },
      });
    }

    // Update channel settings using the service
    const settings = { voice, sms, email };
    const channelSettings = await campaignService.updateChannelSettings(
      campaignId,
      settings
    );

    res.status(200).json({
      success: true,
      data: channelSettings,
    });
  } catch (error) {
    console.error("Error updating channel settings:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to update channel settings",
        details: error.message,
      },
    });
  }
};
