import express from "express";

import {
  createTemplate,
  deleteTemplate,
  getAllTemplates,
  getSingleTemplate,
  updateTemplate,
} from "./template.controller.js";
import { authenticateJWT } from "../../../middleware/authMiddleware.js";

const router = express.Router();

// Protected routes
router.put("/:id", authenticateJWT, updateTemplate);
router.delete("/:id", authenticateJWT, deleteTemplate);
router.get("/:id", authenticateJWT, getSingleTemplate);
router.post("/", authenticateJWT, createTemplate);
router.get("/", authenticateJWT, getAllTemplates);

export const TemplateRoute = router;
