import { prisma } from "../../../lib/prisma.js";
import config from "../../../config/config.js";
import { AccessToken } from "livekit-server-sdk";

// Get assistant by ID (only basic info for public use)
export const getAssistantById = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Find the assistant and verify it belongs to the user
    const assistant = await prisma.assistant.findFirst({
      where: {
        id: id,
        userId: userId,
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        firstMessage: true,
        modes: true,
        provider: true,
        model: true,
      },
    });

    if (!assistant) {
      return res.status(404).json({
        success: false,
        error: {
          code: "ASSISTANT_NOT_FOUND",
          message: "Assistant not found or not accessible",
        },
      });
    }

    res.status(200).json({
      success: true,
      data: assistant,
    });
  } catch (error) {
    console.error("Error fetching assistant:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Internal server error",
      },
    });
  }
};

// Get LiveKit connection details for public access
export const getLivekitConnectionDetails = async (req, res) => {
  try {
    const { assistantId } = req.params;
    const userId = req.user.id;

    // Verify the assistant exists and belongs to the user
    const assistant = await prisma.assistant.findFirst({
      where: {
        id: assistantId,
        userId: userId,
        isActive: true,
      },
      include: {
        user: true,
      },
    });

    if (!assistant) {
      return res.status(404).json({
        success: false,
        error: {
          code: "ASSISTANT_NOT_FOUND",
          message: "Assistant not found or not accessible",
        },
      });
    }

    // Get LiveKit configuration
    const API_KEY = config.liveKit.apiKey;
    const API_SECRET = config.liveKit.apiSecret;
    const LIVEKIT_URL = config.liveKit.url;

    // Generate a unique participant identity
    const participantIdentity = `voice_assistant_user_${Math.floor(
      Math.random() * 10_000
    )}`;
    const roomName = assistant.id;

    // Create an access token with the assistant's attributes
    const at = new AccessToken(API_KEY, API_SECRET, {
      identity: participantIdentity,
      ttl: "15m",
      attributes: {
        id: assistant.id,
        name: assistant.name,
        firstMessage: assistant.firstMessage,
        systemPrompt: assistant.systemPrompt,
        provider: assistant.provider,
        model: assistant.model,
        modes: assistant.modes[1],
        userCalId: assistant.user.calApiKey,
        userId: assistant.userId,
        calEventTypeId: assistant?.user?.calEventTypeId?.toString(),
        isPremium: assistant?.user?.isPremium?.toString(),
      },
    });

    // Add room grant
    const grant = {
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canPublishData: true,
      canSubscribe: true,
    };
    at.addGrant(grant);

    // Generate the token
    const participantToken = await at.toJwt();

    // Return the connection details
    res.status(200).json({
      success: true,
      data: {
        serverUrl: LIVEKIT_URL,
        roomName,
        participantToken,
        participantName: participantIdentity,
      },
    });
  } catch (error) {
    console.error("Error generating LiveKit connection details:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Internal server error",
      },
    });
  }
};

// Send a chat message to an assistant
export const sendChatMessage = async (req, res) => {
  try {
    const { assistantId } = req.params;
    const { message, conversationId } = req.body;
    const userId = req.user.id;

    if (!message) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_MESSAGE",
          message: "Message is required",
        },
      });
    }

    // Find the assistant and verify it belongs to the user
    const assistant = await prisma.assistant.findFirst({
      where: {
        id: assistantId,
        userId: userId,
        isActive: true,
      },
    });

    if (!assistant) {
      return res.status(404).json({
        success: false,
        error: {
          code: "ASSISTANT_NOT_FOUND",
          message: "Assistant not found or not accessible",
        },
      });
    }

    // Prepare the messages for the AI model
    const systemPrompt = assistant.systemPrompt;

    // Create a conversation history or use existing one
    let conversation;
    if (conversationId) {
      conversation = await prisma.conversation.findFirst({
        where: {
          id: conversationId,
          assistantId: assistantId,
        },
        include: {
          messages: {
            orderBy: {
              createdAt: "asc",
            },
          },
        },
      });

      if (!conversation) {
        return res.status(404).json({
          success: false,
          error: {
            code: "CONVERSATION_NOT_FOUND",
            message: "Conversation not found",
          },
        });
      }
    } else {
      // Create a new conversation
      conversation = await prisma.conversation.create({
        data: {
          assistantId: assistantId,
          userId: userId,
        },
        include: {
          messages: false,
        },
      });
    }

    // Save the user message
    await prisma.message.create({
      data: {
        conversationId: conversation.id,
        content: message,
        role: "user",
      },
    });

    // Prepare messages for the AI model
    const messages = [{ role: "system", content: systemPrompt }];

    // Add conversation history if it exists
    if (conversation.messages && conversation.messages.length > 0) {
      // Add up to the last 10 messages to avoid token limits
      const recentMessages = conversation.messages.slice(-10);
      recentMessages.forEach((msg) => {
        messages.push({ role: msg.role, content: msg.content });
      });
    }

    // Add the current user message
    messages.push({ role: "user", content: message });

    // Call the AI model based on the assistant's provider
    let aiResponse;

    if (assistant.provider === "OpenRouter") {
      // Use OpenRouter API
      const openrouterApiKey = config.apis.openRouter.apiKey;

      const response = await fetch(
        "https://openrouter.ai/api/v1/chat/completions",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${openrouterApiKey}`,
            "HTTP-Referer": "https://talkai247.com",
            "X-Title": "Talkai247",
          },
          body: JSON.stringify({
            model: assistant.model,
            messages: messages,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.status}`);
      }

      const data = await response.json();
      aiResponse = data.choices[0].message.content;
    } else {
      // Default fallback response if provider not supported
      aiResponse = "I'm sorry, I couldn't process your request at this time.";
    }

    // Save the AI response
    await prisma.message.create({
      data: {
        conversationId: conversation.id,
        content: aiResponse,
        role: "assistant",
      },
    });

    // Return the response
    res.status(200).json({
      success: true,
      data: {
        conversationId: conversation.id,
        message: aiResponse,
      },
    });
  } catch (error) {
    console.error("Error processing chat message:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Internal server error",
      },
    });
  }
};
