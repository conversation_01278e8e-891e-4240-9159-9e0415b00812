import { prisma } from "../../../lib/prisma.js";
import config from "../../../config/config.js";
import { AccessToken } from "livekit-server-sdk";

// Get assistant by ID (only basic info for public use)
export const getAssistantById = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Find the assistant and verify it belongs to the user
    const assistant = await prisma.assistant.findFirst({
      where: {
        id: id,
        userId: userId,
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        firstMessage: true,
        modes: true,
        provider: true,
        model: true,
      },
    });

    if (!assistant) {
      return res.status(404).json({
        success: false,
        error: {
          code: "ASSISTANT_NOT_FOUND",
          message: "Assistant not found or not accessible",
        },
      });
    }

    res.status(200).json({
      success: true,
      data: assistant,
    });
  } catch (error) {
    console.error("Error fetching assistant:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Internal server error",
      },
    });
  }
};

// Get Page Content by Slug
// TODO: Fetch this dynamic page content from a database (e.g., a SitePage table) in a future iteration.
export const getPageContentBySlug = async (req, res) => {
  try {
    const { slug } = req.params;

    // Hardcoded page content store
    const pages = {
      "terms-of-service": {
        title: "Terms of Service",
        content:
          "<h1>Terms of Service</h1><p>Last Updated: [Date]</p><p>Please read these Terms of Service ('Terms', 'Terms of Service') carefully before using the [Your Website URL] website (the 'Service') operated by [Your Company Name] ('us', 'we', or 'our').</p><h2>1. Acceptance of Terms</h2><p>By accessing or using the Service, you agree to be bound by these Terms. If you disagree with any part of the terms, then you may not access the Service.</p><h2>2. Changes to Terms</h2><p>We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will try to provide at least 30 days' notice prior to any new terms taking effect. What constitutes a material change will be determined at our sole discretion.</p><h2>3. Use of Service</h2><p>You agree not to use the Service for any unlawful purpose or in any way that interrupts, damages, or impairs the service...</p><h2>4. Intellectual Property</h2><p>The Service and its original content, features, and functionality are and will remain the exclusive property of [Your Company Name] and its licensors.</p><h2>5. Termination</h2><p>We may terminate or suspend access to our Service immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.</p><h2>6. Governing Law</h2><p>These Terms shall be governed and construed in accordance with the laws of [Your Jurisdiction], without regard to its conflict of law provisions.</p><h2>7. Contact Us</h2><p>If you have any questions about these Terms, please contact us at [Your Contact Email/Link].</p>",
      },
      "privacy-policy": {
        title: "Privacy Policy",
        content:
          "<h1>Privacy Policy</h1><p>Last Updated: [Date]</p><p>[Your Company Name] ('us', 'we', or 'our') operates the [Your Website URL] website (the 'Service').</p><p>This page informs you of our policies regarding the collection, use, and disclosure of personal data when you use our Service and the choices you have associated with that data.</p><h2>1. Information Collection and Use</h2><p>We collect several different types of information for various purposes to provide and improve our Service to you.</p><h3>Types of Data Collected</h3><ul><li><strong>Personal Data:</strong> While using our Service, we may ask you to provide us with certain personally identifiable information that can be used to contact or identify you ('Personal Data')...</li><li><strong>Usage Data:</strong> We may also collect information on how the Service is accessed and used ('Usage Data')...</li><li><strong>Tracking & Cookies Data:</strong> We use cookies and similar tracking technologies to track the activity on our Service and hold certain information.</li></ul><h2>2. Use of Data</h2><p>[Your Company Name] uses the collected data for various purposes: To provide and maintain the Service; To notify you about changes to our Service; To allow you to participate in interactive features of our Service when you choose to do so...</p><h2>3. Disclosure Of Data</h2><p>Legal Requirements: [Your Company Name] may disclose your Personal Data in the good faith belief that such action is necessary to: To comply with a legal obligation; To protect and defend the rights or property of [Your Company Name]...</p><h2>4. Security Of Data</h2><p>The security of your data is important to us, but remember that no method of transmission over the Internet, or method of electronic storage is 100% secure.</p><h2>5. Contact Us</h2><p>If you have any questions about this Privacy Policy, please contact us at [Your Contact Email/Link].</p>",
      },
      "about-us": {
        title: "About Us",
        content:
          "<h1>About Us</h1><p>Welcome to [Your Company Name]! We are passionate about creating innovative AI solutions that empower businesses and individuals.</p><h2>Our Story</h2><p>[Share a brief story about your company's founding, mission, and vision. Highlight what makes your company unique.]</p><h2>Our Team</h2><p>[Introduce key team members or the team culture. This helps build trust and a personal connection.]</p><h2>Our Values</h2><ul><li><strong>Innovation:</strong> We constantly explore new technologies to deliver cutting-edge solutions.</li><li><strong>Customer Focus:</strong> Our customers are at the heart of everything we do.</li><li><strong>Integrity:</strong> We operate with transparency and uphold the highest ethical standards.</li></ul><h2>Contact Us</h2><p>We'd love to hear from you! Reach out to us at [Your Contact Email/Link] or follow us on [Social Media Links].</p>",
      },
      // Add more static pages as needed
    };

    const pageData = pages[slug];

    if (pageData) {
      res.status(200).json({ success: true, data: pageData });
    } else {
      res.status(404).json({
        success: false,
        error: { code: "PAGE_NOT_FOUND", message: "Page not found" },
      });
    }
  } catch (error) {
    console.error(`Error fetching page content for slug ${req.params.slug}:`, error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Internal server error while fetching page content.",
      },
    });
  }
};

// Get Homepage Content
// TODO: Fetch this content from a database or CMS in a future iteration for better manageability.
export const getHomepageContent = async (req, res) => {
  try {
    const homepageContent = {
      heroSection: {
        title: "Empowering Conversations with Intelligent AI Assistants",
        subtitle:
          "Discover how our advanced AI solutions can revolutionize your customer interactions, streamline operations, and provide personalized experiences at scale.",
        primaryCta: {
          text: "Get Started Free",
          link: "/signup",
        },
        secondaryCta: {
          text: "Explore Features",
          link: "/features",
        },
      },
      services: [
        {
          id: "ai-chatbots",
          name: "AI-Powered Chatbots",
          description:
            "Engage your customers 24/7 with intelligent chatbots that understand context, provide instant answers, and escalate complex queries seamlessly.",
          icon: "chat-bubble-left-right", // Example icon name (e.g., from Heroicons or similar library)
        },
        {
          id: "voice-assistants",
          name: "Voice Assistants",
          description:
            "Leverage the power of voice with our custom voice assistants, offering hands-free interaction and intuitive user experiences.",
          icon: "microphone",
        },
        {
          id: "agent-assist",
          name: "Agent Assist Tools",
          description:
            "Empower your human agents with AI tools that provide real-time suggestions, automate repetitive tasks, and improve overall efficiency.",
          icon: "users-group",
        },
        {
          id: "analytics",
          name: "Interaction Analytics",
          description:
            "Gain valuable insights from customer interactions with our advanced analytics platform, helping you understand sentiment, identify trends, and optimize performance.",
          icon: "chart-bar-square",
        },
      ],
      companyMission: {
        title: "Our Mission: To Make Advanced AI Accessible to All",
        text: "We believe that the power of artificial intelligence should not be confined to large corporations. Our goal is to provide businesses of all sizes with affordable, easy-to-use AI tools that drive growth, enhance customer satisfaction, and unlock new possibilities. We are committed to innovation, ethical AI practices, and a customer-centric approach in everything we do.",
      },
      testimonials: [
        {
          id: "testimonial-1",
          quote:
            "Since implementing their AI chatbot, our customer satisfaction scores have increased by 25%, and our support team can now focus on more complex issues.",
          author: "Jane Doe",
          company: "Innovatech Solutions",
        },
        {
          id: "testimonial-2",
          quote:
            "The voice assistant solution has transformed how our field technicians access information, significantly improving their efficiency and safety.",
          author: "John Smith",
          company: "Field Services Co.",
        },
        {
          id: "testimonial-3",
          quote:
            "The analytics provided by their platform are incredibly insightful. We've been able to identify key areas for improvement in our customer journey.",
          author: "Alice Brown",
          company: "Market Leaders Inc.",
        },
      ],
    };

    res.status(200).json({
      success: true,
      data: homepageContent,
    });
  } catch (error) {
    console.error("Error fetching homepage content:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Internal server error while fetching homepage content.",
      },
    });
  }
};

// Speech-to-Text Facade
// Note: For robust file uploads in production, use middleware like multer.
// This implementation assumes `req.file.buffer` is available if a file is uploaded.
export const speechToTextFacade = async (req, res) => {
  try {
    if (!req.file || !req.file.buffer) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_AUDIO_FILE",
          message:
            "Audio file is required. Ensure you are uploading a file.",
        },
      });
    }
    const audioBuffer = req.file.buffer;
    const audioMimeType = req.file.mimetype; // e.g., 'audio/wav', 'audio/mp3'

    // Get provider and language from request, fallback to config
    const { sttProvider: reqSttProvider, language: reqLanguage } = { ...req.body, ...req.query };

    const defaultConfig = getInternalHomepageAgentConfig().sttConfig;
    const provider = reqSttProvider || defaultConfig.provider;
    const language = reqLanguage || defaultConfig.language; // e.g., 'en-US'

    let transcribedText;

    if (provider === "deepgram") {
      if (!config.apis.deepgram?.apiKey) {
        throw new Error("Deepgram API key is not configured.");
      }

      // Construct Deepgram API URL with parameters
      // Smart format detection is usually on by default, but language can be specified.
      const deepgramUrl = `https://api.deepgram.com/v1/listen?language=${language}&smart_format=true&model=nova-2`; // Example model

      const response = await fetch(deepgramUrl, {
        method: "POST",
        headers: {
          Authorization: `Token ${config.apis.deepgram.apiKey}`,
          "Content-Type": audioMimeType, // Pass the original mimetype of the uploaded file
        },
        body: audioBuffer,
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Deepgram STT API Error:", errorText);
        throw new Error(
          `Deepgram STT API request failed with status ${response.status}: ${errorText}`
        );
      }

      const result = await response.json();

      // Extract transcript - structure may vary based on Deepgram settings
      if (
        result.results &&
        result.results.channels &&
        result.results.channels.length > 0 &&
        result.results.channels[0].alternatives &&
        result.results.channels[0].alternatives.length > 0
      ) {
        transcribedText = result.results.channels[0].alternatives[0].transcript;
      } else {
        console.warn("Deepgram STT response structure not as expected:", result);
        throw new Error("Failed to extract transcript from Deepgram response.");
      }
    } else {
      return res.status(400).json({
        success: false,
        error: {
          code: "STT_PROVIDER_NOT_IMPLEMENTED",
          message: `STT provider '${provider}' is not yet implemented.`,
        },
      });
    }

    if (typeof transcribedText === 'undefined') {
        throw new Error("Transcription failed, text is undefined.");
    }

    res.status(200).json({
      success: true,
      data: {
        text: transcribedText,
      },
    });
  } catch (error) {
    console.error("Error in Speech-to-Text Facade:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "STT_ERROR",
        message: "Speech-to-Text processing failed.",
      },
    });
  }
};

// Text-to-Speech Facade
export const textToSpeechFacade = async (req, res) => {
  try {
    const {
      text,
      ttsProvider: reqProvider,
      voiceId: reqVoiceId,
      language: reqLanguage,
    } = req.body;

    if (!text) {
      return res
        .status(400)
        .json({ success: false, error: { code: "MISSING_TEXT", message: "Text is required." } });
    }

    const defaultConfig = getInternalHomepageAgentConfig().voiceConfig;
    const provider = reqProvider || defaultConfig.provider;
    const voiceId = reqVoiceId || defaultConfig.voiceId;
    const language = reqLanguage || defaultConfig.language;

    let audioBuffer;
    let audioMimeType = "audio/mpeg"; // Default, can be overridden by provider

    if (provider === "playht") {
      if (!config.apis.playht?.apiKey || !config.apis.playht?.userId) {
        throw new Error("PlayHT API key or User ID is not configured.");
      }
      const playHTResponse = await fetch("https://api.play.ht/api/v2/tts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${config.apis.playht.apiKey}`,
          "X-User-ID": config.apis.playht.userId,
        },
        body: JSON.stringify({
          text: text,
          voice: voiceId,
          output_format: "mp3",
          quality: "draft", // or "medium", "high"
        }),
      });

      if (!playHTResponse.ok) {
        const errorData = await playHTResponse.json();
        console.error("PlayHT API Error:", errorData);
        throw new Error(
          `PlayHT API request failed with status ${playHTResponse.status}: ${errorData?.error_message || playHTResponse.statusText}`
        );
      }
      const playHTData = await playHTResponse.json();
      if (!playHTData.href) { // PlayHT v2 returns href for polling
         // For PlayHT v2, you typically need to poll the href provided in the response
         // For simplicity, this example assumes direct audio URL or that the job completes quickly
         // In a production app, implement polling for `playHTData.href`
         // This example will likely fail for longer texts with PlayHT v2 without polling.
         // The provided URL in `playHTData.href` is usually a job URL, not a direct audio link.
         // A quick hack for short audio might be to try and guess a direct link if available, or use their streaming endpoint.
         // However, the documented way is to poll.
         // For this facade, we'll assume the `audioUrl` field (if it existed directly) or simulate fetching if a direct link was available.
         // The example `getPlayhtSpeech` in `playht.controller.js` might have more context on polling if it's from this project.
         // Given the current structure, let's assume we get a direct audio URL or need to fetch it.
         // The PlayHT API response for non-streamed TTS usually gives a job ID and you poll it.
         // Let's try to fetch from `playHTData.href` which is what the v2 API provides.
         // This is a simplification; proper polling for job completion is needed for PlayHT.
        if (playHTData.id && !playHTData.href) { // Check if it's a job ID
            // This part requires implementing a polling mechanism for PlayHT, which is complex for this facade.
            // We will simulate an error or a very quick job completion for now.
            // A proper implementation would poll `https://api.play.ht/api/v2/tts/${playHTData.id}`
            console.warn("PlayHT returned a job ID, direct audio fetch might not work without polling. Attempting direct fetch from provided href (if any) or job URL.");
        }

        // The PlayHT `href` is a job URL. To get audio, you poll it.
        // This is a simplified (and likely to fail for longer jobs) attempt to get audio.
        let attempt = 0;
        let audioReady = false;
        let jobDetails;
        while (attempt < 5 && !audioReady) { // Poll a few times
            await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2s
            const jobStatusResponse = await fetch(playHTData.href, {
                method: "GET",
                headers: {
                    Authorization: `Bearer ${config.apis.playht.apiKey}`,
                    "X-User-ID": config.apis.playht.userId,
                }
            });
            if (!jobStatusResponse.ok) {
                throw new Error(`PlayHT job status check failed: ${jobStatusResponse.statusText}`);
            }
            jobDetails = await jobStatusResponse.json();
            if (jobDetails.output?.url) {
                audioReady = true;
                const audioResponse = await fetch(jobDetails.output.url);
                if (!audioResponse.ok) throw new Error("Failed to fetch PlayHT audio from URL.");
                audioBuffer = await audioResponse.arrayBuffer();
                audioMimeType = audioResponse.headers.get("Content-Type") || "audio/mpeg";
            }
            attempt++;
        }
        if (!audioReady) {
            throw new Error("PlayHT audio did not become ready in time. Full job details: " + JSON.stringify(jobDetails));
        }

      } else if (playHTData.href) { // If href is present, it's likely a job URL to poll
        // Simplified polling logic (production should be more robust)
        let audioUrlFromJob;
        for (let i = 0; i < 5; i++) { // Poll up to 5 times
            await new Promise(resolve => setTimeout(resolve, 1500)); // wait 1.5s
            const jobStatusRes = await fetch(playHTData.href, {
                headers: {
                    "Authorization": `Bearer ${config.apis.playht.apiKey}`,
                    "X-User-ID": config.apis.playht.userId,
                    "Accept": "application/json"
                }
            });
            if (!jobStatusRes.ok) continue;
            const jobStatusData = await jobStatusRes.json();
            if (jobStatusData.output?.url) {
                audioUrlFromJob = jobStatusData.output.url;
                break;
            }
        }
        if (!audioUrlFromJob) throw new Error("PlayHT audio job did not complete in time or no URL found.");
        const audioResp = await fetch(audioUrlFromJob);
        if (!audioResp.ok) throw new Error("Failed to fetch PlayHT audio from job URL.");
        audioBuffer = await audioResp.arrayBuffer();
        audioMimeType = audioResp.headers.get("Content-Type") || "audio/mpeg";
      } else {
         throw new Error("PlayHT API response did not contain expected audio URL or job href.");
      }


    } else if (provider === "cartesia") {
      if (!config.apis.cartesia?.apiKey) {
        throw new Error("Cartesia API key is not configured.");
      }
      const cartesiaResponse = await fetch(
        "https://api.cartesia.ai/tts/bytes",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-API-Key": config.apis.cartesia.apiKey,
          },
          body: JSON.stringify({
            text: text,
            voice_id: voiceId, // Ensure voiceId is compatible with Cartesia (e.g., UUID)
            output_format: "mp3", // or "wav", "flac"
            language: language, // If applicable for Cartesia
          }),
        }
      );
      if (!cartesiaResponse.ok) {
        const errorText = await cartesiaResponse.text();
        console.error("Cartesia API Error:", errorText);
        throw new Error(
          `Cartesia API request failed with status ${cartesiaResponse.status}: ${errorText}`
        );
      }
      // Cartesia /tts/bytes returns raw audio bytes directly
      audioBuffer = await cartesiaResponse.arrayBuffer();
      audioMimeType = cartesiaResponse.headers.get("Content-Type") || "audio/mpeg";


    } else if (provider === "deepgram") {
      // Assuming Deepgram TTS (Speak API)
      if (!config.apis.deepgram?.apiKey) {
        throw new Error("Deepgram API key is not configured.");
      }
      // Note: Deepgram's /speak endpoint might require specific voice models if `voiceId` is used.
      // The URL usually includes the model like: ?model=aura-asteria-en
      // For simplicity, we'll assume `voiceId` maps to a model or is handled by DG.
      const deepgramUrl = `https://api.deepgram.com/v1/speak?model=${voiceId || "aura-asteria-en"}`;
      const deepgramResponse = await fetch(deepgramUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Token ${config.apis.deepgram.apiKey}`,
        },
        body: JSON.stringify({ text }),
      });

      if (!deepgramResponse.ok) {
        const errorText = await deepgramResponse.text();
        console.error("Deepgram TTS API Error:", errorText);
        throw new Error(
          `Deepgram TTS API request failed with status ${deepgramResponse.status}: ${errorText}`
        );
      }
      audioBuffer = await deepgramResponse.arrayBuffer();
      audioMimeType = deepgramResponse.headers.get("Content-Type") || "audio/mpeg";

    } else {
      return res.status(400).json({
        success: false,
        error: {
          code: "UNSUPPORTED_TTS_PROVIDER",
          message: `TTS provider '${provider}' is not supported.`,
        },
      });
    }

    if (!audioBuffer) {
        throw new Error("Audio buffer could not be generated.");
    }

    const audioBase64 = Buffer.from(audioBuffer).toString("base64");

    res.status(200).json({
      success: true,
      data: {
        audioData: `data:${audioMimeType};base64,${audioBase64}`,
        audioType: audioMimeType,
      },
    });
  } catch (error) {
    console.error("Error in Text-to-Speech Facade:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "TTS_ERROR",
        message: "Text-to-Speech processing failed.",
      },
    });
  }
};

// Get Homepage Agent LiveKit Token
export const getHomepageAgentLivekitToken = async (req, res) => {
  try {
    const agentConfig = getInternalHomepageAgentConfig(); // Use internal helper
    const liveKitRoomPrefix = agentConfig.liveKitRoomPrefix;

    let { identity } = req.body;
    if (!identity) {
      identity = `homepage-user-${Math.random().toString(36).substring(7)}`;
    }

    // Construct room name. For homepage agent, might be a fixed room or user-specific if needed.
    // Using prefix + identity for potential uniqueness if multiple users access it simultaneously.
    const roomName = `${liveKitRoomPrefix}${identity}`;

    const API_KEY = config.liveKit.apiKey;
    const API_SECRET = config.liveKit.apiSecret;
    const LIVEKIT_URL = config.liveKit.url;

    if (!API_KEY || !API_SECRET || !LIVEKIT_URL) {
      console.error(
        "LiveKit server configuration is missing in environment config."
      );
      return res.status(500).json({
        success: false,
        error: {
          code: "LIVEKIT_CONFIG_MISSING",
          message: "LiveKit server configuration is incomplete.",
        },
      });
    }

    const at = new AccessToken(API_KEY, API_SECRET, {
      identity: identity,
      ttl: "15m", // Token Time-to-Live
    });

    const grant = {
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canPublishData: true,
      canSubscribe: true,
    };
    at.addGrant(grant);
    const participantToken = await at.toJwt();

    res.status(200).json({
      success: true,
      data: {
        serverUrl: LIVEKIT_URL,
        roomName,
        participantToken,
        participantName: identity, // Return the used identity
      },
    });
  } catch (error) {
    console.error("Error generating Homepage Agent LiveKit token:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "LIVEKIT_TOKEN_ERROR",
        message: "Failed to generate LiveKit token.",
      },
    });
  }
};

// Internal helper to get homepage agent configuration
const getInternalHomepageAgentConfig = () => {
  // TODO: Move this to a database or a more dynamic configuration system in the future
  return {
    assistantName: "Homepage Assistant",
    welcomeMessage:
      "Hello! Welcome to our services. I can help you understand what we offer or guide you through our platform. How can I assist you today?",
    systemPrompt:
      "You are a friendly and helpful assistant for our website. You should provide concise and accurate information to users.",
    voiceConfig: {
      provider: "elevenlabs", // or "playht", "openai", "deepgram"
      voiceId: "21m00Tcm4TlvDq8ikWAM", // Example ElevenLabs voice ID (Arnold)
      language: "en-US",
    },
    sttConfig: {
      provider: "deepgram", // or "google", "whisper"
      language: "en-US",
    },
    interactionModes: ["text", "voice"], // Available interaction modes
    liveKitRoomPrefix: "homepage-agent-", // Prefix for LiveKit room names
  };
};

// Get homepage agent configuration (exposed via API)
export const getHomepageAgentConfig = async (req, res) => {
  try {
    const agentConfig = getInternalHomepageAgentConfig();
    res.status(200).json({
      success: true,
      data: agentConfig,
    });
  } catch (error) {
    console.error("Error fetching homepage agent config:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Internal server error",
      },
    });
  }
};

// Get LiveKit connection details for public access
export const getLivekitConnectionDetails = async (req, res) => {
  try {
    const { assistantId } = req.params;
    const userId = req.user.id;

    // Verify the assistant exists and belongs to the user
    const assistant = await prisma.assistant.findFirst({
      where: {
        id: assistantId,
        userId: userId,
        isActive: true,
      },
      include: {
        user: true,
      },
    });

    if (!assistant) {
      return res.status(404).json({
        success: false,
        error: {
          code: "ASSISTANT_NOT_FOUND",
          message: "Assistant not found or not accessible",
        },
      });
    }

    // Get LiveKit configuration
    const API_KEY = config.liveKit.apiKey;
    const API_SECRET = config.liveKit.apiSecret;
    const LIVEKIT_URL = config.liveKit.url;

    // Generate a unique participant identity
    const participantIdentity = `voice_assistant_user_${Math.floor(
      Math.random() * 10_000
    )}`;
    const roomName = assistant.id;

    // Create an access token with the assistant's attributes
    const at = new AccessToken(API_KEY, API_SECRET, {
      identity: participantIdentity,
      ttl: "15m",
      attributes: {
        id: assistant.id,
        name: assistant.name,
        firstMessage: assistant.firstMessage,
        systemPrompt: assistant.systemPrompt,
        provider: assistant.provider,
        model: assistant.model,
        modes: assistant.modes[1],
        userCalId: assistant.user.calApiKey,
        userId: assistant.userId,
        calEventTypeId: assistant?.user?.calEventTypeId?.toString(),
        isPremium: assistant?.user?.isPremium?.toString(),
      },
    });

    // Add room grant
    const grant = {
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canPublishData: true,
      canSubscribe: true,
    };
    at.addGrant(grant);

    // Generate the token
    const participantToken = await at.toJwt();

    // Return the connection details
    res.status(200).json({
      success: true,
      data: {
        serverUrl: LIVEKIT_URL,
        roomName,
        participantToken,
        participantName: participantIdentity,
      },
    });
  } catch (error) {
    console.error("Error generating LiveKit connection details:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Internal server error",
      },
    });
  }
};

// Send a chat message to an assistant
export const sendChatMessage = async (req, res) => {
  try {
    const { assistantId } = req.params;
    const { message, conversationId } = req.body;
    const userId = req.user.id;

    if (!message) {
      return res.status(400).json({
        success: false,
        error: {
          code: "MISSING_MESSAGE",
          message: "Message is required",
        },
      });
    }

    // Find the assistant and verify it belongs to the user
    const assistant = await prisma.assistant.findFirst({
      where: {
        id: assistantId,
        userId: userId,
        isActive: true,
      },
    });

    if (!assistant) {
      return res.status(404).json({
        success: false,
        error: {
          code: "ASSISTANT_NOT_FOUND",
          message: "Assistant not found or not accessible",
        },
      });
    }

    // Prepare the messages for the AI model
    const systemPrompt = assistant.systemPrompt;

    // Create a conversation history or use existing one
    let conversation;
    if (conversationId) {
      conversation = await prisma.conversation.findFirst({
        where: {
          id: conversationId,
          assistantId: assistantId,
        },
        include: {
          messages: {
            orderBy: {
              createdAt: "asc",
            },
          },
        },
      });

      if (!conversation) {
        return res.status(404).json({
          success: false,
          error: {
            code: "CONVERSATION_NOT_FOUND",
            message: "Conversation not found",
          },
        });
      }
    } else {
      // Create a new conversation
      conversation = await prisma.conversation.create({
        data: {
          assistantId: assistantId,
          userId: userId,
        },
        include: {
          messages: false,
        },
      });
    }

    // Save the user message
    await prisma.message.create({
      data: {
        conversationId: conversation.id,
        content: message,
        role: "user",
      },
    });

    // Prepare messages for the AI model
    const messages = [{ role: "system", content: systemPrompt }];

    // Add conversation history if it exists
    if (conversation.messages && conversation.messages.length > 0) {
      // Add up to the last 10 messages to avoid token limits
      const recentMessages = conversation.messages.slice(-10);
      recentMessages.forEach((msg) => {
        messages.push({ role: msg.role, content: msg.content });
      });
    }

    // Add the current user message
    messages.push({ role: "user", content: message });

    // Call the AI model based on the assistant's provider
    let aiResponse;

    if (assistant.provider === "OpenRouter") {
      // Use OpenRouter API
      const openrouterApiKey = config.apis.openRouter.apiKey;

      const response = await fetch(
        "https://openrouter.ai/api/v1/chat/completions",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${openrouterApiKey}`,
            "HTTP-Referer": "https://talkai247.com",
            "X-Title": "Talkai247",
          },
          body: JSON.stringify({
            model: assistant.model,
            messages: messages,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.status}`);
      }

      const data = await response.json();
      aiResponse = data.choices[0].message.content;
    } else {
      // Default fallback response if provider not supported
      aiResponse = "I'm sorry, I couldn't process your request at this time.";
    }

    // Save the AI response
    await prisma.message.create({
      data: {
        conversationId: conversation.id,
        content: aiResponse,
        role: "assistant",
      },
    });

    // Return the response
    res.status(200).json({
      success: true,
      data: {
        conversationId: conversation.id,
        message: aiResponse,
      },
    });
  } catch (error) {
    console.error("Error processing chat message:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Internal server error",
      },
    });
  }
};
