import express from "express";
import { authenticate<PERSON><PERSON><PERSON><PERSON> } from "../../../middleware/apiKeyMiddleware.js";
import {
  getAssistantById,
  sendChatMessage,
  getLivekitConnectionDetails,
} from "./public.controller.js";

const router = express.Router();

// Public routes that require API key authentication
router.get("/assistant/:id", authenticate<PERSON><PERSON><PERSON><PERSON>, getAssistantById);
router.post("/chat/:assistantId", authenticateApiKey, sendChatMessage);
router.get(
  "/livekit/:assistantId",
  authenticateApiKey,
  getLivekitConnectionDetails
);

export const PublicRoute = router;
