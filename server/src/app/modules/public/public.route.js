import express from "express";
import { authenticate<PERSON><PERSON><PERSON><PERSON> } from "../../../middleware/apiKeyMiddleware.js";
import {
  getAssistantById,
  sendChatMessage,
  getLivekitConnectionDetails,
  getHomepageAgentConfig,
  speechToTextFacade,
  textToSpeechFacade,
  getHomepageAgentLivekitToken,
  getHomepageContent,
  getPageContentBySlug, // Added import
} from "./public.controller.js";

const router = express.Router();

// Public routes that require API key authentication
router.get("/assistant/:id", authenticateApiKey, getAssistantById);
router.post("/chat/:assistantId", authenticateApiKey, sendChatMessage);
router.get(
  "/livekit/:assistantId",
  authenticateApiKey,
  getLivekitConnectionDetails
);

// New route for homepage agent configuration
router.get(
  "/homepage-agent-config",
  authenticate<PERSON>pi<PERSON>ey,
  getHomepageAgentConfig
);

// Routes for STT, TTS, and LiveKit token for homepage agent
router.post("/speech-to-text", authenticate<PERSON>pi<PERSON>ey, speechToTextFacade);
router.post("/text-to-speech", authenticateApiKey, textToSpeechFacade);
router.post(
  "/homepage-agent-livekit-token",
  authenticateApiKey,
  getHomepageAgentLivekitToken
);

// Route for homepage content
router.get("/homepage-content", authenticateApiKey, getHomepageContent);

// Route for dynamic page content by slug
router.get("/page/:slug", authenticateApiKey, getPageContentBySlug);

export const PublicRoute = router;
