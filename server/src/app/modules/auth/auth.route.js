import express from "express";

import {
  getCurrentUser,
  login,
  logout,
  refreshToken,
  register,
} from "../auth/auth.controller.js";
import { authenticateJWT } from "../../../middleware/authMiddleware.js";

const router = express.Router();

// Public routes
router.post("/register", register);
router.post("/login", login);

// Protected routes
router.post("/refresh-token", refreshToken);
router.post("/logout", authenticateJWT, logout);
router.get("/me", authenticateJWT, getCurrentUser);

export const AuthRoute = router;
