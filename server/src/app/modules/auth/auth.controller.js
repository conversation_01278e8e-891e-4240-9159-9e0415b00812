import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import crypto from "crypto";
import { prisma } from "../../../lib/prisma.js";
import config from "../../../config/config.js";

const JWT_SECRET = config.jwt.secret;
const JWT_REFRESH_SECRET = config.jwt.refreshSecret;

// Token expiration times
const ACCESS_TOKEN_EXPIRY = config.jwt.expiresIn;
const REFRESH_TOKEN_EXPIRY = config.jwt.refreshTokenExpiresIn;

// Generate tokens
const generateTokens = (user) => {
  const accessToken = jwt.sign(
    { userId: user.id, email: user.email, role: user.role },
    JWT_SECRET,
    { expiresIn: ACCESS_TOKEN_EXPIRY }
  );
  const refreshToken = jwt.sign(
    { userId: user.id, email: user.email, role: user.role },
    JWT_REFRESH_SECRET,
    { expiresIn: REFRESH_TOKEN_EXPIRY }
  );
  return { accessToken, refreshToken };
};

export const register = async (req, res) => {
  try {
    const { email, password, name, company } = req.body;

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({ where: { email } });
    if (existingUser) {
      return res.status(409).json({ message: "User already exists" });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Generate API keys (similar to Stripe's format)
    const generateApiKey = (prefix) => {
      const randomPart = crypto.randomBytes(16).toString("hex");
      return `${prefix}_${randomPart}`;
    };

    const publicApiKey = generateApiKey("pk_live");
    const privateSecretKey = generateApiKey("sk_live");

    // Encrypt the private key before storing
    // const encryptedPrivateKey = encrypt(privateSecretKey);

    // Create user with API keys
    const newUser = await prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name,
        company: company || null,
        settings: {},
        publicApiKey,
        privateSecretKey: privateSecretKey, //encryptedPrivateKey,
      },
    });

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(newUser);
    // Set refresh token in HTTP-only cookie
    res.cookie("refreshToken", refreshToken, {
      httpOnly: true,
      secure: config.server.nodeEnv === "production",
      sameSite: "strict",
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    // Return user data with keys (excluding password and encrypted private key)
    const {
      password: _,
      privateSecretKey: __,
      ...userWithoutSensitiveData
    } = newUser;

    // Include the original private key in response (only this one time)
    res.status(201).json({
      user: userWithoutSensitiveData,
      accessToken,
      keys: {
        publicKey: publicApiKey,
        privateKey: privateSecretKey, // Only returned once during registration
        warning: "Store your private key securely. It will not be shown again.",
      },
    });
  } catch (error) {
    console.error("Registration error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};
// Login user
export const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user
    const user = await prisma.user.findUnique({ where: { email } });
    if (!user) {
      return res
        .status(404)
        .json({ message: `User ${email} doesn't exist in our database.` });
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(400).json({
        message: `Invalid credentials. Password doesn't match.`,
      });
    }

    // Generate tokens
    const { accessToken, refreshToken } = generateTokens(user);

    // Set refresh token in HTTP-only cookie
    res.cookie("refreshToken", refreshToken, {
      httpOnly: true,
      secure: config.server.nodeEnv === "production",
      sameSite: "strict",
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    });

    // Return user data (excluding password) and access token
    const { password: _, privateSecretKey: __, ...userWithoutPassword } = user;
    res.status(200).json({ user: userWithoutPassword, accessToken });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// Refresh access token
export const refreshToken = async (req, res) => {
  try {
    // Check if cookies object exists
    if (!req.cookies) {
      console.error(
        "Cookies object is undefined. Cookie-parser middleware might not be configured properly."
      );
      return res.status(401).json({
        message: "No refresh token provided",
        details: "Cookie parsing failed",
      });
    }

    const refreshToken = req.cookies.refreshToken;
    if (!refreshToken) {
      return res.status(401).json({ message: "No refresh token provided" });
    }

    // Verify refresh token
    const decoded = jwt.verify(refreshToken, JWT_REFRESH_SECRET);
    const userId = decoded.userId;

    // Check if user exists
    const user = await prisma.user.findUnique({ where: { id: userId } });
    if (!user) {
      return res.status(401).json({ message: "Invalid user" });
    }

    // Generate new access token
    const accessToken = jwt.sign(
      { userId: user.id, email: user.email, role: user.role },
      JWT_SECRET,
      { expiresIn: ACCESS_TOKEN_EXPIRY }
    );

    res.status(200).json({ accessToken });
  } catch (error) {
    console.error("Refresh token error:", error);

    // Provide more specific error messages based on the error type
    if (error instanceof jwt.JsonWebTokenError) {
      return res.status(401).json({ message: "Invalid refresh token" });
    } else if (error instanceof jwt.TokenExpiredError) {
      return res.status(401).json({ message: "Refresh token expired" });
    }

    res
      .status(401)
      .json({ message: "Invalid refresh token", details: error.message });
  }
};

// Logout user
export const logout = async (req, res) => {
  try {
    // Clear refresh token cookie
    res.clearCookie("refreshToken", {
      httpOnly: true,
      secure: config.server.nodeEnv === "production",
      sameSite: "strict",
    });

    res.status(200).json({ message: "Logged out successfully" });
  } catch (error) {
    console.error("Logout error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get current user
export const getCurrentUser = async (req, res) => {
  try {
    // This middleware should run after the authenticateJWT middleware
    const userId = req.user.id;

    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    const { password: _, ...userWithoutPassword } = user;
    res.status(200).json({ user: userWithoutPassword });
    // res.status(200).json(user);
  } catch (error) {
    console.error("Get current user error:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};
