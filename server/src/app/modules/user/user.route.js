import express from "express";

// // import { getCurrentUser } from '../controllers/authController.js';
// import { authenticateJWT } from "../middleware/authMiddleware.js";
// import { updateUser } from "../controllers/userController.js";

// const router = express.Router();

// // Protected routes
// router.put("/update-user/:id", authenticateJWT, updateUser);
// // router.get('/me', authenticateJWT, getCurrentUser);

// export default router;

const router = express.Router();
import {
  changePassword,
  getApiKeys,
  getUserProfile,
  updateApiKeys,
  updateUserProfile,
} from "./user.controller.js";
import { authenticateJWT } from "../../../middleware/authMiddleware.js";

// Protected routes
router.get("/profile", authenticateJWT, getUserProfile);
router.put("/profile", authenticateJWT, updateUserProfile);
router.put("/api-keys", authenticateJWT, updateApiKeys);
router.get("/api-keys", authenticateJWT, getApiKeys);
router.post("/change-password", authenticateJWT, changePassword);

export const UserRoute = router;
