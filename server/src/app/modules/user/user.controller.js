import crypto from "crypto";
import bcrypt from "bcryptjs";
import { prisma } from "../../../lib/prisma.js";
import config from "../../../config/config.js";

// Encryption configuration
const algorithm = "aes-256-cbc";
const ENCRYPTION_KEY = config.encryption.key;

// Validate or generate encryption key
function getEncryptionKey() {
  if (ENCRYPTION_KEY) {
    // Ensure the key is exactly 32 bytes (256 bits) for AES-256
    if (Buffer.from(ENCRYPTION_KEY, "hex").length === 32) {
      return Buffer.from(ENCRYPTION_KEY, "hex");
    }
    // If not hex-encoded, hash it to get 32 bytes
    return crypto.createHash("sha256").update(ENCRYPTION_KEY).digest();
  }
  // Generate a new key if none is provided
  return crypto.randomBytes(32);
}

const secretKey = getEncryptionKey();
const ivLength = 16; // AES block size

// Helper function to encrypt sensitive data
function encrypt(text) {
  try {
    const iv = crypto.randomBytes(ivLength);
    const cipher = crypto.createCipheriv(algorithm, secretKey, iv);
    let encrypted = cipher.update(text, "utf8", "hex");
    encrypted += cipher.final("hex");
    // Return IV + encrypted data (IV doesn't need to be secret)
    return iv.toString("hex") + ":" + encrypted;
  } catch (error) {
    console.error("Encryption error:", error);
    throw new Error("Encryption failed");
  }
}

// Helper function to decrypt sensitive data
function decrypt(encryptedText) {
  try {
    const parts = encryptedText.split(":");
    const iv = Buffer.from(parts.shift(), "hex");
    const encrypted = parts.join(":");
    const decipher = crypto.createDecipheriv(algorithm, secretKey, iv);
    let decrypted = decipher.update(encrypted, "hex", "utf8");
    decrypted += decipher.final("utf8");
    return decrypted;
  } catch (error) {
    console.error("Decryption error:", error);
    throw new Error("Decryption failed");
  }
}

// Get user profile
export const getUserProfile = async (req, res) => {
  try {
    console.log("req.user", req.user);
    const userId = req.user.id;
    console.log("userId", userId);
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }
    const { password: _, ...userWithoutPassword } = user;
    res.status(200).json(userWithoutPassword);
    // res.json(user);
  } catch (error) {
    console.error("Error fetching user profile:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

// Update user profile
export const updateUserProfile = async (req, res) => {
  try {
    const userId = req.user.id;
    const { name, email, company, phoneNumber, settings } = req.body;

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        name,
        email,
        company,
        phoneNumber,
        settings,
      },
      //   select: {
      //     id: true,
      //     email: true,
      //     name: true,
      //     company: true,
      //     role: true,
      //     phoneNumber: true,
      //     settings: true,
      //     calEventTypeId: true,
      //     publicApiKey: true,
      //     createdAt: true,
      //     updatedAt: true,
      //   },
    });
    const { password: _, ...userWithoutPassword } = updatedUser;
    res.status(200).json(userWithoutPassword);
    // res.json(updatedUser);
  } catch (error) {
    console.error("Error updating user profile:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};
// Update API keys
export const updateApiKeys = async (req, res) => {
  try {
    const userId = req.user.id;
    const { calEventTypeId, calApiKey } = req.body;

    // Validate input
    if (!userId) {
      return res.status(400).json({ error: "User ID is required" });
    }

    // Encrypt sensitive keys before storing
    let encryptedCalApiKey = null;
    // let encryptedPrivateApiKey = null;

    if (calApiKey && calApiKey.startsWith("cal_")) {
      try {
        encryptedCalApiKey = encrypt(calApiKey);
      } catch (error) {
        console.error("Error encrypting Cal API key:", error);
        return res.status(500).json({ error: "Failed to encrypt Cal API key" });
      }
    } else {
      encryptedCalApiKey = calApiKey;
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        calEventTypeId,
        calApiKey: encryptedCalApiKey,
      },
    });

    // Remove sensitive fields before sending response
    const {
      password: _,
      calApiKey: __,
      privateSecretKey: ___,
      publicApiKey: ____,
      ...safeUserData
    } = updatedUser;

    res.status(200).json(safeUserData);
  } catch (error) {
    console.error("Error updating API keys:", error);
    res.status(500).json({
      error: "Internal server error",
      details: config.server.isDevelopment ? error.message : undefined,
    });
  }
};

// Get decrypted API keys (only when needed)
export const getApiKeys = async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        calEventTypeId: true,
        calApiKey: true,
        publicApiKey: true,
        privateSecretKey: true,
      },
    });

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Decrypt the sensitive keys
    const decryptedKeys = {
      calEventTypeId: user.calEventTypeId,
      publicApiKey: user.publicApiKey,
      calApiKey: user.calApiKey ? decrypt(user.calApiKey) : null,
      //   privateSecretKey: user.privateSecretKey ? decrypt(user.privateSecretKey) : null,
    };

    res.json(decryptedKeys);
  } catch (error) {
    console.error("Error fetching API keys:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};

// Change password
export const changePassword = async (req, res) => {
  try {
    const userId = req.user.id;
    const { currentPassword, newPassword } = req.body;

    const user = await prisma.user.findUnique({ where: { id: userId } });

    if (!user) {
      return res.status(404).json({ error: "User not found" });
    }

    // Verify current password
    const isMatch = await bcrypt.compare(currentPassword, user.password);
    if (!isMatch) {
      return res.status(400).json({ error: "Current password is incorrect" });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    await prisma.user.update({
      where: { id: userId },
      data: { password: hashedPassword },
    });

    res.json({ message: "Password updated successfully" });
  } catch (error) {
    console.error("Error changing password:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};
