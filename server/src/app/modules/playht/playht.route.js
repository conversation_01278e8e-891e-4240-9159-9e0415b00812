import express from "express";

import { authenticateJWT } from "../../../middleware/authMiddleware.js";
import {
  getPlayhtPreview,
  getPlayhtSpeech,
  getPlayhtVoices,
} from "./playht.controller.js";

const router = express.Router();

router.post("/speech", authenticateJWT, getPlayhtSpeech);
router.get("/voices", authenticateJWT, getPlayhtVoices);
router.get("/preview", authenticateJWT, getPlayhtPreview);
// router.get("/me", authenticateJWT, getCurrentUser);

export const PlayhtRoute = router;
