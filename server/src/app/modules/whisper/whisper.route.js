import express from "express";
import {
  getWhisperTemplates,
  createWhisperTemplate,
  updateWhisperTemplate,
  deleteWhisperTemplate,
  getWhisperConnectionDetails,
  endWhisperCall,
  getContactGoals,
  updateCallTranscript,
} from "./whisper.controller.js";
import { authenticateJWT } from "../../../middleware/authMiddleware.js";

const router = express.Router();

// Whisper templates routes
router.get("/templates", authenticateJWT, getWhisperTemplates);
router.post("/templates", authenticateJWT, createWhisperTemplate);
router.put("/templates/:id", authenticateJWT, updateWhisperTemplate);
router.delete("/templates/:id", authenticateJWT, deleteWhisperTemplate);

// Whisper call routes
router.get("/connection-details", authenticateJWT, getWhisperConnectionDetails);
router.post("/calls/:callId/end", authenticateJWT, endWhisperCall);
router.get("/contacts/:contactId/goals", authenticateJWT, getContactGoals);
router.put("/calls/:callId/transcript", authenticateJWT, updateCallTranscript);

export default router;
