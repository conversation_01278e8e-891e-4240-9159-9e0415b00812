import express from "express";
import {
  getWhisperTemplates,
  createWhisperTemplate,
  updateWhisperTemplate,
  deleteWhisperTemplate,
  getWhisperConnectionDetails,
  endWhisperCall,
  getContactGoals,
  updateCallTranscript,
  initializeSipConfiguration,
  getSipConfigurationStatus,
  setWhisperMode,
  getRoomState,
} from "./whisper.controller.js";
import { authenticateJWT } from "../../../middleware/authMiddleware.js";

const router = express.Router();

// Whisper templates routes
router.get("/templates", authenticateJWT, getWhisperTemplates);
router.post("/templates", authenticateJWT, createWhisperTemplate);
router.put("/templates/:id", authenticateJWT, updateWhisperTemplate);
router.delete("/templates/:id", authenticateJWT, deleteWhisperTemplate);

// Whisper call routes
router.get("/connection-details", authenticateJWT, getWhisperConnectionDetails);
router.post("/calls/:callId/end", authenticateJWT, endWhisperCall);
router.get("/contacts/:contactId/goals", authenticateJWT, getContactGoals);
router.put("/calls/:callId/transcript", authenticateJWT, updateCallTranscript);

// SIP configuration routes (admin only)
router.post("/sip/initialize", authenticateJWT, initializeSipConfiguration);
router.get("/sip/status", authenticateJWT, getSipConfigurationStatus);

// Whisper mode control routes
router.post("/calls/:callId/mode", authenticateJWT, setWhisperMode);
router.get("/rooms/:roomName/state", authenticateJWT, getRoomState);

export default router;
