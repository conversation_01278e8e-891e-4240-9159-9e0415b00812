import { prisma } from "../../../lib/prisma.js";
import config from "../../../config/config.js";
import { AccessToken } from "livekit-server-sdk";
import sipService from "../../../services/sipService.js";
import livekitSipService from "../../../services/livekitSipService.js";
import livekitService from "../../../services/livekitService.js";
import whisperAudioRoutingService from "../../../services/whisperAudioRoutingService.js";

/**
 * Get all whisper templates for a user
 */
export const getWhisperTemplates = async (req, res) => {
  try {
    const userId = req.user.id;

    const templates = await prisma.whisperTemplate.findMany({
      where: {
        OR: [{ userId }, { isSystem: true }],
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    res.json({
      success: true,
      data: templates,
    });
  } catch (error) {
    console.error("Error fetching whisper templates:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to fetch whisper templates",
      },
    });
  }
};

/**
 * Create a new whisper template
 */
export const createWhisperTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const { name, type, systemPrompt, editablePrompt, tags = [] } = req.body;

    if (!name || !type || !systemPrompt || !editablePrompt) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Missing required fields",
        },
      });
    }

    const template = await prisma.whisperTemplate.create({
      data: {
        userId,
        name,
        type,
        systemPrompt,
        editablePrompt,
        tags,
        isSystem: false,
        isHidden: false,
      },
    });

    res.status(201).json({
      success: true,
      data: template,
    });
  } catch (error) {
    console.error("Error creating whisper template:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create whisper template",
      },
    });
  }
};

/**
 * Update a whisper template
 */
export const updateWhisperTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const { name, type, systemPrompt, editablePrompt, tags, isHidden } =
      req.body;

    // Check if template exists and belongs to user
    const existingTemplate = await prisma.whisperTemplate.findFirst({
      where: {
        id,
        userId,
      },
    });

    if (!existingTemplate) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Template not found or not owned by user",
        },
      });
    }

    // Don't allow updating system templates
    if (existingTemplate.isSystem) {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "System templates cannot be modified",
        },
      });
    }

    const updatedTemplate = await prisma.whisperTemplate.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(type && { type }),
        ...(systemPrompt && { systemPrompt }),
        ...(editablePrompt && { editablePrompt }),
        ...(tags && { tags }),
        ...(isHidden !== undefined && { isHidden }),
      },
    });

    res.json({
      success: true,
      data: updatedTemplate,
    });
  } catch (error) {
    console.error("Error updating whisper template:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update whisper template",
      },
    });
  }
};

/**
 * Delete a whisper template
 */
export const deleteWhisperTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    // Check if template exists and belongs to user
    const existingTemplate = await prisma.whisperTemplate.findFirst({
      where: {
        id,
        userId,
      },
    });

    if (!existingTemplate) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Template not found or not owned by user",
        },
      });
    }

    // Don't allow deleting system templates
    if (existingTemplate.isSystem) {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "System templates cannot be deleted",
        },
      });
    }

    await prisma.whisperTemplate.delete({
      where: { id },
    });

    res.json({
      success: true,
      message: "Template deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting whisper template:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to delete whisper template",
      },
    });
  }
};

/**
 * Get whisper connection details for a call
 */
export const getWhisperConnectionDetails = async (req, res) => {
  try {
    const userId = req.user.id;
    const { contactId } = req.query;

    if (!contactId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Contact ID is required",
        },
      });
    }

    // Check if contact exists and belongs to user
    const contact = await prisma.contact.findFirst({
      where: {
        id: contactId,
        userId,
      },
    });

    if (!contact) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Contact not found or not owned by user",
        },
      });
    }

    // Create a whisper room using LiveKit SIP service
    const room = await livekitSipService.createWhisperRoom(contactId, userId);
    const roomName = room.name;

    // Generate participant identities for the three-participant architecture
    const userIdentity = `user_${userId}_${Date.now()}`;
    const callerIdentity = `caller_${contactId}_${Date.now()}`;
    const aiIdentity = `ai_assistant_${Date.now()}`;

    const API_KEY = config.liveKit.apiKey;
    const API_SECRET = config.liveKit.apiSecret;
    const LIVEKIT_URL = config.liveKit.url;

    // Create token for the User participant
    const userToken = new AccessToken(API_KEY, API_SECRET, {
      identity: userIdentity,
      ttl: "60m", // 1 hour token
      metadata: JSON.stringify({
        userId,
        contactId,
        contactName: contact.name,
        contactType: contact.type,
        participantType: "user",
        timestamp: new Date().toISOString(),
      }),
    });

    // User permissions: can publish, subscribe, and send data
    const userGrant = {
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canPublishData: true,
      canSubscribe: true,
      hidden: false, // User is visible
    };
    userToken.addGrant(userGrant);

    // Create token for the AI Assistant (hidden participant)
    const aiToken = new AccessToken(API_KEY, API_SECRET, {
      identity: aiIdentity,
      ttl: "60m",
      metadata: JSON.stringify({
        userId,
        contactId,
        contactName: contact.name,
        participantType: "ai_assistant",
        timestamp: new Date().toISOString(),
      }),
    });

    // AI Assistant permissions: can publish, subscribe, send data, but is hidden
    const aiGrant = {
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canPublishData: true,
      canSubscribe: true,
      hidden: true, // AI is hidden from other participants
    };
    aiToken.addGrant(aiGrant);

    // Generate the JWT tokens
    const userParticipantToken = await userToken.toJwt();
    const aiParticipantToken = await aiToken.toJwt();

    // Find or create a default assistant for whisper calls
    let defaultAssistant = await prisma.assistant.findFirst({
      where: {
        userId,
        name: "Whisper Assistant",
      },
    });

    if (!defaultAssistant) {
      // Create a default assistant for this user
      defaultAssistant = await prisma.assistant.create({
        data: {
          userId,
          name: "Whisper Assistant",
          modes: ["voice"],
          firstMessage: "Hello, I'm your Whisper Assistant.",
          systemPrompt:
            "You are a helpful assistant that provides whisper advice during calls.",
          provider: "openai",
          model: "gpt-4",
          tools: [],
          voice: {
            provider: "elevenlabs",
            voiceId: "default",
            settings: {
              speed: 1.0,
              pitch: 1.0,
              stability: 0.5,
              volume: 1.0,
            },
          },
          isActive: true,
        },
      });
    }

    // Create a call record in the database
    const call = await prisma.call.create({
      data: {
        userId,
        contactId,
        assistantId: defaultAssistant.id, // Use the default assistant ID
        startTime: new Date(),
        status: "IN_PROGRESS",
        transcript: [],
        goals: [],
        metrics: {
          averageSentiment: 0,
          sentimentTimeline: [],
          whisperEffectiveness: 0,
          goalCompletion: 0,
        },
      },
    });

    res.json({
      success: true,
      data: {
        serverUrl: LIVEKIT_URL,
        roomName,
        // User participant details
        userParticipant: {
          token: userParticipantToken,
          identity: userIdentity,
          type: "user",
        },
        // AI Assistant participant details
        aiParticipant: {
          token: aiParticipantToken,
          identity: aiIdentity,
          type: "ai_assistant",
        },
        // Caller participant details (will join via SIP)
        callerParticipant: {
          identity: callerIdentity,
          type: "caller",
        },
        callId: call.id,
        // Whisper configuration
        whisperConfig: {
          mode: "ai-to-user", // Default mode
          goals: [], // Will be populated based on contact goals
        },
        // Instructions for AI agent
        agentInstructions: {
          systemPrompt: `You are an AI assistant providing whisper advice during a call with ${contact.name}.
Contact Type: ${contact.type}
Your role: Listen to the conversation and provide brief, actionable whisper suggestions to help the user achieve their goals.
Keep responses under 20 words. Be strategic and supportive.`,
          contactInfo: {
            name: contact.name,
            type: contact.type,
            phone: contact.phone,
            email: contact.email,
          },
        },
      },
    });
  } catch (error) {
    console.error("Error generating whisper connection details:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to generate connection details",
      },
    });
  }
};

/**
 * End a whisper call
 */
export const endWhisperCall = async (req, res) => {
  try {
    const userId = req.user.id;
    const { callId } = req.params;

    if (!callId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Call ID is required",
        },
      });
    }

    // Check if call exists and belongs to user
    const call = await prisma.call.findFirst({
      where: {
        id: callId,
        userId,
        status: "IN_PROGRESS",
      },
    });

    if (!call) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Active call not found or not owned by user",
        },
      });
    }

    // Calculate call duration
    const endTime = new Date();
    const duration = Math.floor(
      (endTime.getTime() - call.startTime.getTime()) / 1000
    ); // Duration in seconds

    // Update call record
    const updatedCall = await prisma.call.update({
      where: { id: callId },
      data: {
        endTime,
        duration,
        status: "COMPLETED",
      },
    });

    res.json({
      success: true,
      data: {
        callId: updatedCall.id,
        duration: updatedCall.duration,
        status: updatedCall.status,
      },
    });
  } catch (error) {
    console.error("Error ending whisper call:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to end call",
      },
    });
  }
};

/**
 * Get contact goals for whisper
 */
export const getContactGoals = async (req, res) => {
  try {
    const userId = req.user.id;
    const { contactId } = req.params;

    if (!contactId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Contact ID is required",
        },
      });
    }

    // Check if contact exists and belongs to user
    const contact = await prisma.contact.findFirst({
      where: {
        id: contactId,
        userId,
      },
    });

    if (!contact) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Contact not found or not owned by user",
        },
      });
    }

    // Get goals for this contact
    // For now, we'll return mock goals since the actual goals implementation is not yet in place
    const mockGoals = [
      {
        id: "goal1",
        title: "Upsell Premium Package",
        aiPrompt:
          "Suggest premium features when the customer shows interest in basic services",
        completed: false,
        progress: 0,
      },
      {
        id: "goal2",
        title: "Address Customer Concerns",
        aiPrompt:
          "Identify and address any concerns the customer raises about the service",
        completed: false,
        progress: 0,
      },
    ];

    res.json({
      success: true,
      data: mockGoals,
    });
  } catch (error) {
    console.error("Error fetching contact goals:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to fetch contact goals",
      },
    });
  }
};

/**
 * Update call transcript and goals progress
 */
export const updateCallTranscript = async (req, res) => {
  try {
    const userId = req.user.id;
    const { callId } = req.params;
    const { transcript, goals } = req.body;

    if (!callId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Call ID is required",
        },
      });
    }

    // Check if call exists and belongs to user
    const call = await prisma.call.findFirst({
      where: {
        id: callId,
        userId,
      },
    });

    if (!call) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Call not found or not owned by user",
        },
      });
    }

    // Update call record with new transcript entries and goals
    const updatedCall = await prisma.call.update({
      where: { id: callId },
      data: {
        transcript: transcript || call.transcript,
        goals: goals || call.goals,
      },
    });

    res.json({
      success: true,
      data: {
        callId: updatedCall.id,
        transcript: updatedCall.transcript,
        goals: updatedCall.goals,
      },
    });
  } catch (error) {
    console.error("Error updating call transcript:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update call transcript",
      },
    });
  }
};

/**
 * Initialize SIP configuration for Whisper feature
 * Sets up Twilio SIP trunk and LiveKit integration
 */
export const initializeSipConfiguration = async (req, res) => {
  try {
    const userId = req.user.id;

    // Check if user is admin or has permission to configure SIP
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== "ADMIN") {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "Only administrators can configure SIP settings",
        },
      });
    }

    console.log("Initializing SIP configuration for Whisper feature...");

    // Create Twilio SIP trunk
    const sipTrunk = await sipService.createSipTrunk();

    // Create LiveKit SIP configuration
    const inboundTrunk = await livekitSipService.createInboundTrunk();
    const outboundTrunk = await livekitSipService.createOutboundTrunk();
    const dispatchRule = await livekitSipService.createDispatchRule();

    res.json({
      success: true,
      data: {
        message: "SIP configuration initialized successfully",
        twilioTrunk: {
          sid: sipTrunk.sid,
          domainName: sipTrunk.domainName,
          friendlyName: sipTrunk.friendlyName,
        },
        livekitConfig: {
          inboundTrunk,
          outboundTrunk,
          dispatchRule,
        },
      },
    });
  } catch (error) {
    console.error("Error initializing SIP configuration:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to initialize SIP configuration",
        details: error.message,
      },
    });
  }
};

/**
 * Get SIP configuration status
 */
export const getSipConfigurationStatus = async (req, res) => {
  try {
    const userId = req.user.id;

    // Check if user has permission to view SIP configuration
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || user.role !== "ADMIN") {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "Only administrators can view SIP configuration",
        },
      });
    }

    // Get Twilio SIP trunks
    const sipTrunks = await sipService.listSipTrunks();

    // Get LiveKit whisper rooms
    const whisperRooms = await livekitSipService.listWhisperRooms();

    res.json({
      success: true,
      data: {
        twilioSipTrunks: sipTrunks.map((trunk) => ({
          sid: trunk.sid,
          friendlyName: trunk.friendlyName,
          domainName: trunk.domainName,
          status: trunk.status,
        })),
        activeWhisperRooms: whisperRooms.length,
        whisperRooms: whisperRooms.map((room) => ({
          name: room.name,
          numParticipants: room.numParticipants,
          creationTime: room.creationTime,
        })),
      },
    });
  } catch (error) {
    console.error("Error getting SIP configuration status:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get SIP configuration status",
        details: error.message,
      },
    });
  }
};

/**
 * Set whisper mode for a call
 * Controls the audio routing between participants
 */
export const setWhisperMode = async (req, res) => {
  try {
    const userId = req.user.id;
    const { callId } = req.params;
    const { mode, roomName, participants } = req.body;

    if (!callId || !mode || !roomName || !participants) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Call ID, mode, room name, and participants are required",
        },
      });
    }

    // Validate mode
    const validModes = ["ai-to-user", "user-to-ai", "normal"];
    if (!validModes.includes(mode)) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message:
            "Invalid whisper mode. Must be one of: ai-to-user, user-to-ai, normal",
        },
      });
    }

    // Check if call exists and belongs to user
    const call = await prisma.call.findFirst({
      where: {
        id: callId,
        userId,
        status: "IN_PROGRESS",
      },
    });

    if (!call) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Active call not found or not owned by user",
        },
      });
    }

    const { userIdentity, callerIdentity, aiIdentity } = participants;

    let result;
    switch (mode) {
      case "ai-to-user":
        result = await whisperAudioRoutingService.setupAiToUserMode(
          roomName,
          userIdentity,
          callerIdentity,
          aiIdentity
        );
        break;
      case "user-to-ai":
        result = await whisperAudioRoutingService.setupUserToAiMode(
          roomName,
          userIdentity,
          callerIdentity,
          aiIdentity
        );
        break;
      case "normal":
        result = await whisperAudioRoutingService.setupNormalMode(
          roomName,
          userIdentity,
          callerIdentity,
          aiIdentity
        );
        break;
    }

    res.json({
      success: true,
      data: {
        callId,
        mode,
        roomName,
        audioRouting: result,
      },
    });
  } catch (error) {
    console.error("Error setting whisper mode:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to set whisper mode",
        details: error.message,
      },
    });
  }
};

/**
 * Get current room state and participant information
 */
export const getRoomState = async (req, res) => {
  try {
    const userId = req.user.id;
    const { roomName } = req.params;

    if (!roomName) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Room name is required",
        },
      });
    }

    // Get room state
    const roomState = await whisperAudioRoutingService.getRoomState(roomName);

    res.json({
      success: true,
      data: roomState,
    });
  } catch (error) {
    console.error("Error getting room state:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to get room state",
        details: error.message,
      },
    });
  }
};

/**
 * Trigger AI agent to join a whisper room
 * This endpoint is called to start the AI assistant for a whisper session
 */
export const triggerAiAgent = async (req, res) => {
  try {
    const userId = req.user.id;
    const { callId, roomName, agentInstructions } = req.body;

    if (!callId || !roomName) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Call ID and room name are required",
        },
      });
    }

    // Check if call exists and belongs to user
    const call = await prisma.call.findFirst({
      where: {
        id: callId,
        userId,
        status: "IN_PROGRESS",
      },
    });

    if (!call) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Active call not found or not owned by user",
        },
      });
    }

    // In a production environment, this would trigger the AI agent to join
    // For now, we'll simulate the agent joining by updating the call record
    const updatedCall = await prisma.call.update({
      where: { id: callId },
      data: {
        transcript: [
          ...call.transcript,
          {
            type: "system",
            message:
              "AI Assistant has joined the call and is ready to provide whisper suggestions.",
            timestamp: new Date().toISOString(),
          },
        ],
      },
    });

    // TODO: In production, trigger the Python AI agent to join the room
    // This could be done via:
    // 1. Message queue (Redis/RabbitMQ)
    // 2. Direct HTTP call to agent service
    // 3. WebSocket message to agent manager

    console.log(`AI Agent triggered for room: ${roomName}, call: ${callId}`);

    res.json({
      success: true,
      data: {
        callId,
        roomName,
        message: "AI Agent triggered successfully",
        agentStatus: "joining",
      },
    });
  } catch (error) {
    console.error("Error triggering AI agent:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to trigger AI agent",
        details: error.message,
      },
    });
  }
};
