import { prisma } from "../../../lib/prisma.js";
import config from "../../../config/config.js";
import { AccessToken } from "livekit-server-sdk";

/**
 * Get all whisper templates for a user
 */
export const getWhisperTemplates = async (req, res) => {
  try {
    const userId = req.user.id;

    const templates = await prisma.whisperTemplate.findMany({
      where: {
        OR: [{ userId }, { isSystem: true }],
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    res.json({
      success: true,
      data: templates,
    });
  } catch (error) {
    console.error("Error fetching whisper templates:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to fetch whisper templates",
      },
    });
  }
};

/**
 * Create a new whisper template
 */
export const createWhisperTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const { name, type, systemPrompt, editablePrompt, tags = [] } = req.body;

    if (!name || !type || !systemPrompt || !editablePrompt) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Missing required fields",
        },
      });
    }

    const template = await prisma.whisperTemplate.create({
      data: {
        userId,
        name,
        type,
        systemPrompt,
        editablePrompt,
        tags,
        isSystem: false,
        isHidden: false,
      },
    });

    res.status(201).json({
      success: true,
      data: template,
    });
  } catch (error) {
    console.error("Error creating whisper template:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to create whisper template",
      },
    });
  }
};

/**
 * Update a whisper template
 */
export const updateWhisperTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;
    const { name, type, systemPrompt, editablePrompt, tags, isHidden } =
      req.body;

    // Check if template exists and belongs to user
    const existingTemplate = await prisma.whisperTemplate.findFirst({
      where: {
        id,
        userId,
      },
    });

    if (!existingTemplate) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Template not found or not owned by user",
        },
      });
    }

    // Don't allow updating system templates
    if (existingTemplate.isSystem) {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "System templates cannot be modified",
        },
      });
    }

    const updatedTemplate = await prisma.whisperTemplate.update({
      where: { id },
      data: {
        ...(name && { name }),
        ...(type && { type }),
        ...(systemPrompt && { systemPrompt }),
        ...(editablePrompt && { editablePrompt }),
        ...(tags && { tags }),
        ...(isHidden !== undefined && { isHidden }),
      },
    });

    res.json({
      success: true,
      data: updatedTemplate,
    });
  } catch (error) {
    console.error("Error updating whisper template:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update whisper template",
      },
    });
  }
};

/**
 * Delete a whisper template
 */
export const deleteWhisperTemplate = async (req, res) => {
  try {
    const userId = req.user.id;
    const { id } = req.params;

    // Check if template exists and belongs to user
    const existingTemplate = await prisma.whisperTemplate.findFirst({
      where: {
        id,
        userId,
      },
    });

    if (!existingTemplate) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Template not found or not owned by user",
        },
      });
    }

    // Don't allow deleting system templates
    if (existingTemplate.isSystem) {
      return res.status(403).json({
        success: false,
        error: {
          code: "FORBIDDEN",
          message: "System templates cannot be deleted",
        },
      });
    }

    await prisma.whisperTemplate.delete({
      where: { id },
    });

    res.json({
      success: true,
      message: "Template deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting whisper template:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to delete whisper template",
      },
    });
  }
};

/**
 * Get whisper connection details for a call
 */
export const getWhisperConnectionDetails = async (req, res) => {
  try {
    const userId = req.user.id;
    const { contactId } = req.query;

    if (!contactId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Contact ID is required",
        },
      });
    }

    // Check if contact exists and belongs to user
    const contact = await prisma.contact.findFirst({
      where: {
        id: contactId,
        userId,
      },
    });

    if (!contact) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Contact not found or not owned by user",
        },
      });
    }

    // Generate a unique room name for this call
    const roomName = `whisper_${userId}_${contactId}_${Date.now()}`;
    const participantIdentity = `user_${userId}_${Date.now()}`;

    // Create LiveKit token
    const API_KEY = config.liveKit.apiKey;
    const API_SECRET = config.liveKit.apiSecret;
    const LIVEKIT_URL = config.liveKit.url;

    const at = new AccessToken(API_KEY, API_SECRET, {
      identity: participantIdentity,
      ttl: "60m", // 1 hour token
      metadata: JSON.stringify({
        userId,
        contactId,
        contactName: contact.name,
        contactType: contact.type,
        timestamp: new Date().toISOString(),
      }),
    });

    // Add permissions to the token
    const grant = {
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canPublishData: true,
      canSubscribe: true,
    };
    at.addGrant(grant);

    // Generate the JWT token
    const participantToken = await at.toJwt();

    // Find or create a default assistant for whisper calls
    let defaultAssistant = await prisma.assistant.findFirst({
      where: {
        userId,
        name: "Whisper Assistant",
      },
    });

    if (!defaultAssistant) {
      // Create a default assistant for this user
      defaultAssistant = await prisma.assistant.create({
        data: {
          userId,
          name: "Whisper Assistant",
          modes: ["voice"],
          firstMessage: "Hello, I'm your Whisper Assistant.",
          systemPrompt:
            "You are a helpful assistant that provides whisper advice during calls.",
          provider: "openai",
          model: "gpt-4",
          tools: [],
          voice: {
            provider: "elevenlabs",
            voiceId: "default",
            settings: {
              speed: 1.0,
              pitch: 1.0,
              stability: 0.5,
              volume: 1.0,
            },
          },
          isActive: true,
        },
      });
    }

    // Create a call record in the database
    const call = await prisma.call.create({
      data: {
        userId,
        contactId,
        assistantId: defaultAssistant.id, // Use the default assistant ID
        startTime: new Date(),
        status: "IN_PROGRESS",
        transcript: [],
        goals: [],
        metrics: {
          averageSentiment: 0,
          sentimentTimeline: [],
          whisperEffectiveness: 0,
          goalCompletion: 0,
        },
      },
    });

    res.json({
      success: true,
      data: {
        serverUrl: LIVEKIT_URL,
        roomName,
        participantToken,
        participantName: participantIdentity,
        callId: call.id,
      },
    });
  } catch (error) {
    console.error("Error generating whisper connection details:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to generate connection details",
      },
    });
  }
};

/**
 * End a whisper call
 */
export const endWhisperCall = async (req, res) => {
  try {
    const userId = req.user.id;
    const { callId } = req.params;

    if (!callId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Call ID is required",
        },
      });
    }

    // Check if call exists and belongs to user
    const call = await prisma.call.findFirst({
      where: {
        id: callId,
        userId,
        status: "IN_PROGRESS",
      },
    });

    if (!call) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Active call not found or not owned by user",
        },
      });
    }

    // Calculate call duration
    const endTime = new Date();
    const duration = Math.floor(
      (endTime.getTime() - call.startTime.getTime()) / 1000
    ); // Duration in seconds

    // Update call record
    const updatedCall = await prisma.call.update({
      where: { id: callId },
      data: {
        endTime,
        duration,
        status: "COMPLETED",
      },
    });

    res.json({
      success: true,
      data: {
        callId: updatedCall.id,
        duration: updatedCall.duration,
        status: updatedCall.status,
      },
    });
  } catch (error) {
    console.error("Error ending whisper call:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to end call",
      },
    });
  }
};

/**
 * Get contact goals for whisper
 */
export const getContactGoals = async (req, res) => {
  try {
    const userId = req.user.id;
    const { contactId } = req.params;

    if (!contactId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Contact ID is required",
        },
      });
    }

    // Check if contact exists and belongs to user
    const contact = await prisma.contact.findFirst({
      where: {
        id: contactId,
        userId,
      },
    });

    if (!contact) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Contact not found or not owned by user",
        },
      });
    }

    // Get goals for this contact
    // For now, we'll return mock goals since the actual goals implementation is not yet in place
    const mockGoals = [
      {
        id: "goal1",
        title: "Upsell Premium Package",
        aiPrompt:
          "Suggest premium features when the customer shows interest in basic services",
        completed: false,
        progress: 0,
      },
      {
        id: "goal2",
        title: "Address Customer Concerns",
        aiPrompt:
          "Identify and address any concerns the customer raises about the service",
        completed: false,
        progress: 0,
      },
    ];

    res.json({
      success: true,
      data: mockGoals,
    });
  } catch (error) {
    console.error("Error fetching contact goals:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to fetch contact goals",
      },
    });
  }
};

/**
 * Update call transcript and goals progress
 */
export const updateCallTranscript = async (req, res) => {
  try {
    const userId = req.user.id;
    const { callId } = req.params;
    const { transcript, goals } = req.body;

    if (!callId) {
      return res.status(400).json({
        success: false,
        error: {
          code: "INVALID_INPUT",
          message: "Call ID is required",
        },
      });
    }

    // Check if call exists and belongs to user
    const call = await prisma.call.findFirst({
      where: {
        id: callId,
        userId,
      },
    });

    if (!call) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Call not found or not owned by user",
        },
      });
    }

    // Update call record with new transcript entries and goals
    const updatedCall = await prisma.call.update({
      where: { id: callId },
      data: {
        transcript: transcript || call.transcript,
        goals: goals || call.goals,
      },
    });

    res.json({
      success: true,
      data: {
        callId: updatedCall.id,
        transcript: updatedCall.transcript,
        goals: updatedCall.goals,
      },
    });
  } catch (error) {
    console.error("Error updating call transcript:", error);
    res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: "Failed to update call transcript",
      },
    });
  }
};
