import { prisma } from "../../../lib/prisma.js";
import config from "../../../config/config.js";
import axios from "axios";

/**
 * Service for handling Whisper AI processing
 */
class WhisperService {
  /**
   * Process a transcript entry and generate AI advice based on goals
   * @param {string} text - The transcript text to process
   * @param {Array} goals - The goals to check against
   * @returns {Promise<Object>} - The AI advice and updated goals
   */
  async processTranscriptForAdvice(text, goals) {
    try {
      // Extract keywords from the text
      const keywords = this.extractKeywords(text);
      
      // Find relevant goals based on keywords
      const relevantGoals = goals.filter(goal => {
        const goalKeywords = this.extractKeywords(goal.aiPrompt);
        return goalKeywords.some(keyword => keywords.includes(keyword));
      });
      
      if (relevantGoals.length === 0) {
        return {
          advice: null,
          updatedGoals: goals
        };
      }
      
      // Generate advice using AI
      const advice = await this.generateAIAdvice(text, relevantGoals[0]);
      
      // Update goal progress
      const updatedGoals = goals.map(goal => {
        if (relevantGoals.some(rg => rg.id === goal.id)) {
          return {
            ...goal,
            progress: Math.min(goal.progress + 0.25, 1),
            completed: goal.progress + 0.25 >= 1
          };
        }
        return goal;
      });
      
      return {
        advice,
        updatedGoals
      };
    } catch (error) {
      console.error("Error processing transcript for advice:", error);
      return {
        advice: null,
        updatedGoals: goals
      };
    }
  }
  
  /**
   * Extract keywords from text
   * @param {string} text - The text to extract keywords from
   * @returns {Array<string>} - Array of keywords
   */
  extractKeywords(text) {
    // Remove common words and punctuation
    const cleanText = text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, ' ')
      .trim();
    
    // Split into words
    const words = cleanText.split(' ');
    
    // Filter out common words (stop words)
    const stopWords = ['the', 'and', 'a', 'an', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were'];
    const keywords = words.filter(word => 
      word.length > 2 && !stopWords.includes(word)
    );
    
    // Return unique keywords
    return [...new Set(keywords)];
  }
  
  /**
   * Generate AI advice based on text and goal
   * @param {string} text - The transcript text
   * @param {Object} goal - The relevant goal
   * @returns {Promise<Object>} - The AI advice
   */
  async generateAIAdvice(text, goal) {
    try {
      // In a production environment, this would call an AI service like OpenAI
      // For now, we'll simulate a response
      
      // If you want to integrate with OpenAI, uncomment this code:
      /*
      const response = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: 'gpt-4',
          messages: [
            {
              role: 'system',
              content: `You are an AI assistant that provides advice during calls. 
                        The user is on a call with someone and needs subtle advice.
                        Your goal is: ${goal.title}. 
                        Specific instructions: ${goal.aiPrompt}.
                        Keep your response very brief and actionable.`
            },
            {
              role: 'user',
              content: `The other person just said: "${text}". What should I say or ask next?`
            }
          ],
          max_tokens: 150,
          temperature: 0.7
        },
        {
          headers: {
            'Authorization': `Bearer ${config.openai.apiKey}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      return {
        type: 'ai',
        message: response.data.choices[0].message.content,
        timestamp: new Date().toISOString(),
        goalId: goal.id
      };
      */
      
      // Simulated response
      const responses = {
        "Upsell Premium Package": [
          "They seem interested in basic features. Mention how the premium package includes 24/7 support.",
          "This is a good time to highlight the advanced analytics in our premium tier.",
          "They're asking about limitations. Explain how the premium package removes these caps."
        ],
        "Address Customer Concerns": [
          "They sound hesitant about the pricing. Emphasize the value and ROI.",
          "Acknowledge their concern about implementation time and mention our onboarding support.",
          "They're worried about compatibility. Explain our integration capabilities."
        ]
      };
      
      const goalResponses = responses[goal.title] || responses["Address Customer Concerns"];
      const randomIndex = Math.floor(Math.random() * goalResponses.length);
      
      return {
        type: 'ai',
        message: goalResponses[randomIndex],
        timestamp: new Date().toISOString(),
        goalId: goal.id
      };
    } catch (error) {
      console.error("Error generating AI advice:", error);
      return {
        type: 'ai',
        message: "I couldn't generate specific advice at this moment.",
        timestamp: new Date().toISOString()
      };
    }
  }
  
  /**
   * Save a call transcript entry
   * @param {string} callId - The call ID
   * @param {Object} entry - The transcript entry
   * @returns {Promise<Object>} - The updated call
   */
  async saveTranscriptEntry(callId, entry) {
    try {
      const call = await prisma.call.findUnique({
        where: { id: callId }
      });
      
      if (!call) {
        throw new Error("Call not found");
      }
      
      const updatedTranscript = [...call.transcript, entry];
      
      const updatedCall = await prisma.call.update({
        where: { id: callId },
        data: {
          transcript: updatedTranscript
        }
      });
      
      return updatedCall;
    } catch (error) {
      console.error("Error saving transcript entry:", error);
      throw error;
    }
  }
  
  /**
   * Update goals for a call
   * @param {string} callId - The call ID
   * @param {Array} goals - The updated goals
   * @returns {Promise<Object>} - The updated call
   */
  async updateGoals(callId, goals) {
    try {
      const updatedCall = await prisma.call.update({
        where: { id: callId },
        data: {
          goals
        }
      });
      
      return updatedCall;
    } catch (error) {
      console.error("Error updating goals:", error);
      throw error;
    }
  }
}

export default new WhisperService();
