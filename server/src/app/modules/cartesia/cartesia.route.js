import express from "express";

import { authenticateJWT } from "../../../middleware/authMiddleware.js";
import {
  createCartesiaTextToSpeech,
  getCartesiaSingleVoice,
  getCartesiaSingleVoiceSample,
  getCartesiaVoices,
} from "./cartesia.controller.js";

const router = express.Router();

router.get("/voices", authenticateJWT, getCartesiaVoices);
router.get("/voice/:voiceId", authenticateJWT, getCartesiaSingleVoice);
router.get(
  "/voice-sample/:voiceId",
  authenticateJWT,
  getCartesiaSingleVoiceSample
);
router.post("/text-to-speech", authenticateJWT, createCartesiaTextToSpeech);

export const CartesiaRoute = router;
