// Check if we have the Cartesia API key

import config from "../../../config/config.js";

// Check if we have the Cartesia API key
const cartesiaApiKey = config.apis.cartesia.apiKey;
if (!cartesiaApiKey) {
  console.warn("CARTESIA_API_KEY environment variable is not set");
}

export const getCartesiaSingleVoiceSample = async (req, res) => {
  try {
    if (!cartesiaApiKey) {
      throw new Error("Cartesia API key is not configured");
    }

    const voiceId = req.params.voiceId;
    console.log("Fetching voice sample for:", voiceId);

    // First get the voice details
    const voiceResponse = await fetch(
      `https://api.cartesia.ai/voices/${voiceId}`,
      {
        headers: {
          "X-API-Key": cartesiaApiKey,
          Accept: "application/json",
          "Cartesia-Version": "2024-06-10",
        },
      }
    );

    console.log("Voice Response Status:", voiceResponse.status);
    if (!voiceResponse.ok) {
      const errorText = await voiceResponse.text();
      console.error("Cartesia API Error Response:", errorText);
      throw new Error(
        `Failed to fetch voice details: ${voiceResponse.status}. ${errorText}`
      );
    }

    const voice = await voiceResponse.json();
    // console.log("Voice:", voice);

    // Get the voice's audio
    const audioResponse = await fetch(
      `https://api.cartesia.ai/voices/${voiceId}/audio`,
      {
        headers: {
          "X-API-Key": cartesiaApiKey,
          Accept: "audio/mpeg",
          "Cartesia-Version": "2024-06-10",
        },
      }
    );

    console.log("Audio Response Status:", audioResponse.status);
    if (!audioResponse.ok) {
      const errorText = await audioResponse.text();
      console.error("Cartesia API Error Response:", errorText);
      throw new Error(
        `Failed to fetch voice audio: ${audioResponse.status}. ${errorText}`
      );
    }

    const audioBuffer = await audioResponse.arrayBuffer();
    res.set("Content-Type", "audio/mpeg");
    res.send(Buffer.from(audioBuffer));
  } catch (error) {
    console.error("Error fetching voice sample:", error);
    res.status(500).json({
      error: "Failed to fetch voice sample",
      details: error.message,
      voiceId: req.params.voiceId,
    });
  }
};

export const getCartesiaVoices = async (req, res) => {
  try {
    console.log("Received request for Cartesia voices");

    if (!cartesiaApiKey) {
      console.error("Cartesia API key is missing");
      throw new Error("Cartesia API key is not configured");
    }
    const response = await fetch("https://api.cartesia.ai/voices", {
      method: "GET",
      headers: {
        "X-API-Key": cartesiaApiKey,
        Accept: "application/json",
        "Cartesia-Version": "2024-06-10",
        "User-Agent": "TalkAI247/1.0",
      },
    });

    console.log("Cartesia API Response Status:", response.status);
    const responseText = await response.text();
    // console.log("Cartesia API Raw Response:", responseText);

    if (!response.ok) {
      console.error("Cartesia API Error Response:", responseText);
      throw new Error(
        `Failed to fetch voices: ${response.status}. ${responseText}`
      );
    }

    try {
      const data = JSON.parse(responseText);
      // console.log("Cartesia API Parsed Data:", JSON.stringify(data, null, 2));
      // The Cartesia API returns an array directly, not wrapped in a voices property
      res.json({ voices: Array.isArray(data) ? data : [] });
    } catch (parseError) {
      console.error("Error parsing JSON response:", parseError);
      throw new Error("Failed to parse Cartesia API response");
    }
  } catch (error) {
    console.error("Error fetching Cartesia voices:", error);
    res.status(500).json({
      error: "Failed to fetch voices",
      details: error.message,
      stack: error.stack,
    });
  }
};
export const getCartesiaSingleVoice = async (req, res) => {
  try {
    if (!cartesiaApiKey) {
      throw new Error("Cartesia API key is not configured");
    }

    const voiceId = req.params.voiceId;
    console.log("Fetching voice metadata for:", voiceId);

    // First get the voice details
    const voiceResponse = await fetch(
      `https://api.cartesia.ai/voices/${voiceId}`,
      {
        headers: {
          "X-API-Key": cartesiaApiKey,
          Accept: "application/json",
          "Cartesia-Version": "2024-06-10",
        },
      }
    );

    console.log("Voice Response Status:", voiceResponse.status);
    if (!voiceResponse.ok) {
      const errorText = await voiceResponse.text();
      console.error("Cartesia API Error Response:", errorText);
      throw new Error(
        `Failed to fetch voice details: ${voiceResponse.status}. ${errorText}`
      );
    }

    const voice = await voiceResponse.json();
    res.json({
      id: voice.id,
      name: voice.name,
      description: voice.description,
      language: voice.language,
    });
  } catch (error) {
    console.error("Error fetching voice metadata:", error);
    res.status(500).json({
      error: "Failed to fetch voice metadata",
      details: error.message,
      voiceId: req.params.voiceId,
    });
  }
};

export const createCartesiaTextToSpeech = async (req, res) => {
  try {
    const { text, voiceId } = req.body;

    if (!text || !voiceId) {
      return res
        .status(400)
        .json({ error: "Missing required parameters: text and voiceId" });
    }

    console.log("Generating Cartesia speech:", { text, voiceId });

    const response = await fetch("https://api.cartesia.ai/tts/bytes", {
      method: "POST",
      headers: {
        "X-API-Key": cartesiaApiKey,
        "Content-Type": "application/json",
        "Cartesia-Version": "2024-06-10",
      },
      body: JSON.stringify({
        modelId: "sonic-english",
        transcript: text,
        voice: {
          mode: "id",
          id: voiceId,
        },
        outputFormat: {
          container: "mp3",
          sampleRate: 44100,
          encoding: "mp3",
        },
      }),
    });

    console.log("Cartesia TTS Response Status:", response.status);
    if (!response.ok) {
      const errorText = await response.text();
      console.error("Cartesia API Error Response:", errorText);
      throw new Error(`Cartesia API error: ${response.status}. ${errorText}`);
    }

    const audioBuffer = await response.arrayBuffer();
    res.set("Content-Type", "audio/mpeg");
    res.send(Buffer.from(audioBuffer));
  } catch (error) {
    console.error("Error generating Cartesia speech:", error);
    res.status(500).json({
      error: "Failed to generate Cartesia speech",
      details: error.message,
      apiKey: cartesiaApiKey ? "present" : "missing",
    });
  }
};
