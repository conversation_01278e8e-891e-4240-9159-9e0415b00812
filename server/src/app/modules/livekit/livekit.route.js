import express from "express";

import { authenticateJWT } from "../../../middleware/authMiddleware.js";
import {
  getConnectionDetails,
  getWhisperConnectionDetails,
  testLiveKitWebSocket,
} from "./livekit.controller.js";

const router = express.Router();

router.get("/connection-details", authenticateJWT, getConnectionDetails);
router.get(
  "/whisper-connection-details",
  authenticateJWT,
  getWhisperConnectionDetails
);
// Test endpoint for LiveKit WebSocket connectivity
router.get("/test-websocket", authenticateJWT, testLiveKitWebSocket);

export const LivekitRoute = router;
