import { AccessToken } from "livekit-server-sdk";
import { prisma } from "../../../lib/prisma.js";
import config from "../../../config/config.js";
import WebSocket from "ws";
import livekitService from "../../../services/livekitService.js";

export const getConnectionDetails = async (req, res) => {
  try {
    const API_KEY = config.liveKit.apiKey;
    const API_SECRET = config.liveKit.apiSecret;
    const LIVEKIT_URL = config.liveKit.url;
    // Extract dynamic initial message and initial context text from query parameters
    const roomName = req.query.roomName;
    console.log("roomName", roomName);
    const participantIdentity = `voice_assistant_user_${Math.floor(
      Math.random() * 10_000
    )}`;
    // const roomName = `voice_assistant_room_${Math.floor(
    //   Math.random() * 10_000
    // )}`;

    const assistant = await prisma.assistant.findUnique({
      where: { id: roomName },
      include: {
        user: true,
      },
    });
    if (!assistant) {
      return res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: "Assistant not found",
        },
      });
    }
    // console.log("assistant", assistant);
    console.log("assistantsasd", assistant.tools[1].config);
    const at = new AccessToken(API_KEY, API_SECRET, {
      identity: participantIdentity,
      ttl: "15m",
      attributes: {
        id: assistant.id,
        name: assistant.name,
        firstMessage: assistant.firstMessage,
        systemPrompt: assistant.systemPrompt,
        provider: assistant.provider,
        model: assistant.model,
        modes: assistant.modes[1],
        userCalId: assistant.user.calApiKey,
        // tools: assistant.tools,
        // voice: assistant.voice,
        scrapingUrl: assistant?.tools?.find((tool) => tool.type === "scraping")
          ?.config.url,
        userId: assistant.userId,
        calEventTypeId: assistant?.user?.calEventTypeId?.toString(),
        isPremium: assistant?.user?.isPremium?.toString(),
      },
    });
    const grant = {
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canPublishData: true,
      canSubscribe: true,
    };
    at.addGrant(grant);
    const participantToken = await at.toJwt();
    res.json({
      serverUrl: LIVEKIT_URL,
      roomName,
      participantToken,
      participantName: participantIdentity,
      metadata: "How do chatgpt works",
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: err.message });
  }
};

export const getWhisperConnectionDetails = async (req, res) => {
  try {
    const API_KEY = config.liveKit.apiKey;
    const API_SECRET = config.liveKit.apiSecret;
    const LIVEKIT_URL = config.liveKit.url;
    // Extract dynamic initial message and initial context text from query parameters
    const roomName = req.query.roomName;
    console.log("roomName", roomName);
    const participantIdentity = `voice_assistant_user_${Math.floor(
      Math.random() * 10_000
    )}`;
    // const roomName = `voice_assistant_room_${Math.floor(
    //   Math.random() * 10_000
    // )}`;

    // console.log("assistant", assistant);
    const at = new AccessToken(API_KEY, API_SECRET, {
      identity: participantIdentity,
      ttl: "15m",
    });
    const grant = {
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canPublishData: true,
      canSubscribe: true,
    };
    at.addGrant(grant);
    const participantToken = await at.toJwt();
    res.json({
      serverUrl: LIVEKIT_URL,
      roomName,
      participantToken,
      participantName: participantIdentity,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: err.message });
  }
};

/**
 * Test LiveKit WebSocket connectivity
 * This endpoint tests if the LiveKit server is accessible via WebSocket
 */
export const testLiveKitWebSocket = async (req, res) => {
  try {
    const liveKitUrl = config.liveKit.url;

    if (!liveKitUrl) {
      return res.status(400).json({
        success: false,
        error: {
          code: "CONFIGURATION_ERROR",
          message: "LiveKit URL is not configured",
        },
      });
    }

    // Log the LiveKit URL
    console.log(`Testing WebSocket connection to LiveKit URL: ${liveKitUrl}`);

    // Create a WebSocket connection to the LiveKit server
    const ws = new WebSocket(liveKitUrl);

    // Set a timeout for the connection attempt
    const timeout = setTimeout(() => {
      ws.terminate();
      return res.status(408).json({
        success: false,
        error: {
          code: "TIMEOUT",
          message: "Connection attempt timed out after 5 seconds",
        },
      });
    }, 5000);

    // Handle WebSocket events
    ws.on("open", () => {
      clearTimeout(timeout);
      ws.close();

      return res.json({
        success: true,
        message: "Successfully connected to LiveKit WebSocket server",
        url: liveKitUrl,
      });
    });

    ws.on("error", (error) => {
      clearTimeout(timeout);
      console.error("WebSocket connection error:", error);

      return res.status(500).json({
        success: false,
        error: {
          code: "CONNECTION_ERROR",
          message: `Failed to connect to LiveKit WebSocket server: ${error.message}`,
        },
      });
    });
  } catch (error) {
    console.error("Error testing LiveKit WebSocket:", error);

    return res.status(500).json({
      success: false,
      error: {
        code: "SERVER_ERROR",
        message: `Server error: ${error.message}`,
      },
    });
  }
};
