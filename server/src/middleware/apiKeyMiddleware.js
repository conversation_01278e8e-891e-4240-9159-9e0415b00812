import { PrismaClient } from "@prisma/client";
const prisma = new PrismaClient();

/**
 * Middleware to authenticate requests using API key
 * This allows external websites to access specific endpoints
 * without requiring JWT authentication
 */
export const authenticateApiKey = async (req, res, next) => {
  const apiKey = req.headers["x-api-key"];
  // console.log("apiKey", apiKey);
  if (!apiKey) {
    return res.status(401).json({
      success: false,
      error: {
        code: "API_KEY_MISSING",
        message: "API key is required",
      },
    });
  }

  try {
    // Find the user with the provided public API key
    const user = await prisma.user.findFirst({
      where: { publicApiKey: apiKey },
      select: {
        id: true,
        email: true,
        role: true,
        isPremium: true,
      },
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        error: {
          code: "INVALID_API_KEY",
          message: "Invalid API key",
        },
      });
    }

    // Attach the user to the request
    req.user = user;
    next();
  } catch (err) {
    console.error("API key authentication error:", err);
    return res.status(500).json({
      success: false,
      error: {
        code: "AUTH_ERROR",
        message: "Authentication error",
      },
    });
  }
};
