import { PrismaClient } from "@prisma/client";
import jwt from "jsonwebtoken";
import config from "../config/config.js";
const prisma = new PrismaClient();

const JWT_SECRET = config.jwt.secret;

export const authenticateJWT = async (req, res, next) => {
  const authHeader = req.headers.authorization;

  if (authHeader) {
    const token = authHeader.split(" ")[1];

    try {
      const decoded = jwt.verify(token, JWT_SECRET);

      // Fetch the user from database to ensure they still exist
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          email: true,
          role: true,
        },
      });

      if (!user) {
        return res.status(401).json({ error: "User not found" });
      }

      // Attach the full user object to the request
      req.user = user;
      next();
    } catch (err) {
      console.error("JWT verification error:", err);

      // Return different status codes based on the error type
      if (err instanceof jwt.TokenExpiredError) {
        return res.status(401).json({
          error: "Token expired",
          code: "TOKEN_EXPIRED",
        });
      } else if (err instanceof jwt.JsonWebTokenError) {
        return res.status(403).json({
          error: "Invalid token",
          code: "INVALID_TOKEN",
        });
      }

      return res.status(403).json({
        error: "Invalid or expired token",
        code: "AUTH_ERROR",
      });
    }
  } else {
    res.status(401).json({ error: "Authorization header missing" });
  }
};
