import { prisma } from "../lib/prisma.js";

/**
 * Service for managing whisper sessions and analytics
 * Handles tracking of whisper interactions and session data
 */
class WhisperSessionService {
  /**
   * Create a new whisper session
   */
  async createSession(sessionData) {
    try {
      const {
        callId,
        userId,
        contactId,
        livekitRoomName,
        whisperMode,
        participantData,
      } = sessionData;

      const session = await prisma.whisperSession.create({
        data: {
          callId,
          userId,
          contactId,
          livekitRoomName,
          sessionStartTime: new Date(),
          whisperMode,
          participantData,
          goalProgress: [],
          audioMetrics: {},
        },
        include: {
          call: true,
          user: true,
          contact: true,
        },
      });

      console.log(`Whisper session created: ${session.id}`);
      return session;
    } catch (error) {
      console.error("Error creating whisper session:", error);
      throw error;
    }
  }

  /**
   * End a whisper session
   */
  async endSession(sessionId) {
    try {
      const session = await prisma.whisperSession.update({
        where: { id: sessionId },
        data: {
          sessionEndTime: new Date(),
          totalDuration: await this._calculateSessionDuration(sessionId),
        },
        include: {
          interactions: true,
        },
      });

      console.log(`Whisper session ended: ${sessionId}`);
      return session;
    } catch (error) {
      console.error("Error ending whisper session:", error);
      throw error;
    }
  }

  /**
   * Log a whisper interaction
   */
  async logInteraction(interactionData) {
    try {
      const {
        sessionId,
        interactionType,
        userSpeech,
        callerSpeech,
        aiResponse,
        triggerGoals,
        responseTime,
        confidence,
        metadata,
      } = interactionData;

      const interaction = await prisma.whisperInteraction.create({
        data: {
          sessionId,
          interactionType,
          userSpeech,
          callerSpeech,
          aiResponse,
          triggerGoals: triggerGoals || [],
          responseTime,
          confidence,
          timestamp: new Date(),
          metadata: metadata || {},
        },
      });

      console.log(`Whisper interaction logged: ${interaction.id}`);
      return interaction;
    } catch (error) {
      console.error("Error logging whisper interaction:", error);
      throw error;
    }
  }

  /**
   * Update session mode
   */
  async updateSessionMode(sessionId, newMode) {
    try {
      const session = await prisma.whisperSession.update({
        where: { id: sessionId },
        data: { whisperMode: newMode },
      });

      // Log the mode change as an interaction
      await this.logInteraction({
        sessionId,
        interactionType: "MODE_CHANGE",
        metadata: {
          previousMode: session.whisperMode,
          newMode,
          timestamp: new Date().toISOString(),
        },
      });

      return session;
    } catch (error) {
      console.error("Error updating session mode:", error);
      throw error;
    }
  }

  /**
   * Update goal progress for a session
   */
  async updateGoalProgress(sessionId, goalProgress) {
    try {
      const session = await prisma.whisperSession.update({
        where: { id: sessionId },
        data: { goalProgress },
      });

      return session;
    } catch (error) {
      console.error("Error updating goal progress:", error);
      throw error;
    }
  }

  /**
   * Get session analytics
   */
  async getSessionAnalytics(sessionId) {
    try {
      const session = await prisma.whisperSession.findUnique({
        where: { id: sessionId },
        include: {
          interactions: {
            orderBy: { timestamp: "asc" },
          },
          call: true,
          contact: true,
        },
      });

      if (!session) {
        throw new Error("Session not found");
      }

      const analytics = {
        session: {
          id: session.id,
          duration: session.totalDuration,
          mode: session.whisperMode,
          startTime: session.sessionStartTime,
          endTime: session.sessionEndTime,
        },
        interactions: {
          total: session.interactions.length,
          byType: this._groupInteractionsByType(session.interactions),
          averageResponseTime: this._calculateAverageResponseTime(session.interactions),
          confidenceDistribution: this._calculateConfidenceDistribution(session.interactions),
        },
        goals: {
          progress: session.goalProgress,
          completion: this._calculateGoalCompletion(session.goalProgress),
        },
        contact: {
          name: session.contact.name,
          type: session.contact.type,
        },
      };

      return analytics;
    } catch (error) {
      console.error("Error getting session analytics:", error);
      throw error;
    }
  }

  /**
   * Get user's whisper session history
   */
  async getUserSessionHistory(userId, limit = 10) {
    try {
      const sessions = await prisma.whisperSession.findMany({
        where: { userId },
        orderBy: { sessionStartTime: "desc" },
        take: limit,
        include: {
          contact: true,
          call: true,
          _count: {
            select: { interactions: true },
          },
        },
      });

      return sessions.map(session => ({
        id: session.id,
        contactName: session.contact.name,
        contactType: session.contact.type,
        duration: session.totalDuration,
        mode: session.whisperMode,
        interactionCount: session._count.interactions,
        startTime: session.sessionStartTime,
        endTime: session.sessionEndTime,
        callStatus: session.call.status,
      }));
    } catch (error) {
      console.error("Error getting user session history:", error);
      throw error;
    }
  }

  /**
   * Get whisper effectiveness metrics
   */
  async getEffectivenessMetrics(userId, timeRange = 30) {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - timeRange);

      const sessions = await prisma.whisperSession.findMany({
        where: {
          userId,
          sessionStartTime: { gte: startDate },
        },
        include: {
          interactions: true,
          call: true,
        },
      });

      const metrics = {
        totalSessions: sessions.length,
        totalInteractions: sessions.reduce((sum, s) => sum + s.interactions.length, 0),
        averageSessionDuration: this._calculateAverageSessionDuration(sessions),
        modeUsage: this._calculateModeUsage(sessions),
        successfulCalls: sessions.filter(s => s.call.status === "COMPLETED").length,
        averageResponseTime: this._calculateOverallAverageResponseTime(sessions),
      };

      return metrics;
    } catch (error) {
      console.error("Error getting effectiveness metrics:", error);
      throw error;
    }
  }

  /**
   * Helper method to calculate session duration
   */
  async _calculateSessionDuration(sessionId) {
    const session = await prisma.whisperSession.findUnique({
      where: { id: sessionId },
    });

    if (!session || !session.sessionEndTime) {
      return null;
    }

    const duration = Math.floor(
      (session.sessionEndTime - session.sessionStartTime) / 1000
    );
    return duration;
  }

  /**
   * Helper method to group interactions by type
   */
  _groupInteractionsByType(interactions) {
    return interactions.reduce((acc, interaction) => {
      acc[interaction.interactionType] = (acc[interaction.interactionType] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * Helper method to calculate average response time
   */
  _calculateAverageResponseTime(interactions) {
    const responseTimes = interactions
      .filter(i => i.responseTime)
      .map(i => i.responseTime);

    if (responseTimes.length === 0) return null;

    return responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
  }

  /**
   * Helper method to calculate confidence distribution
   */
  _calculateConfidenceDistribution(interactions) {
    const confidences = interactions
      .filter(i => i.confidence !== null)
      .map(i => i.confidence);

    if (confidences.length === 0) return null;

    return {
      average: confidences.reduce((sum, conf) => sum + conf, 0) / confidences.length,
      high: confidences.filter(c => c >= 0.8).length,
      medium: confidences.filter(c => c >= 0.5 && c < 0.8).length,
      low: confidences.filter(c => c < 0.5).length,
    };
  }

  /**
   * Helper method to calculate goal completion
   */
  _calculateGoalCompletion(goalProgress) {
    if (!goalProgress || goalProgress.length === 0) return 0;

    const completedGoals = goalProgress.filter(goal => goal.completed).length;
    return (completedGoals / goalProgress.length) * 100;
  }

  /**
   * Helper method to calculate average session duration
   */
  _calculateAverageSessionDuration(sessions) {
    const durations = sessions
      .filter(s => s.totalDuration)
      .map(s => s.totalDuration);

    if (durations.length === 0) return null;

    return durations.reduce((sum, duration) => sum + duration, 0) / durations.length;
  }

  /**
   * Helper method to calculate mode usage
   */
  _calculateModeUsage(sessions) {
    return sessions.reduce((acc, session) => {
      acc[session.whisperMode] = (acc[session.whisperMode] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * Helper method to calculate overall average response time
   */
  _calculateOverallAverageResponseTime(sessions) {
    const allResponseTimes = sessions
      .flatMap(s => s.interactions)
      .filter(i => i.responseTime)
      .map(i => i.responseTime);

    if (allResponseTimes.length === 0) return null;

    return allResponseTimes.reduce((sum, time) => sum + time, 0) / allResponseTimes.length;
  }
}

export default new WhisperSessionService();
