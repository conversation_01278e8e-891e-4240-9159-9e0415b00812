import { RoomServiceClient } from 'livekit-server-sdk';
import config from '../config/config.js';

/**
 * Service for managing LiveKit SIP integration
 * Implements the architecture described in livekit.md Section 1.3
 */
class LiveKitSipService {
  constructor() {
    if (!config.liveKit.url || !config.liveKit.apiKey || !config.liveKit.apiSecret) {
      console.warn('LiveKit credentials not configured. SIP functionality will be disabled.');
      this.isConfigured = false;
      return;
    }

    this.roomService = new RoomServiceClient(
      config.liveKit.url,
      config.liveKit.apiKey,
      config.liveKit.apiSecret
    );
    this.isConfigured = true;
    console.log('LiveKit SIP service initialized successfully');
  }

  /**
   * Create LiveKit inbound SIP trunk configuration
   * This represents the Twilio trunk within LiveKit
   */
  async createInboundTrunk() {
    if (!this.isConfigured) {
      throw new Error('LiveKit is not configured');
    }

    try {
      // Configuration for inbound trunk
      const inboundTrunkConfig = {
        name: 'twilio-whisper-inbound',
        metadata: {
          description: 'Twilio SIP trunk for Whisper feature inbound calls',
          provider: 'twilio',
          created: new Date().toISOString(),
        },
        // Allow calls from Twilio phone number
        numbers: config.twilio.phoneNumber ? [config.twilio.phoneNumber] : [],
      };

      console.log('Creating LiveKit inbound SIP trunk:', inboundTrunkConfig);
      
      // Note: This is a conceptual implementation
      // The actual LiveKit SIP API may differ based on your LiveKit version
      // You may need to use the LiveKit CLI or API directly
      
      return inboundTrunkConfig;
    } catch (error) {
      console.error('Error creating LiveKit inbound trunk:', error);
      throw error;
    }
  }

  /**
   * Create LiveKit outbound SIP trunk configuration
   * For making outbound calls through Twilio
   */
  async createOutboundTrunk() {
    if (!this.isConfigured) {
      throw new Error('LiveKit is not configured');
    }

    try {
      const sipConfig = config.twilio.sipTrunk;
      
      // Configuration for outbound trunk
      const outboundTrunkConfig = {
        name: 'twilio-whisper-outbound',
        metadata: {
          description: 'Twilio SIP trunk for Whisper feature outbound calls',
          provider: 'twilio',
          created: new Date().toISOString(),
        },
        // Twilio termination URI
        address: sipConfig.domainName,
        // Authentication credentials
        auth: {
          username: sipConfig.username,
          password: sipConfig.password,
        },
        // From number for outbound calls
        fromNumber: config.twilio.phoneNumber,
      };

      console.log('Creating LiveKit outbound SIP trunk:', outboundTrunkConfig);
      
      return outboundTrunkConfig;
    } catch (error) {
      console.error('Error creating LiveKit outbound trunk:', error);
      throw error;
    }
  }

  /**
   * Create dispatch rule for automatic room creation
   * Implements dispatchRuleIndividual for unique rooms per call
   */
  async createDispatchRule() {
    if (!this.isConfigured) {
      throw new Error('LiveKit is not configured');
    }

    try {
      // Configuration for dispatch rule
      const dispatchRuleConfig = {
        name: 'whisper-individual-rooms',
        type: 'individual', // Creates unique room for each call
        metadata: {
          description: 'Dispatch rule for Whisper feature - individual rooms per call',
          roomPrefix: 'whisper-call-',
          created: new Date().toISOString(),
        },
        // Room configuration
        roomConfig: {
          // Automatically terminate room after 10 minutes of inactivity
          emptyTimeout: 600, // 10 minutes in seconds
          // Maximum room duration (2 hours)
          maxParticipants: 3, // User, Caller, AI Assistant
          // Enable recording if needed
          enableRecording: false,
        },
        // Participant configuration
        participantConfig: {
          // Default permissions for SIP participants (Caller)
          defaultGrants: {
            roomJoin: true,
            canPublish: true,
            canSubscribe: true,
            canPublishData: false, // Disable data publishing for external callers
            hidden: false,
          },
        },
      };

      console.log('Creating LiveKit dispatch rule:', dispatchRuleConfig);
      
      return dispatchRuleConfig;
    } catch (error) {
      console.error('Error creating LiveKit dispatch rule:', error);
      throw error;
    }
  }

  /**
   * Generate room name for whisper calls
   * Following the pattern: whisper-call-{timestamp}-{random}
   */
  generateWhisperRoomName() {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `whisper-call-${timestamp}-${random}`;
  }

  /**
   * Create a whisper room programmatically
   * Used when initiating calls from the application
   */
  async createWhisperRoom(contactId, userId) {
    if (!this.isConfigured) {
      throw new Error('LiveKit is not configured');
    }

    try {
      const roomName = this.generateWhisperRoomName();
      
      const roomOptions = {
        name: roomName,
        // Automatically clean up after 10 minutes of inactivity
        emptyTimeout: 600,
        // Maximum duration of 2 hours
        maxParticipants: 3,
        metadata: JSON.stringify({
          type: 'whisper',
          contactId,
          userId,
          created: new Date().toISOString(),
        }),
      };

      console.log(`Creating whisper room: ${roomName}`);
      const room = await this.roomService.createRoom(roomOptions);
      
      console.log(`Whisper room created successfully: ${room.name}`);
      return room;
    } catch (error) {
      console.error('Error creating whisper room:', error);
      throw error;
    }
  }

  /**
   * Delete a whisper room
   */
  async deleteWhisperRoom(roomName) {
    if (!this.isConfigured) {
      throw new Error('LiveKit is not configured');
    }

    try {
      console.log(`Deleting whisper room: ${roomName}`);
      await this.roomService.deleteRoom(roomName);
      console.log(`Whisper room deleted successfully: ${roomName}`);
      return true;
    } catch (error) {
      console.error('Error deleting whisper room:', error);
      throw error;
    }
  }

  /**
   * List active whisper rooms
   */
  async listWhisperRooms() {
    if (!this.isConfigured) {
      throw new Error('LiveKit is not configured');
    }

    try {
      const rooms = await this.roomService.listRooms();
      
      // Filter for whisper rooms
      const whisperRooms = rooms.filter(room => 
        room.name.startsWith('whisper-call-') || 
        (room.metadata && JSON.parse(room.metadata).type === 'whisper')
      );
      
      return whisperRooms;
    } catch (error) {
      console.error('Error listing whisper rooms:', error);
      throw error;
    }
  }

  /**
   * Get room details including participants
   */
  async getWhisperRoomDetails(roomName) {
    if (!this.isConfigured) {
      throw new Error('LiveKit is not configured');
    }

    try {
      const participants = await this.roomService.listParticipants(roomName);
      const room = await this.roomService.listRooms([roomName]);
      
      return {
        room: room[0],
        participants,
      };
    } catch (error) {
      console.error('Error getting whisper room details:', error);
      throw error;
    }
  }
}

export default new LiveKitSipService();
