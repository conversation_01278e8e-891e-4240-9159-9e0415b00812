import twilio from "twilio";
import config from "../config/config.js";
import { AccessToken } from "livekit-server-sdk";

// Import Twilio ClientCapability correctly
const ClientCapability = twilio.jwt.ClientCapability;

/**
 * Service for handling Twilio phone calls and SMS
 */
class TwilioService {
  validateRequest(signature, url, params) {
    if (!this.isConfigured) return false;

    try {
      return twilio.validateRequest(
        config.twilio.authToken,
        signature,
        url,
        params
      );
    } catch (error) {
      console.error("Error validating Twilio request:", error);
      return false;
    }
  }
  constructor() {
    // Check if Twilio credentials are configured
    if (
      !config.twilio?.accountSid ||
      !config.twilio?.authToken ||
      !config.twilio?.phoneNumber
    ) {
      console.warn(
        "Twilio credentials not fully configured. Call functionality will be limited."
      );
      console.warn(
        `AccountSid: ${config.twilio?.accountSid ? "Set" : "Not set"}`
      );
      console.warn(
        `AuthToken: ${config.twilio?.authToken ? "Set" : "Not set"}`
      );
      console.warn(
        `PhoneNumber: ${config.twilio?.phoneNumber ? "Set" : "Not set"}`
      );
      this.isConfigured = false;
      return;
    }

    try {
      // Log the first few characters of credentials for debugging
      console.log(
        `Using Twilio Account SID: ${config.twilio.accountSid.substring(
          0,
          10
        )}...`
      );
      console.log(
        `Using Twilio Auth Token: ${config.twilio.authToken.substring(0, 5)}...`
      );
      console.log(`Using Twilio Phone Number: ${config.twilio.phoneNumber}`);

      this.client = twilio(config.twilio.accountSid, config.twilio.authToken, {
        logLevel: "debug",
      });

      // Test the client with a simple API call
      this.client.api
        .accounts(config.twilio.accountSid)
        .fetch()
        .then((account) => {
          console.log(
            `Successfully authenticated with Twilio! Account status: ${account.status}`
          );
          console.log(`Account friendly name: ${account.friendlyName}`);
        })
        .catch((err) => {
          console.error("Failed to authenticate with Twilio API:", err);
          console.error(`Error code: ${err.code}, status: ${err.status}`);
          console.error(
            "Please check your Twilio credentials and account status"
          );
          this.isConfigured = false;
        });

      this.phoneNumber = config.twilio.phoneNumber;
      this.isConfigured = true;
      console.log("Twilio service initialized successfully");
    } catch (error) {
      console.error("Error initializing Twilio client:", error);
      this.isConfigured = false;
    }
  }

  /**
   * Format phone number to E.164 format
   * @param {string} phoneNumber - Phone number to format
   * @returns {string} - Formatted phone number
   */
  formatPhoneNumber(phoneNumber) {
    // Remove all non-digit characters
    let digits = phoneNumber.replace(/\D/g, "");

    // If the number doesn't start with a country code, add +1 (US)
    if (digits.length === 10) {
      digits = "1" + digits;
    }

    // Add the + prefix
    return "+" + digits;
  }

  /**
   * Make an outbound call
   * @param {string} to - Recipient phone number
   * @param {string} callbackUrl - URL for Twilio to request TwiML instructions
   * @param {string | null} fromPhoneNumber - Optional phone number to use as caller ID. Defaults to service's configured number.
   * @returns {Promise<object>} - Call details
   */
  async makeOutboundCall(to, callbackUrl, fromPhoneNumber = null) {
    if (!this.isConfigured) {
      throw new Error("Twilio is not configured");
    }

    try {
      // Format the recipient phone number
      const formattedTo = this.formatPhoneNumber(to);
      // Determine the effective 'from' number
      const effectiveFromNumber = fromPhoneNumber
        ? this.formatPhoneNumber(fromPhoneNumber)
        : this.phoneNumber;

      console.log(
        `Making outbound call to ${formattedTo} from ${effectiveFromNumber}`
      );
      console.log(`Callback URL: ${callbackUrl}`);

      // Validate the callback URL
      if (!callbackUrl.startsWith("http")) {
        throw new Error(
          `Invalid callback URL: ${callbackUrl}. Must be a full URL starting with http or https.`
        );
      }

      // Extract the base URL without the specific endpoint
      const baseUrl = callbackUrl.substring(
        0,
        callbackUrl.lastIndexOf("/api/v1/")
      );
      const statusCallbackUrl = `${baseUrl}/api/v1/direct-call/status`;

      console.log(`Using status callback URL: ${statusCallbackUrl}`);

      const call = await this.client.calls.create({
        to: formattedTo,
        from: effectiveFromNumber, // Use the determined 'from' number
        url: callbackUrl,
        method: "POST", // Explicitly set the HTTP method to POST
        statusCallback: callbackUrl.includes("twimlets.com")
          ? null
          : statusCallbackUrl,
        statusCallbackEvent: ["initiated", "ringing", "answered", "completed"],
        statusCallbackMethod: "POST",
      });
      // Log the full call object for debugging
      console.log("Twilio call object:", JSON.stringify(call, null, 2));

      console.log(
        `Call initiated with SID: ${call.sid}, status: ${call.status}`
      );
      console.log(
        `Call direction: ${call.direction}, from: ${call.from}, to: ${call.to}`
      );

      // Log the URL that Twilio will use to get TwiML
      console.log(
        `Twilio will request TwiML from: ${callbackUrl} using HTTP ${
          call.method || "POST"
        }`
      );

      if (call.status !== "queued" && call.status !== "initiated") {
        console.warn(
          `Unexpected call status: ${call.status}. This may indicate an issue.`
        );
      }

      return {
        sid: call.sid,
        status: call.status,
        direction: call.direction,
        from: call.from,
        to: call.to,
      };
    } catch (error) {
      console.error("Error making outbound call:", error);
      // Add more detailed error information
      if (error.code) {
        console.error(
          `Twilio error code: ${error.code}, more info: ${error.moreInfo}`
        );
      }
      throw error;
    }
  }

  /**
   * Generate TwiML for connecting a call to a LiveKit room
   * @param {string} roomName - LiveKit room name
   * @param {string} participantIdentity - LiveKit participant identity
   * @returns {string} - TwiML markup
   */
  // generateConnectTwiML(roomName, participantIdentity) {
  //   // Create a very simple TwiML that will definitely work
  //   // This is a fallback in case the more complex TwiML has issues
  //   const simpleTwiML = `
  //     <Response>
  //       <Say>Welcome to the call. You are now connected.</Say>
  //       <Pause length="2"/>
  //       <Say>This is a test call using Twilio.</Say>
  //       <Pause length="30"/>
  //       <Say>Press any key to end the call.</Say>
  //       <Gather numDigits="1" timeout="60"/>
  //       <Say>Thank you for testing. Goodbye.</Say>
  //     </Response>
  //   `;

  //   // Extract the phone number from the room name (format: whisper_userId_contactId_phoneNumber)
  //   const phoneNumberPart = roomName.split("_")[3] || "";
  //   if (!phoneNumberPart) {
  //     console.error("Could not extract phone number from room name:", roomName);
  //     return simpleTwiML;
  //   }

  //   try {
  //     console.log("Generating TwiML for three-way call configuration");
  //     console.log(
  //       `Room name: ${roomName}, Participant identity: ${participantIdentity}`
  //     );
  //     console.log(`Phone number from room name: ${phoneNumberPart}`);

  //     // Get LiveKit configuration
  //     const liveKitHost =
  //       config.liveKit.url?.replace(/^(wss?|https?):\/\//, "") || "";

  //     // Log the configuration for debugging
  //     console.log(`LiveKit host: ${liveKitHost}`);
  //     console.log(
  //       `LiveKit API key: ${config.liveKit.apiKey ? "Set" : "Not set"}`
  //     );
  //     console.log(
  //       `LiveKit API secret: ${config.liveKit.apiSecret ? "Set" : "Not set"}`
  //     );

  //     // Check if we should use the Stream element for LiveKit integration
  //     const useStreamElement = process.env.USE_STREAM_ELEMENT === "true";

  //     // Check if we're in development mode
  //     const isDevelopment = process.env.NODE_ENV !== "production";

  //     if (useStreamElement && liveKitHost && config.liveKit.apiKey) {
  //       // TEMPORARILY DISABLED: Stream element connection to LiveKit
  //       // Instead, use a simple call to test basic Twilio functionality
  //       console.log("TEMPORARILY USING SIMPLE CALL INSTEAD OF STREAM ELEMENT");

  //       return `
  //         <Response>
  //           <Say>Welcome to the call. This is a simple test call without LiveKit integration.</Say>
  //           <Pause length="2"/>
  //           <Say>We are testing basic Twilio functionality first.</Say>
  //           <Pause length="5"/>
  //           <Say>If you can hear this message, basic Twilio calling is working correctly.</Say>
  //           <Pause length="30"/>
  //           <Say>The call will now end. Thank you for testing.</Say>
  //         </Response>
  //       `;
  //     } else if (isDevelopment || !useStreamElement) {
  //       // Use a simple two-way call with <Dial>
  //       console.log("Using simple two-way call with Dial");

  //       return `
  //         <Response>
  //           <Say>Welcome to the call. You will now be connected to the other party.</Say>
  //           <Dial callerId="${this.phoneNumber}" record="record-from-answer" timeout="30">
  //             <Number>${phoneNumberPart}</Number>
  //           </Dial>
  //           <Say>The call has ended. Thank you for using our service.</Say>
  //         </Response>
  //       `;
  //     } else {
  //       // Fallback to a standard call
  //       console.log("Using standard call TwiML (fallback)");

  //       return `
  //         <Response>
  //           <Say>Welcome to the call. You are now being connected.</Say>
  //           <Dial callerId="${this.phoneNumber}" timeout="30">
  //             <Number>${phoneNumberPart}</Number>
  //           </Dial>
  //           <Say>The call has ended. Thank you for using our service.</Say>
  //         </Response>
  //       `;
  //     }
  //   } catch (error) {
  //     console.error("Error generating TwiML:", error);
  //     // If there's any error, fall back to the simple TwiML
  //     return simpleTwiML;
  //   }
  // }

  generateConnectTwiML(roomName, participantIdentity) {
    const wsUrl = config.liveKit.url.startsWith("ws")
      ? config.liveKit.url
      : config.liveKit.url.replace(/^https?/, "ws");

    return `
      <Response>
        <Connect>
          <Stream 
            name="LiveKit Stream"
            url="${wsUrl}"
            statusCallback="${config.server.apiURL}/direct-call/stream-events"
            statusCallbackMethod="POST"
          >
            <Parameter name="room" value="${roomName}" />
            <Parameter name="identity" value="${participantIdentity}" />
            <Parameter name="token" value="${this.generateLiveKitToken(
              roomName,
              participantIdentity
            )}" />
            <Parameter name="publish" value="true" />
            <Parameter name="subscribe" value="true" />
          </Stream>
        </Connect>
      </Response>
    `;
  }

  generateLiveKitToken(roomName, identity) {
    const API_KEY = config.liveKit.apiKey;
    const API_SECRET = config.liveKit.apiSecret;

    const at = new AccessToken(API_KEY, API_SECRET, {
      identity: identity,
      ttl: "60m",
    });

    at.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
      canPublishData: true,
    });

    return at.toJwt();
  }

  /**
   * End an active call
   * @param {string} callSid - Twilio call SID
   * @returns {Promise<object>} - Call details
   */
  async endCall(callSid) {
    if (!this.isConfigured) {
      throw new Error("Twilio is not configured");
    }

    try {
      console.log(`Attempting to end Twilio call with SID: ${callSid}`);

      // First, check if the call exists and is still in progress
      try {
        const callDetails = await this.client.calls(callSid).fetch();
        console.log(`Current call status: ${callDetails.status}`);

        // If the call is already completed or failed, just return the current status
        if (
          ["completed", "busy", "failed", "no-answer", "canceled"].includes(
            callDetails.status
          )
        ) {
          console.log(
            `Call is already in terminal state: ${callDetails.status}`
          );
          return {
            sid: callDetails.sid,
            status: callDetails.status,
            message: "Call was already in terminal state",
          };
        }
      } catch (fetchError) {
        console.error(
          `Error fetching call details for SID ${callSid}:`,
          fetchError
        );
        // Continue with the attempt to end the call even if we can't fetch details
      }

      // Try to end the call
      const call = await this.client.calls(callSid).update({
        status: "completed",
      });

      console.log(
        `Successfully ended call with SID ${callSid}, new status: ${call.status}`
      );
      return {
        sid: call.sid,
        status: call.status,
        message: "Call ended successfully",
      };
    } catch (error) {
      console.error(`Error ending call with SID ${callSid}:`, error);

      // Return a structured error instead of throwing
      return {
        error: true,
        message: error.message,
        code: error.code,
        status: error.status,
        details: "Failed to end call but continuing with call cleanup",
      };
    }
  }

  /**
   * Generate a Twilio Client capability token
   * @param {string} clientIdentity - Client identity
   * @returns {string} - Capability token
   */
  generateClientToken(clientIdentity) {
    if (!this.isConfigured) {
      throw new Error("Twilio is not configured");
    }

    try {
      console.log(`Generating client token for identity: ${clientIdentity}`);
      console.log(
        `Using Account SID: ${config.twilio.accountSid.substring(0, 5)}...`
      );
      console.log(
        `Using Application SID: ${
          config.twilio.applicationSid
            ? config.twilio.applicationSid.substring(0, 5) + "..."
            : "Not set"
        }`
      );

      // Check if we have all required credentials
      if (!config.twilio.applicationSid) {
        console.warn(
          "WARNING: No Twilio Application SID configured - outgoing calls will not work"
        );
      }

      // Create a capability token
      const capability = new ClientCapability({
        accountSid: config.twilio.accountSid,
        authToken: config.twilio.authToken,
      });

      // Allow the client to receive incoming calls
      console.log(`Adding IncomingClientScope for identity: ${clientIdentity}`);
      capability.addScope(
        new ClientCapability.IncomingClientScope(clientIdentity)
      );

      // Allow the client to make outgoing calls
      if (config.twilio.applicationSid) {
        console.log(
          `Adding OutgoingClientScope with application SID: ${config.twilio.applicationSid.substring(
            0,
            5
          )}...`
        );
        capability.addScope(
          new ClientCapability.OutgoingClientScope({
            applicationSid: config.twilio.applicationSid,
            clientName: clientIdentity,
          })
        );
      } else {
        console.warn(
          "No Application SID configured - outgoing calls will not work"
        );
      }

      // Generate the token
      const token = capability.toJwt();

      // Log a portion of the token for debugging
      console.log(`Generated client token successfully for ${clientIdentity}`);
      console.log(`Token starts with: ${token.substring(0, 20)}...`);
      console.log(`Token length: ${token.length} characters`);

      return token;
    } catch (error) {
      console.error("Error generating client token:", error);
      console.error("Error details:", error.message);
      if (error.stack) {
        console.error("Stack trace:", error.stack);
      }
      throw error;
    }
  }

  /**
   * Release a phone number from Twilio account
   * @param {string} twilioSid - The SID of the phone number resource to release
   * @returns {Promise<{success: boolean, message?: string}>}
   */
  async releasePhoneNumber(twilioSid) {
    if (!this.isConfigured) {
      // console.error("Twilio is not configured. Cannot release phone number.");
      // throw new Error("Twilio is not configured");
      // For deletion, if Twilio isn't configured, we can't release, but DB deletion might still be desired.
      // So, we'll return a success-like response indicating it couldn't be processed with Twilio.
      return { success: true, message: "Twilio not configured, skipped release." };
    }
    if (!twilioSid) {
        console.warn("No Twilio SID provided for phone number release.");
        return { success: false, message: "Twilio SID is required." };
    }

    try {
      console.log(`Attempting to release Twilio phone number with SID: ${twilioSid}`);
      // The Twilio API uses .remove() on the resource instance.
      await this.client.incomingPhoneNumbers(twilioSid).remove();
      console.log(`Successfully released Twilio phone number SID: ${twilioSid}`);
      return { success: true, message: "Phone number released successfully from Twilio." };
    } catch (error) {
      console.error(`Error releasing Twilio phone number SID ${twilioSid}:`, error);
      // Handle cases where the number might already be released or the SID is invalid
      // Twilio error code 20404 means "The requested resource was not found"
      if (error.status === 404 || error.code === 20404) {
        console.warn(`Twilio phone number SID ${twilioSid} not found. It might have been already released.`);
        return { success: true, message: "Phone number not found on Twilio (possibly already released)." };
      }
      // For other errors, indicate failure but allow DB operation to proceed if desired by caller.
      return { success: false, message: `Failed to release phone number from Twilio: ${error.message}` };
    }
  }
}

// Create a singleton instance
const twilioService = new TwilioService();

export default twilioService;
