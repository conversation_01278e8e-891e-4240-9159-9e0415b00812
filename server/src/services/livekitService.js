import { RoomServiceClient } from 'livekit-server-sdk';
import config from '../config/config.js';

/**
 * Service for managing LiveKit rooms and participants
 */
class LiveKitService {
  constructor() {
    // Check if LiveKit credentials are configured
    if (
      !config.liveKit?.apiKey ||
      !config.liveKit?.apiSecret ||
      !config.liveKit?.url
    ) {
      console.warn(
        "LiveKit credentials not fully configured. LiveKit functionality will be limited."
      );
      console.warn(
        `API Key: ${config.liveKit?.apiKey ? "Set" : "Not set"}`
      );
      console.warn(
        `API Secret: ${config.liveKit?.apiSecret ? "Set" : "Not set"}`
      );
      console.warn(
        `URL: ${config.liveKit?.url ? "Set" : "Not set"}`
      );
      this.isConfigured = false;
      return;
    }

    try {
      this.roomService = new RoomServiceClient(
        config.liveKit.url,
        config.liveKit.apiKey,
        config.liveKit.apiSecret
      );
      this.isConfigured = true;
      console.log("LiveKit service initialized successfully");
    } catch (error) {
      console.error("Error initializing LiveKit client:", error);
      this.isConfigured = false;
    }
  }

  /**
   * Get participants in a room
   * @param {string} roomName - Name of the room
   * @returns {Promise<Array>} - List of participants
   */
  async getParticipants(roomName) {
    if (!this.isConfigured) {
      throw new Error("LiveKit is not configured");
    }

    try {
      const participants = await this.roomService.listParticipants(roomName);
      return participants;
    } catch (error) {
      console.error(`Error getting participants for room ${roomName}:`, error);
      throw error;
    }
  }

  /**
   * Update subscriptions for a participant
   * This is used to control which tracks a participant can receive
   * @param {string} roomName - Name of the room
   * @param {string} participantIdentity - Identity of the participant to update
   * @param {Array<string>} trackSids - List of track SIDs
   * @param {boolean} subscribe - Whether to subscribe or unsubscribe
   * @returns {Promise<void>}
   */
  async updateSubscriptions(roomName, participantIdentity, trackSids, subscribe) {
    if (!this.isConfigured) {
      throw new Error("LiveKit is not configured");
    }

    try {
      console.log(`Updating subscriptions for participant ${participantIdentity} in room ${roomName}`);
      console.log(`Tracks: ${trackSids.join(', ')}, Subscribe: ${subscribe}`);
      
      await this.roomService.updateSubscriptions(
        roomName,
        participantIdentity,
        trackSids,
        subscribe
      );
      
      console.log(`Successfully updated subscriptions for participant ${participantIdentity}`);
    } catch (error) {
      console.error(`Error updating subscriptions for participant ${participantIdentity}:`, error);
      throw error;
    }
  }

  /**
   * Get tracks in a room
   * @param {string} roomName - Name of the room
   * @returns {Promise<Array>} - List of tracks
   */
  async getTracks(roomName) {
    if (!this.isConfigured) {
      throw new Error("LiveKit is not configured");
    }

    try {
      // First get all participants
      const participants = await this.getParticipants(roomName);
      
      // Then collect all tracks from all participants
      const tracks = [];
      for (const participant of participants) {
        // Get tracks for this participant
        const participantTracks = await this.roomService.listParticipantTracks(
          roomName,
          participant.identity
        );
        
        // Add participant identity to each track for easier identification
        participantTracks.forEach(track => {
          tracks.push({
            ...track,
            participantIdentity: participant.identity
          });
        });
      }
      
      return tracks;
    } catch (error) {
      console.error(`Error getting tracks for room ${roomName}:`, error);
      throw error;
    }
  }

  /**
   * Unsubscribe SIP participant from agent tracks
   * This is used to prevent the called person from hearing the AI agent
   * @param {string} roomName - Name of the room
   * @param {string} sipParticipantIdentity - Identity of the SIP participant
   * @returns {Promise<void>}
   */
  async unsubscribeSipFromAgentTracks(roomName, sipParticipantIdentity) {
    if (!this.isConfigured) {
      throw new Error("LiveKit is not configured");
    }

    try {
      console.log(`Unsubscribing SIP participant ${sipParticipantIdentity} from agent tracks in room ${roomName}`);
      
      // Get all tracks in the room
      const tracks = await this.getTracks(roomName);
      
      // Filter for agent audio tracks
      // Agent participants typically have identities starting with "agent_" or have a metadata field indicating they are agents
      const agentTracks = tracks.filter(track => {
        // Check if this is an audio track
        if (track.type !== 'audio') return false;
        
        // Check if this is from an agent participant
        const isAgent = 
          track.participantIdentity.startsWith('agent_') || 
          (track.metadata && track.metadata.includes('agent'));
          
        return isAgent;
      });
      
      if (agentTracks.length === 0) {
        console.log(`No agent audio tracks found in room ${roomName}`);
        return;
      }
      
      // Get the track SIDs
      const agentTrackSids = agentTracks.map(track => track.sid);
      
      console.log(`Found ${agentTrackSids.length} agent audio tracks: ${agentTrackSids.join(', ')}`);
      
      // Unsubscribe the SIP participant from these tracks
      await this.updateSubscriptions(
        roomName,
        sipParticipantIdentity,
        agentTrackSids,
        false // unsubscribe
      );
      
      console.log(`Successfully unsubscribed SIP participant ${sipParticipantIdentity} from agent tracks`);
    } catch (error) {
      console.error(`Error unsubscribing SIP participant ${sipParticipantIdentity} from agent tracks:`, error);
      throw error;
    }
  }
}

// Create a singleton instance
const livekitService = new LiveKitService();

export default livekitService;
