import { RoomServiceClient } from 'livekit-server-sdk';
import config from '../config/config.js';

/**
 * Service for managing server-enforced audio routing in whisper calls
 * Implements the selective audio routing described in livekit.md Section 4
 */
class WhisperAudioRoutingService {
  constructor() {
    if (!config.liveKit.url || !config.liveKit.apiKey || !config.liveKit.apiSecret) {
      console.warn('LiveKit credentials not configured. Audio routing will be disabled.');
      this.isConfigured = false;
      return;
    }

    this.roomService = new RoomServiceClient(
      config.liveKit.url,
      config.liveKit.apiKey,
      config.liveKit.apiSecret
    );
    this.isConfigured = true;
    console.log('Whisper audio routing service initialized successfully');
  }

  /**
   * Set up AI-to-User whisper mode
   * User hears: Caller + AI Assistant
   * Caller hears: User ONLY
   * AI Assistant hears: Caller + User
   */
  async setupAiToUserMode(roomName, userIdentity, callerIdentity, aiIdentity) {
    if (!this.isConfigured) {
      throw new Error('LiveKit is not configured');
    }

    try {
      console.log(`Setting up AI-to-User whisper mode in room: ${roomName}`);

      // Get all participants and their tracks
      const participants = await this.roomService.listParticipants(roomName);
      
      // Find track SIDs for each participant
      const userTracks = this.getParticipantTracks(participants, userIdentity);
      const callerTracks = this.getParticipantTracks(participants, callerIdentity);
      const aiTracks = this.getParticipantTracks(participants, aiIdentity);

      // Configure User subscriptions: Caller + AI Assistant
      if (userTracks.found) {
        const userSubscriptions = [
          ...callerTracks.audioTracks,
          ...aiTracks.audioTracks,
        ];
        await this.updateParticipantSubscriptions(
          roomName,
          userIdentity,
          userSubscriptions,
          true
        );
        console.log(`User ${userIdentity} subscribed to Caller and AI tracks`);
      }

      // Configure Caller subscriptions: User ONLY
      if (callerTracks.found) {
        const callerSubscriptions = [...userTracks.audioTracks];
        await this.updateParticipantSubscriptions(
          roomName,
          callerIdentity,
          callerSubscriptions,
          true
        );
        console.log(`Caller ${callerIdentity} subscribed to User tracks only`);
      }

      // Configure AI Assistant subscriptions: Caller + User
      if (aiTracks.found) {
        const aiSubscriptions = [
          ...callerTracks.audioTracks,
          ...userTracks.audioTracks,
        ];
        await this.updateParticipantSubscriptions(
          roomName,
          aiIdentity,
          aiSubscriptions,
          true
        );
        console.log(`AI ${aiIdentity} subscribed to Caller and User tracks`);
      }

      return {
        success: true,
        mode: 'ai-to-user',
        subscriptions: {
          user: [...callerTracks.audioTracks, ...aiTracks.audioTracks],
          caller: [...userTracks.audioTracks],
          ai: [...callerTracks.audioTracks, ...userTracks.audioTracks],
        },
      };
    } catch (error) {
      console.error('Error setting up AI-to-User whisper mode:', error);
      throw error;
    }
  }

  /**
   * Set up User-to-AI whisper mode
   * User hears: AI Assistant ONLY
   * Caller hears: AI Assistant ONLY
   * AI Assistant hears: User + Caller
   */
  async setupUserToAiMode(roomName, userIdentity, callerIdentity, aiIdentity) {
    if (!this.isConfigured) {
      throw new Error('LiveKit is not configured');
    }

    try {
      console.log(`Setting up User-to-AI whisper mode in room: ${roomName}`);

      // Get all participants and their tracks
      const participants = await this.roomService.listParticipants(roomName);
      
      // Find track SIDs for each participant
      const userTracks = this.getParticipantTracks(participants, userIdentity);
      const callerTracks = this.getParticipantTracks(participants, callerIdentity);
      const aiTracks = this.getParticipantTracks(participants, aiIdentity);

      // Configure User subscriptions: AI Assistant ONLY
      if (userTracks.found) {
        const userSubscriptions = [...aiTracks.audioTracks];
        await this.updateParticipantSubscriptions(
          roomName,
          userIdentity,
          userSubscriptions,
          true
        );
        console.log(`User ${userIdentity} subscribed to AI tracks only`);
      }

      // Configure Caller subscriptions: AI Assistant ONLY
      if (callerTracks.found) {
        const callerSubscriptions = [...aiTracks.audioTracks];
        await this.updateParticipantSubscriptions(
          roomName,
          callerIdentity,
          callerSubscriptions,
          true
        );
        console.log(`Caller ${callerIdentity} subscribed to AI tracks only`);
      }

      // Configure AI Assistant subscriptions: User + Caller
      if (aiTracks.found) {
        const aiSubscriptions = [
          ...userTracks.audioTracks,
          ...callerTracks.audioTracks,
        ];
        await this.updateParticipantSubscriptions(
          roomName,
          aiIdentity,
          aiSubscriptions,
          true
        );
        console.log(`AI ${aiIdentity} subscribed to User and Caller tracks`);
      }

      return {
        success: true,
        mode: 'user-to-ai',
        subscriptions: {
          user: [...aiTracks.audioTracks],
          caller: [...aiTracks.audioTracks],
          ai: [...userTracks.audioTracks, ...callerTracks.audioTracks],
        },
      };
    } catch (error) {
      console.error('Error setting up User-to-AI whisper mode:', error);
      throw error;
    }
  }

  /**
   * Reset to normal conference mode (everyone hears everyone)
   */
  async setupNormalMode(roomName, userIdentity, callerIdentity, aiIdentity) {
    if (!this.isConfigured) {
      throw new Error('LiveKit is not configured');
    }

    try {
      console.log(`Setting up normal conference mode in room: ${roomName}`);

      // Get all participants and their tracks
      const participants = await this.roomService.listParticipants(roomName);
      
      // Find track SIDs for each participant
      const userTracks = this.getParticipantTracks(participants, userIdentity);
      const callerTracks = this.getParticipantTracks(participants, callerIdentity);
      const aiTracks = this.getParticipantTracks(participants, aiIdentity);

      // All participants subscribe to all other participants
      const allTracks = [
        ...userTracks.audioTracks,
        ...callerTracks.audioTracks,
        ...aiTracks.audioTracks,
      ];

      // Configure subscriptions for each participant
      for (const identity of [userIdentity, callerIdentity, aiIdentity]) {
        const participantTracks = this.getParticipantTracks(participants, identity);
        if (participantTracks.found) {
          // Subscribe to all tracks except own
          const subscriptions = allTracks.filter(
            trackSid => !participantTracks.audioTracks.includes(trackSid)
          );
          
          await this.updateParticipantSubscriptions(
            roomName,
            identity,
            subscriptions,
            true
          );
          console.log(`${identity} subscribed to all other participants`);
        }
      }

      return {
        success: true,
        mode: 'normal',
        subscriptions: {
          user: [...callerTracks.audioTracks, ...aiTracks.audioTracks],
          caller: [...userTracks.audioTracks, ...aiTracks.audioTracks],
          ai: [...userTracks.audioTracks, ...callerTracks.audioTracks],
        },
      };
    } catch (error) {
      console.error('Error setting up normal conference mode:', error);
      throw error;
    }
  }

  /**
   * Helper method to get participant tracks
   */
  getParticipantTracks(participants, identity) {
    const participant = participants.find(p => p.identity === identity);
    
    if (!participant) {
      return {
        found: false,
        audioTracks: [],
        videoTracks: [],
      };
    }

    const audioTracks = participant.tracks
      .filter(track => track.type === 'audio')
      .map(track => track.sid);
    
    const videoTracks = participant.tracks
      .filter(track => track.type === 'video')
      .map(track => track.sid);

    return {
      found: true,
      audioTracks,
      videoTracks,
    };
  }

  /**
   * Helper method to update participant subscriptions
   */
  async updateParticipantSubscriptions(roomName, participantIdentity, trackSids, subscribe) {
    try {
      await this.roomService.updateSubscriptions(
        roomName,
        participantIdentity,
        trackSids,
        subscribe
      );
    } catch (error) {
      console.error(`Error updating subscriptions for ${participantIdentity}:`, error);
      throw error;
    }
  }

  /**
   * Get current room state and participant information
   */
  async getRoomState(roomName) {
    if (!this.isConfigured) {
      throw new Error('LiveKit is not configured');
    }

    try {
      const participants = await this.roomService.listParticipants(roomName);
      
      return {
        roomName,
        participantCount: participants.length,
        participants: participants.map(p => ({
          identity: p.identity,
          tracks: p.tracks.map(t => ({
            sid: t.sid,
            type: t.type,
            name: t.name,
          })),
          metadata: p.metadata,
        })),
      };
    } catch (error) {
      console.error('Error getting room state:', error);
      throw error;
    }
  }
}

export default new WhisperAudioRoutingService();
