import twilio from 'twilio';
import config from '../config/config.js';

/**
 * Service for managing Twilio SIP trunks and LiveKit SIP integration
 * Implements the architecture described in livekit.md for Elastic SIP Trunking
 */
class SipService {
  constructor() {
    if (!config.twilio.accountSid || !config.twilio.authToken) {
      console.warn('Twilio credentials not configured. SIP functionality will be disabled.');
      this.isConfigured = false;
      return;
    }

    this.client = twilio(config.twilio.accountSid, config.twilio.authToken);
    this.isConfigured = true;
    console.log('SIP service initialized successfully');
  }

  /**
   * Create or update Twilio Elastic SIP Trunk
   * Following the architecture from livekit.md Section 1.2
   */
  async createSipTrunk() {
    if (!this.isConfigured) {
      throw new Error('Twilio is not configured');
    }

    try {
      const sipConfig = config.twilio.sipTrunk;
      
      if (!sipConfig.domainName || !sipConfig.domainName.endsWith('.pstn.twilio.com')) {
        throw new Error('SIP domain must end with .pstn.twilio.com');
      }

      console.log(`Creating SIP trunk with domain: ${sipConfig.domainName}`);

      // Create the SIP trunk
      const trunk = await this.client.trunking.v1.trunks.create({
        friendlyName: 'LiveKit Whisper SIP Trunk',
        domainName: sipConfig.domainName,
      });

      console.log(`SIP trunk created with SID: ${trunk.sid}`);

      // Configure Origination (inbound calls)
      await this.configureTrunkOrigination(trunk.sid);

      // Configure Termination (outbound calls)
      await this.configureTrunkTermination(trunk.sid);

      // Associate phone number with trunk
      if (config.twilio.phoneNumber) {
        await this.associatePhoneNumber(trunk.sid, config.twilio.phoneNumber);
      }

      return trunk;
    } catch (error) {
      console.error('Error creating SIP trunk:', error);
      throw error;
    }
  }

  /**
   * Configure trunk origination for inbound calls
   * Routes incoming calls to LiveKit SIP service
   */
  async configureTrunkOrigination(trunkSid) {
    try {
      // This should point to your LiveKit SIP service
      // Format: sip:<your_livekit_sip_host>
      const livekitSipUri = process.env.LIVEKIT_SIP_URI || 'sip:your-livekit-instance.livekit.cloud';

      const origination = await this.client.trunking.v1
        .trunks(trunkSid)
        .originationUrls.create({
          friendlyName: 'LiveKit SIP Origination',
          sipUrl: livekitSipUri,
          enabled: true,
          priority: 10,
          weight: 10,
        });

      console.log(`Origination configured: ${origination.sid}`);
      return origination;
    } catch (error) {
      console.error('Error configuring trunk origination:', error);
      throw error;
    }
  }

  /**
   * Configure trunk termination for outbound calls
   * Sets up authentication and termination URI
   */
  async configureTrunkTermination(trunkSid) {
    try {
      const sipConfig = config.twilio.sipTrunk;

      // Create credential list for authentication
      const credentialList = await this.client.sip.credentialLists.create({
        friendlyName: 'LiveKit SIP Credentials',
      });

      // Add credentials to the list
      await this.client.sip
        .credentialLists(credentialList.sid)
        .credentials.create({
          username: sipConfig.username,
          password: sipConfig.password,
        });

      // Associate credential list with trunk
      await this.client.trunking.v1
        .trunks(trunkSid)
        .credentialLists.create({
          credentialListSid: credentialList.sid,
        });

      console.log(`Termination configured with credential list: ${credentialList.sid}`);
      
      // The termination SIP URI will be: <trunk-domain>.pstn.twilio.com
      const terminationUri = sipConfig.domainName;
      console.log(`Termination URI: ${terminationUri}`);

      return {
        credentialListSid: credentialList.sid,
        terminationUri,
      };
    } catch (error) {
      console.error('Error configuring trunk termination:', error);
      throw error;
    }
  }

  /**
   * Associate a phone number with the SIP trunk
   */
  async associatePhoneNumber(trunkSid, phoneNumber) {
    try {
      // First, find the phone number resource
      const phoneNumbers = await this.client.incomingPhoneNumbers.list({
        phoneNumber: phoneNumber,
      });

      if (phoneNumbers.length === 0) {
        throw new Error(`Phone number ${phoneNumber} not found in account`);
      }

      const phoneNumberSid = phoneNumbers[0].sid;

      // Associate with trunk
      await this.client.trunking.v1
        .trunks(trunkSid)
        .phoneNumbers.create({
          phoneNumberSid: phoneNumberSid,
        });

      console.log(`Phone number ${phoneNumber} associated with trunk ${trunkSid}`);
      return true;
    } catch (error) {
      console.error('Error associating phone number with trunk:', error);
      throw error;
    }
  }

  /**
   * List existing SIP trunks
   */
  async listSipTrunks() {
    if (!this.isConfigured) {
      throw new Error('Twilio is not configured');
    }

    try {
      const trunks = await this.client.trunking.v1.trunks.list();
      return trunks;
    } catch (error) {
      console.error('Error listing SIP trunks:', error);
      throw error;
    }
  }

  /**
   * Delete a SIP trunk
   */
  async deleteSipTrunk(trunkSid) {
    if (!this.isConfigured) {
      throw new Error('Twilio is not configured');
    }

    try {
      await this.client.trunking.v1.trunks(trunkSid).remove();
      console.log(`SIP trunk ${trunkSid} deleted`);
      return true;
    } catch (error) {
      console.error('Error deleting SIP trunk:', error);
      throw error;
    }
  }

  /**
   * Get SIP trunk configuration details
   */
  async getSipTrunkDetails(trunkSid) {
    if (!this.isConfigured) {
      throw new Error('Twilio is not configured');
    }

    try {
      const trunk = await this.client.trunking.v1.trunks(trunkSid).fetch();
      const originations = await this.client.trunking.v1
        .trunks(trunkSid)
        .originationUrls.list();
      const phoneNumbers = await this.client.trunking.v1
        .trunks(trunkSid)
        .phoneNumbers.list();
      const credentialLists = await this.client.trunking.v1
        .trunks(trunkSid)
        .credentialLists.list();

      return {
        trunk,
        originations,
        phoneNumbers,
        credentialLists,
      };
    } catch (error) {
      console.error('Error getting SIP trunk details:', error);
      throw error;
    }
  }
}

export default new SipService();
