import jwt from "jsonwebtoken";
import config from "../config/config.js";

const JWT_SECRET = config.jwt.secret || "your-secret-key";

export const generateToken = (user) => {
  return jwt.sign(
    {
      id: user.id,
      email: user.email,
      role: user.role,
    },
    JWT_SECRET,
    { expiresIn: "7d" }
  );
};

export const verifyToken = (token) => {
  return jwt.verify(token, JWT_SECRET);
};
