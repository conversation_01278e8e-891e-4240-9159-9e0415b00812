import dotenv from "dotenv";
import { fileURLToPath } from "url";
import path from "path";

// Resolve the path to the .env file which is in server/.env
const __dirname = path.dirname(fileURLToPath(import.meta.url));
dotenv.config({ path: path.resolve(__dirname, "../../.env") });

const config = {
  server: {
    port: parseInt(process.env.PORT || "3000", 10),
    nodeEnv: process.env.NODE_ENV || "development",
    isProduction: process.env.NODE_ENV === "production",
    isDevelopment: process.env.NODE_ENV === "development",
    apiURL: process.env.API_URL || "http://localhost:3030/api/v1",
  },
  database: {
    url: process.env.DATABASE_URL,
  },
  jwt: {
    secret: process.env.JWT_SECRET,
    refreshSecret: process.env.JWT_REFRESH_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN || "15m",
    refreshTokenExpiresIn: process.env.JWT_REFRESH_TOKEN_EXPIRES_IN || "7d",
  },
  encryption: {
    key: process.env.ENCRYPTION_KEY,
  },
  apis: {
    openAPI: {
      apiKey: process.env.OPEN_API_KEY,
      baseUrl: process.env.OPEN_API_BASE_URL,
    },
    openRouter: {
      apiKey: process.env.OPENROUTER_API_KEY,
      baseUrl: process.env.OPENROUTER_BASE_URL,
    },
    cartesia: {
      apiKey: process.env.CARTESIA_API_KEY,
    },
    playht: {
      apiKey: process.env.PLAYHT_API_KEY,
      userId: process.env.PLAYHT_USER_ID,
    },
    elevenLabs: {
      apiKey: process.env.ELEVENLABS_API_KEY,
    },
    deepgram: {
      apiKey: process.env.DEEPGRAM_API_KEY,
    },
  },
  twilio: {
    accountSid: process.env.TWILIO_ACCOUNT_SID,
    authToken: process.env.TWILIO_AUTH_TOKEN,
    phoneNumber: process.env.TWILIO_PHONE_NUMBER,
    applicationSid: process.env.TWILIO_APPLICATION_SID,
    // SIP Trunk configuration for Whisper feature
    sipTrunk: {
      domainName: process.env.TWILIO_SIP_DOMAIN, // Must end with .pstn.twilio.com
      username: process.env.TWILIO_SIP_USERNAME,
      password: process.env.TWILIO_SIP_PASSWORD,
    },
  },
  liveKit: {
    url: process.env.LIVEKIT_URL,
    apiKey: process.env.LIVEKIT_API_KEY,
    apiSecret: process.env.LIVEKIT_API_SECRET,
  },
};

// Validate required configuration
const validateConfig = () => {
  const requiredConfigs = [
    { key: "DATABASE_URL", value: config.database.url },
    { key: "JWT_SECRET", value: config.jwt.secret },
    { key: "JWT_REFRESH_SECRET", value: config.jwt.refreshSecret },
    { key: "ENCRYPTION_KEY", value: config.encryption.key },
  ];

  if (config.server.isProduction) {
    requiredConfigs.push(
      { key: "LIVEKIT_URL", value: config.liveKit.url },
      { key: "LIVEKIT_API_KEY", value: config.liveKit.apiKey },
      { key: "LIVEKIT_API_SECRET", value: config.liveKit.apiSecret }
    );
  }

  const missingConfigs = requiredConfigs.filter((item) => !item.value);

  if (missingConfigs.length > 0) {
    const missingKeys = missingConfigs.map((item) => item.key).join(", ");
    throw new Error(`Missing required environment variables: ${missingKeys}`);
  }
};

validateConfig();

export default config;
