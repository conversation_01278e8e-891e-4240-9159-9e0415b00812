#!/usr/bin/env node

/**
 * This script helps set up LiveKit for Twilio integration.
 * It checks the LiveKit configuration and provides guidance on how to set it up.
 */

import fs from 'fs';
import readline from 'readline';
import { fileURLToPath } from 'url';
import path from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const envPath = path.join(__dirname, '..', '.env');

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to prompt for input
const prompt = (question) => {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
};

// Main function
async function main() {
  console.log('\n=== LiveKit-Twilio Integration Setup ===');
  console.log('This script will help you set up LiveKit for Twilio integration.');
  console.log('You need to have a publicly accessible LiveKit server for this to work.\n');

  // Check current LiveKit configuration
  console.log('Current LiveKit configuration:');
  console.log(`LIVEKIT_URL: ${process.env.LIVEKIT_URL || 'Not set'}`);
  console.log(`LIVEKIT_API_KEY: ${process.env.LIVEKIT_API_KEY ? 'Set' : 'Not set'}`);
  console.log(`LIVEKIT_API_SECRET: ${process.env.LIVEKIT_API_SECRET ? 'Set' : 'Not set'}\n`);

  // Check if LiveKit is properly configured
  if (!process.env.LIVEKIT_URL || !process.env.LIVEKIT_API_KEY || !process.env.LIVEKIT_API_SECRET) {
    console.log('LiveKit is not fully configured. Please set up LiveKit first.\n');
    
    const setupLiveKit = await prompt('Do you want to set up LiveKit now? (y/n): ');
    if (setupLiveKit.toLowerCase() !== 'y') {
      console.log('\nExiting setup. Please set up LiveKit manually and run this script again.');
      rl.close();
      return;
    }
    
    const liveKitUrl = await prompt('Enter your LiveKit server URL (e.g., example.livekit.cloud): ');
    const liveKitApiKey = await prompt('Enter your LiveKit API key: ');
    const liveKitApiSecret = await prompt('Enter your LiveKit API secret: ');
    
    // Read existing .env file
    let envContent = '';
    try {
      envContent = fs.readFileSync(envPath, 'utf8');
    } catch (error) {
      console.error('Error reading .env file:', error.message);
      console.log('Creating a new .env file...');
    }
    
    // Update or add LiveKit configuration
    let newEnvContent = envContent;
    
    if (envContent.includes('LIVEKIT_URL=')) {
      newEnvContent = newEnvContent.replace(/LIVEKIT_URL=.*/g, `LIVEKIT_URL=${liveKitUrl}`);
    } else {
      newEnvContent += `\n# LiveKit configuration\nLIVEKIT_URL=${liveKitUrl}\n`;
    }
    
    if (envContent.includes('LIVEKIT_API_KEY=')) {
      newEnvContent = newEnvContent.replace(/LIVEKIT_API_KEY=.*/g, `LIVEKIT_API_KEY=${liveKitApiKey}`);
    } else if (!envContent.includes('LIVEKIT_URL=')) {
      newEnvContent += `LIVEKIT_API_KEY=${liveKitApiKey}\n`;
    }
    
    if (envContent.includes('LIVEKIT_API_SECRET=')) {
      newEnvContent = newEnvContent.replace(/LIVEKIT_API_SECRET=.*/g, `LIVEKIT_API_SECRET=${liveKitApiSecret}`);
    } else if (!envContent.includes('LIVEKIT_URL=')) {
      newEnvContent += `LIVEKIT_API_SECRET=${liveKitApiSecret}\n`;
    }
    
    // Add USE_STREAM_ELEMENT flag
    if (!envContent.includes('USE_STREAM_ELEMENT=')) {
      newEnvContent += `\n# Enable Stream element for Twilio-LiveKit integration\nUSE_STREAM_ELEMENT=false\n`;
    }
    
    // Write updated content to .env file
    try {
      fs.writeFileSync(envPath, newEnvContent);
      console.log('\nLiveKit configuration has been added to your .env file.');
    } catch (error) {
      console.error('Error writing to .env file:', error.message);
    }
  }
  
  // Provide guidance on setting up LiveKit for Twilio
  console.log('\n=== LiveKit-Twilio Integration Guide ===');
  console.log('To enable LiveKit integration with Twilio, you need to:');
  console.log('1. Make sure your LiveKit server is publicly accessible');
  console.log('2. Configure LiveKit to accept WebSocket connections from Twilio');
  console.log('3. Set USE_STREAM_ELEMENT=true in your .env file when ready to test');
  console.log('\nFor testing purposes, we\'ve set up a fallback using the Dial verb.');
  console.log('This allows you to make calls while you set up the LiveKit integration.');
  console.log('\nWhen you\'re ready to test the LiveKit integration:');
  console.log('1. Set USE_STREAM_ELEMENT=true in your .env file');
  console.log('2. Restart your server');
  console.log('3. Make a test call');
  console.log('\nIf you encounter WebSocket handshake errors, check that:');
  console.log('- Your LiveKit server is publicly accessible');
  console.log('- The /twilio endpoint is properly configured on your LiveKit server');
  console.log('- Your LiveKit API key and secret are correct');
  
  rl.close();
}

// Run the main function
main().catch(error => {
  console.error('An error occurred:', error);
  rl.close();
});
