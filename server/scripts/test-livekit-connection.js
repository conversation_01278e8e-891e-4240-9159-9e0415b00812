#!/usr/bin/env node

/**
 * This script tests the connection to a LiveKit server.
 * It attempts to establish a WebSocket connection and reports the result.
 */

import WebSocket from 'ws';
import readline from 'readline';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Load environment variables from .env file
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.join(__dirname, '..', '.env') });

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to prompt for input
const prompt = (question) => {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
};

// Main function
async function main() {
  console.log('\n=== LiveKit Connection Test ===');
  
  // Get LiveKit URL from environment or prompt
  let liveKitUrl = process.env.LIVEKIT_URL;
  if (!liveKitUrl) {
    liveKitUrl = await prompt('Enter your LiveKit server URL (e.g., wss://example.livekit.cloud): ');
  } else {
    console.log(`Using LiveKit URL from environment: ${liveKitUrl}`);
  }
  
  console.log(`\nTesting connection to: ${liveKitUrl}`);
  console.log('Attempting to establish WebSocket connection...');
  
  try {
    // Create a WebSocket connection
    const ws = new WebSocket(liveKitUrl);
    
    // Set a timeout
    const timeout = setTimeout(() => {
      console.log('\n❌ Connection attempt timed out after 5 seconds');
      console.log('This could indicate:');
      console.log('  - The LiveKit server is not reachable');
      console.log('  - There might be network issues');
      console.log('  - The URL might be incorrect');
      ws.terminate();
      rl.close();
      process.exit(1);
    }, 5000);
    
    // Handle connection open
    ws.on('open', () => {
      clearTimeout(timeout);
      console.log('\n✅ Successfully connected to LiveKit server!');
      console.log('The WebSocket connection was established successfully.');
      console.log('This confirms that your LiveKit server is accessible via WebSocket.');
      ws.close();
      rl.close();
    });
    
    // Handle errors
    ws.on('error', (error) => {
      clearTimeout(timeout);
      console.log('\n❌ Failed to connect to LiveKit server');
      console.log(`Error: ${error.message}`);
      console.log('\nPossible issues:');
      console.log('  - The LiveKit server URL might be incorrect');
      console.log('  - The LiveKit server might not be running');
      console.log('  - There might be network issues or firewall restrictions');
      console.log('  - The server might not support WebSocket connections');
      rl.close();
      process.exit(1);
    });
  } catch (error) {
    console.log('\n❌ Error creating WebSocket connection');
    console.log(`Error: ${error.message}`);
    rl.close();
    process.exit(1);
  }
}

// Run the main function
main().catch(error => {
  console.error('An unexpected error occurred:', error);
  rl.close();
  process.exit(1);
});
