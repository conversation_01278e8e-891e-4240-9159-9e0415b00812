#!/usr/bin/env node

/**
 * This script helps set up Twilio credentials for the application.
 * It prompts the user for their Twilio credentials and adds them to the .env file.
 */

import fs from 'fs';
import readline from 'readline';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const envPath = path.join(__dirname, '..', '.env');

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Function to prompt for input
const prompt = (question) => {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
};

// Main function
async function main() {
  console.log('\n=== Twilio Setup ===');
  console.log('This script will help you set up Twilio credentials for your application.');
  console.log('You can find your Twilio credentials at https://www.twilio.com/console\n');

  const accountSid = await prompt('Enter your Twilio Account SID: ');
  const authToken = await prompt('Enter your Twilio Auth Token: ');
  const phoneNumber = await prompt('Enter your Twilio Phone Number (with country code, e.g., +**********): ');

  // Read existing .env file
  let envContent = '';
  try {
    envContent = fs.readFileSync(envPath, 'utf8');
  } catch (error) {
    console.error('Error reading .env file:', error.message);
    console.log('Creating a new .env file...');
  }

  // Check if Twilio credentials already exist
  const hasTwilioAccountSid = envContent.includes('TWILIO_ACCOUNT_SID=');
  const hasTwilioAuthToken = envContent.includes('TWILIO_AUTH_TOKEN=');
  const hasTwilioPhoneNumber = envContent.includes('TWILIO_PHONE_NUMBER=');

  // Update or add Twilio credentials
  let newEnvContent = envContent;
  
  if (hasTwilioAccountSid) {
    newEnvContent = newEnvContent.replace(/TWILIO_ACCOUNT_SID=.*/g, `TWILIO_ACCOUNT_SID=${accountSid}`);
  } else {
    newEnvContent += `\n# Twilio credentials\nTWILIO_ACCOUNT_SID=${accountSid}\n`;
  }
  
  if (hasTwilioAuthToken) {
    newEnvContent = newEnvContent.replace(/TWILIO_AUTH_TOKEN=.*/g, `TWILIO_AUTH_TOKEN=${authToken}`);
  } else if (!hasTwilioAccountSid) {
    newEnvContent += `TWILIO_AUTH_TOKEN=${authToken}\n`;
  }
  
  if (hasTwilioPhoneNumber) {
    newEnvContent = newEnvContent.replace(/TWILIO_PHONE_NUMBER=.*/g, `TWILIO_PHONE_NUMBER=${phoneNumber}`);
  } else if (!hasTwilioAccountSid) {
    newEnvContent += `TWILIO_PHONE_NUMBER=${phoneNumber}\n`;
  }

  // Write updated content to .env file
  try {
    fs.writeFileSync(envPath, newEnvContent);
    console.log('\nTwilio credentials have been added to your .env file.');
    console.log('You can now use Twilio features in your application.');
  } catch (error) {
    console.error('Error writing to .env file:', error.message);
  }

  rl.close();
}

// Run the main function
main().catch(error => {
  console.error('An error occurred:', error);
  rl.close();
});
