-- Create<PERSON><PERSON>
CREATE TYPE "WhisperMode" AS ENUM ('AI_TO_USER', 'USER_TO_AI', 'NORMAL');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "AgentStatus" AS ENUM ('DISCONNECTED', 'CONNECTING', 'CONNECTED', 'ERROR');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "InteractionType" AS ENUM ('AI_TO_USER_WHISPER', 'USER_TO_AI_WHISPER', 'TRIGGER_DETECTED', 'MODE_CHANGE', 'AGENT_STATUS_CHANGE');

-- AlterTable
ALTER TABLE "Call" ADD COLUMN     "aiAgentStatus" "AgentStatus" NOT NULL DEFAULT 'DISCONNECTED',
ADD COLUMN     "audioRoutingConfig" JSONB,
ADD COLUMN     "isWhisperCall" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "livekitRoomName" TEXT,
ADD COLUMN     "participantData" JSONB,
ADD COLUMN     "sipTrunkInfo" JSONB,
ADD COLUMN     "whisperInteractions" JSONB[],
ADD COLUMN     "whisperMode" "WhisperMode" NOT NULL DEFAULT 'AI_TO_USER';

-- CreateTable
CREATE TABLE "WhisperSession" (
    "id" TEXT NOT NULL,
    "callId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "contactId" TEXT NOT NULL,
    "livekitRoomName" TEXT NOT NULL,
    "sessionStartTime" TIMESTAMP(3) NOT NULL,
    "sessionEndTime" TIMESTAMP(3),
    "totalDuration" INTEGER,
    "whisperMode" "WhisperMode" NOT NULL,
    "participantData" JSONB NOT NULL,
    "audioMetrics" JSONB,
    "goalProgress" JSONB[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "WhisperSession_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WhisperInteraction" (
    "id" TEXT NOT NULL,
    "sessionId" TEXT NOT NULL,
    "interactionType" "InteractionType" NOT NULL,
    "userSpeech" TEXT,
    "callerSpeech" TEXT,
    "aiResponse" TEXT,
    "triggerGoals" JSONB[],
    "responseTime" INTEGER,
    "confidence" DOUBLE PRECISION,
    "timestamp" TIMESTAMP(3) NOT NULL,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "WhisperInteraction_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "WhisperSession_callId_idx" ON "WhisperSession"("callId");

-- CreateIndex
CREATE INDEX "WhisperSession_userId_idx" ON "WhisperSession"("userId");

-- CreateIndex
CREATE INDEX "WhisperSession_contactId_idx" ON "WhisperSession"("contactId");

-- CreateIndex
CREATE INDEX "WhisperSession_livekitRoomName_idx" ON "WhisperSession"("livekitRoomName");

-- CreateIndex
CREATE INDEX "WhisperInteraction_sessionId_idx" ON "WhisperInteraction"("sessionId");

-- CreateIndex
CREATE INDEX "WhisperInteraction_interactionType_idx" ON "WhisperInteraction"("interactionType");

-- CreateIndex
CREATE INDEX "WhisperInteraction_timestamp_idx" ON "WhisperInteraction"("timestamp");

-- CreateIndex
CREATE INDEX "Call_livekitRoomName_idx" ON "Call"("livekitRoomName");

-- CreateIndex
CREATE INDEX "Call_isWhisperCall_idx" ON "Call"("isWhisperCall");

-- AddForeignKey
ALTER TABLE "WhisperSession" ADD CONSTRAINT "WhisperSession_callId_fkey" FOREIGN KEY ("callId") REFERENCES "Call"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WhisperSession" ADD CONSTRAINT "WhisperSession_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WhisperSession" ADD CONSTRAINT "WhisperSession_contactId_fkey" FOREIGN KEY ("contactId") REFERENCES "Contact"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WhisperInteraction" ADD CONSTRAINT "WhisperInteraction_sessionId_fkey" FOREIGN KEY ("sessionId") REFERENCES "WhisperSession"("id") ON DELETE CASCADE ON UPDATE CASCADE;
