// This is your Prisma schema file
datasource db {
    provider = "postgresql"
    url      = env("DATABASE_URL")
}

generator client {
    provider = "prisma-client-js"
}

// User model
model User {
    id                 String               @id @default(cuid())
    email              String               @unique
    password           String // Added password field
    name               String
    company            String?
    isPremium          Boolean              @default(false)
    role               UserRole             @default(USER)
    phoneNumber        String?
    createdAt          DateTime             @default(now())
    updatedAt          DateTime             @updatedAt
    settings           Json // Stores user settings as JSON
    calEventTypeId     Int?
    calApiKey          String?
    publicApiKey       String?
    privateSecretKey   String?
    assistants         Assistant[]
    contacts           Contact[]
    calls              Call[]
    templates          WhisperTemplate[]
    campaigns          Campaign[]
    resources          Resource[]
    assistantTemplates AssistantTemplate[]
    campaignTemplates  CampaignTemplate[]
    teamMemberships    CampaignTeamMember[]
    campaignComments   CampaignComment[]
    whisperSessions    WhisperSession[]
    UserPhoneNumber    UserPhoneNumber[]
}

enum UserRole {
    ADMIN
    USER
}

// Assistant model
model Assistant {
    id           String   @id @default(cuid())
    userId       String
    user         User     @relation(fields: [userId], references: [id])
    name         String
    modes        String[] // Array of modes (web, voice)
    firstMessage String
    systemPrompt String   @db.Text
    provider     String
    model        String
    tools        Json // Stores tools configuration as JSON
    voice        Json // Stores voice settings as JSON
    isActive     Boolean  @default(true)
    createdAt    DateTime @default(now())
    updatedAt    DateTime @updatedAt
    calls        Call[]

    userPhoneNumberId String?
    userPhoneNumber   UserPhoneNumber? @relation(fields: [userPhoneNumberId], references: [id], onDelete: SetNull)

    @@index([userPhoneNumberId])
}

// UserPhoneNumber model (Added for this subtask)
model UserPhoneNumber {
    id           String      @id @default(cuid())
    userId       String
    user         User        @relation(fields: [userId], references: [id])
    phoneNumber  String      @unique // The actual phone number in E.164 format
    friendlyName String? // User-defined name for the number
    isDefault    Boolean     @default(false) // Whether this is the default number for the user
    isVerified   Boolean     @default(false)
    capabilities String[] // e.g., ["voice", "sms"]
    providerId   String? // e.g., Twilio SID for the number
    createdAt    DateTime    @default(now())
    updatedAt    DateTime    @updatedAt
    assistants   Assistant[] // Back-relation to Assistant

    @@index([userId])
}

// Contact model
model Contact {
    id                String            @id @default(cuid())
    userId            String
    name              String
    email             String
    phone             String
    type              ContactType
    transparencyLevel TransparencyLevel
    subcategory       String?
    customSubcategory String?
    campaignId        String?
    tags              String[]
    notes             String?           @db.Text
    createdAt         DateTime          @default(now())
    updatedAt         DateTime          @updatedAt
    lastContactedAt   DateTime?
    calls             Call[]
    campaign          Campaign?         @relation(fields: [campaignId], references: [id])
    user              User              @relation(fields: [userId], references: [id])
    whisperSessions   WhisperSession[]

    @@index([userId])
    @@index([campaignId])
}

enum ContactType {
    PERSONAL
    BUSINESS
}

enum TransparencyLevel {
    FULL
    PARTIAL
    NONE
}

// Call model
model Call {
    id                  String           @id @default(cuid())
    userId              String
    contactId           String
    assistantId         String
    startTime           DateTime
    endTime             DateTime?
    duration            Int? // Duration in seconds
    status              CallStatus
    twilioSid           String? // Twilio call SID for outbound calls
    recording           Json? // Stores recording info as JSON
    transcript          Json[] // Array of transcript entries
    goals               Json[] // Array of call goals
    metrics             Json // Stores call metrics as JSON
    notes               String?          @db.Text
    // Enhanced whisper functionality fields
    isWhisperCall       Boolean          @default(false) // Whether this is a whisper-enabled call
    livekitRoomName     String? // LiveKit room name for the call
    whisperMode         WhisperMode      @default(AI_TO_USER) // Current whisper mode
    participantData     Json? // Stores participant identities and metadata
    audioRoutingConfig  Json? // Stores current audio routing configuration
    aiAgentStatus       AgentStatus      @default(DISCONNECTED) // Status of AI agent
    whisperInteractions Json[] // Array of whisper interactions for analytics
    sipTrunkInfo        Json? // SIP trunk information for telephony calls
    createdAt           DateTime         @default(now())
    updatedAt           DateTime         @updatedAt
    user                User             @relation(fields: [userId], references: [id])
    contact             Contact          @relation(fields: [contactId], references: [id])
    assistant           Assistant        @relation(fields: [assistantId], references: [id])
    whisperSessions     WhisperSession[]

    @@index([userId])
    @@index([contactId])
    @@index([assistantId])
    @@index([livekitRoomName])
    @@index([isWhisperCall])
}

enum CallStatus {
    SCHEDULED
    IN_PROGRESS
    COMPLETED
    FAILED
}

enum WhisperMode {
    AI_TO_USER // AI provides whisper suggestions to the user
    USER_TO_AI // User can whisper instructions to the AI
    NORMAL // Normal conference mode - everyone hears everyone
}

enum AgentStatus {
    DISCONNECTED // AI agent is not connected
    CONNECTING // AI agent is connecting to the room
    CONNECTED // AI agent is connected and active
    ERROR // AI agent encountered an error
}

// WhisperSession model for tracking detailed whisper analytics
model WhisperSession {
    id               String               @id @default(cuid())
    callId           String
    userId           String
    contactId        String
    livekitRoomName  String
    sessionStartTime DateTime
    sessionEndTime   DateTime?
    totalDuration    Int? // Duration in seconds
    whisperMode      WhisperMode
    participantData  Json // Stores participant identities and metadata
    interactions     WhisperInteraction[]
    audioMetrics     Json? // Audio quality metrics
    goalProgress     Json[] // Progress on goals during the session
    createdAt        DateTime             @default(now())
    updatedAt        DateTime             @updatedAt
    call             Call                 @relation(fields: [callId], references: [id], onDelete: Cascade)
    user             User                 @relation(fields: [userId], references: [id])
    contact          Contact              @relation(fields: [contactId], references: [id])

    @@index([callId])
    @@index([userId])
    @@index([contactId])
    @@index([livekitRoomName])
}

// WhisperInteraction model for tracking individual whisper exchanges
model WhisperInteraction {
    id              String          @id @default(cuid())
    sessionId       String
    interactionType InteractionType
    userSpeech      String?         @db.Text // What the user said (if applicable)
    callerSpeech    String?         @db.Text // What the caller said (if applicable)
    aiResponse      String?         @db.Text // AI's whisper response
    triggerGoals    Json[] // Goals that triggered this interaction
    responseTime    Int? // Time taken to generate response (ms)
    confidence      Float? // Confidence score of the trigger detection
    timestamp       DateTime
    metadata        Json? // Additional metadata
    createdAt       DateTime        @default(now())
    session         WhisperSession  @relation(fields: [sessionId], references: [id], onDelete: Cascade)

    @@index([sessionId])
    @@index([interactionType])
    @@index([timestamp])
}

enum InteractionType {
    AI_TO_USER_WHISPER // AI provided a whisper to the user
    USER_TO_AI_WHISPER // User whispered to the AI
    TRIGGER_DETECTED // A goal trigger was detected
    MODE_CHANGE // Whisper mode was changed
    AGENT_STATUS_CHANGE // AI agent status changed
}

// WhisperTemplate model
model WhisperTemplate {
    id             String       @id @default(cuid())
    userId         String
    name           String
    type           TemplateType
    systemPrompt   String       @db.Text
    editablePrompt String       @db.Text
    isSystem       Boolean      @default(false)
    isHidden       Boolean      @default(false)
    tags           String[]
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt
    user           User         @relation(fields: [userId], references: [id])

    @@index([userId])
}

enum TemplateType {
    BUSINESS
    PERSONAL
}

// Assistant Template model
model AssistantTemplate {
    id           String   @id @default(cuid())
    name         String
    description  String?
    tags         String[]
    type         String // 'system' or 'user'
    systemPrompt String   @db.Text
    firstMessage String
    tools        Json // Array of tool configurations
    isActive     Boolean  @default(true)
    createdAt    DateTime @default(now())
    updatedAt    DateTime @updatedAt
    createdBy    String? // userId of template creator (null for system templates)
    user         User?    @relation(fields: [createdBy], references: [id])
}

// Campaign model
model Campaign {
    id                 String               @id @default(cuid())
    userId             String
    name               String
    description        String?              @db.Text
    startDate          DateTime
    endDate            DateTime?
    status             CampaignStatus
    contacts           Contact[]
    goals              CampaignGoal[]
    metrics            Json // Stores campaign metrics as JSON
    automationSettings Json? // Stores automation settings as JSON
    callScript         String?              @db.Text
    assistantId        String?
    phoneNumberId      String?
    channelSettings    ChannelSettings?
    teamMembers        CampaignTeamMember[]
    comments           CampaignComment[]
    createdAt          DateTime             @default(now())
    updatedAt          DateTime             @updatedAt
    user               User                 @relation(fields: [userId], references: [id])
    template           CampaignTemplate?    @relation(fields: [templateId], references: [id])
    templateId         String?

    @@index([userId])
    @@index([templateId])
}

enum CampaignStatus {
    DRAFT
    SCHEDULED
    ACTIVE
    COMPLETED
    CANCELLED
    PAUSED
}

// Campaign Goal model
model CampaignGoal {
    id          String   @id @default(cuid())
    campaignId  String
    title       String
    description String?  @db.Text
    target      Int
    progress    Int      @default(0)
    completed   Boolean  @default(false)
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt
    campaign    Campaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)

    @@index([campaignId])
}

// Campaign Template model
model CampaignTemplate {
    id           String     @id @default(cuid())
    userId       String?
    name         String
    description  String?    @db.Text
    isSystem     Boolean    @default(false)
    campaignData Json // Stores template data as JSON
    createdAt    DateTime   @default(now())
    updatedAt    DateTime   @updatedAt
    user         User?      @relation(fields: [userId], references: [id])
    campaigns    Campaign[]

    @@index([userId])
}

// Channel Settings model
model ChannelSettings {
    id         String   @id @default(cuid())
    campaignId String   @unique
    voice      Json? // Voice channel settings
    sms        Json? // SMS channel settings
    email      Json? // Email channel settings
    createdAt  DateTime @default(now())
    updatedAt  DateTime @updatedAt
    campaign   Campaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
}

// Campaign Team Member model
model CampaignTeamMember {
    id           String           @id @default(cuid())
    campaignId   String
    userId       String
    role         TeamMemberRole
    invitedEmail String?
    status       InvitationStatus @default(PENDING)
    lastActive   DateTime?
    createdAt    DateTime         @default(now())
    updatedAt    DateTime         @updatedAt
    campaign     Campaign         @relation(fields: [campaignId], references: [id], onDelete: Cascade)
    user         User             @relation(fields: [userId], references: [id])

    @@unique([campaignId, userId])
    @@index([campaignId])
    @@index([userId])
}

enum TeamMemberRole {
    OWNER
    ADMIN
    EDITOR
    VIEWER
}

enum InvitationStatus {
    PENDING
    ACCEPTED
    DECLINED
}

// Campaign Comment model
model CampaignComment {
    id          String   @id @default(cuid())
    campaignId  String
    userId      String
    content     String   @db.Text
    attachments String[]
    createdAt   DateTime @default(now())
    updatedAt   DateTime @updatedAt
    campaign    Campaign @relation(fields: [campaignId], references: [id], onDelete: Cascade)
    user        User     @relation(fields: [userId], references: [id])

    @@index([campaignId])
    @@index([userId])
}

// Resource model
model Resource {
    id          String       @id @default(cuid())
    userId      String
    title       String
    type        ResourceType
    url         String
    description String?      @db.Text
    tags        String[]
    isPublic    Boolean      @default(false)
    createdAt   DateTime     @default(now())
    updatedAt   DateTime     @updatedAt
    user        User         @relation(fields: [userId], references: [id])

    @@index([userId])
}

enum ResourceType {
    DOCUMENTATION
    GUIDE
    TUTORIAL
    WHITEPAPER
    VIDEO
}
