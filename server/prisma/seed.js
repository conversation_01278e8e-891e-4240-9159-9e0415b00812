import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  // Create admin user
  const adminPassword =
    "$2a$10$89.tRyrGAAxSFxM/Ij.Fru3TZDHrnoJ83zQ2u9ODVnJEnk93kr1pW"; // await bcrypt.hash("admin123", 10);
  const admin = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      password: adminPassword,
      name: "Admin User",
      role: "ADMIN",
      settings: {
        defaultTransparencyLevel: "FULL",
        recordingEnabled: true,
        webSearchEnabled: true,
        preferredVoice: "male",
      },
    },
  });

  // Create demo user
  const demoPassword =
    "$2a$10$JtiYJMBC82xlRwsOeCmfnO0x7VqjjfJOrEPtnLylln5CAS97LDRsy"; //await bcrypt.hash("demo123", 10);
  const demoUser = await prisma.user.upsert({
    where: { email: "<EMAIL>" },
    update: {},
    create: {
      email: "<EMAIL>",
      password: demoPassword,
      name: "Demo User",
      company: "Demo Company",
      role: "USER",
      settings: {
        defaultTransparencyLevel: "PARTIAL",
        recordingEnabled: true,
        webSearchEnabled: false,
        preferredVoice: "female",
      },
    },
  });

  // Create system whisper templates
  const systemTemplates = [
    {
      name: "Professional Tone",
      type: "BUSINESS",
      systemPrompt:
        "Maintain a professional and courteous tone throughout the conversation.",
      editablePrompt:
        "Remind me to keep the conversation professional and focused on business objectives.",
      isSystem: true,
      tags: ["professional", "business", "communication"],
    },
    {
      name: "Customer Support",
      type: "BUSINESS",
      systemPrompt:
        "Focus on understanding and resolving customer issues efficiently.",
      editablePrompt:
        "Guide me to ask relevant questions and provide helpful solutions.",
      isSystem: true,
      tags: ["support", "customer service", "problem-solving"],
    },
    {
      name: "Sales Approach",
      type: "BUSINESS",
      systemPrompt:
        "Help identify opportunities and present solutions effectively.",
      editablePrompt:
        "Suggest ways to highlight product benefits and address customer needs.",
      isSystem: true,
      tags: ["sales", "negotiation", "persuasion"],
    },
    {
      name: "Empathetic Listener",
      type: "PERSONAL",
      systemPrompt: "Focus on understanding and acknowledging emotions.",
      editablePrompt:
        "Remind me to show empathy and validate feelings during the conversation.",
      isSystem: true,
      tags: ["empathy", "personal", "emotional intelligence"],
    },
  ];

  for (const template of systemTemplates) {
    await prisma.whisperTemplate.create({
      data: {
        ...template,
        userId: admin.id,
      },
    });
  }

  // Create demo resources
  const resources = [
    {
      title: "Getting Started Guide",
      type: "DOCUMENTATION",
      url: "https://docs.talkai247.com/getting-started",
      description: "Learn how to set up and use Talkai247 effectively.",
      tags: ["guide", "setup", "basics"],
      isPublic: true,
    },
    {
      title: "Best Practices for AI Calls",
      type: "GUIDE",
      url: "https://docs.talkai247.com/best-practices",
      description: "Tips and tricks for making the most of your AI calls.",
      tags: ["tips", "optimization", "quality"],
      isPublic: true,
    },
  ];

  for (const resource of resources) {
    await prisma.resource.create({
      data: {
        ...resource,
        userId: admin.id,
      },
    });
  }

  // Create demo contacts
  const contacts = [
    {
      name: "John Smith",
      email: "<EMAIL>",
      phone: "+***********",
      type: "BUSINESS",
      transparencyLevel: "FULL",
      tags: ["client", "tech"],
      notes: "Key decision maker at ABC Corp",
      userId: demoUser.id,
    },
    {
      name: "Sarah Johnson",
      email: "<EMAIL>",
      phone: "+***********",
      type: "BUSINESS",
      transparencyLevel: "PARTIAL",
      tags: ["prospect", "healthcare"],
      notes: "Interested in our enterprise solution",
      userId: demoUser.id,
    },
    {
      name: "Michael Brown",
      email: "<EMAIL>",
      phone: "+***********",
      type: "PERSONAL",
      transparencyLevel: "NONE",
      tags: ["friend", "referral"],
      notes: "Met at networking event",
      userId: demoUser.id,
    },
  ];

  const createdContacts = [];
  for (const contact of contacts) {
    const createdContact = await prisma.contact.create({
      data: contact,
    });
    createdContacts.push(createdContact);
  }

  // Create campaign templates
  const campaignTemplates = [
    {
      name: "Sales Outreach",
      description: "Standard sales outreach campaign with follow-ups",
      isSystem: true,
      campaignData: {
        description: "A structured campaign to reach out to potential clients",
        goals: [
          {
            title: "Initial Contact",
            description: "Make first contact with all prospects",
            target: 100,
          },
          {
            title: "Meetings Scheduled",
            description: "Schedule discovery calls",
            target: 30,
          },
          {
            title: "Proposals Sent",
            description: "Send proposals to qualified leads",
            target: 15,
          },
        ],
        channelSettings: {
          voice: {
            enabled: true,
            message:
              "Hello, this is {{user.name}} from {{user.company}}. I'm calling to discuss how we can help with your {{contact.tags[0]}} needs.",
          },
          sms: {
            enabled: true,
            message:
              "Hi {{contact.name}}, this is {{user.name}} from {{user.company}}. I'd like to schedule a call to discuss our solutions. What time works for you?",
          },
          email: {
            enabled: true,
            template: "sales_outreach_email",
          },
        },
        automationSettings: {
          schedule: {
            recurring: {
              pattern: "WEEKDAY_MORNING",
            },
          },
          callSettings: {
            maxAttempts: 3,
            retryDelay: 2,
          },
        },
      },
      userId: admin.id,
    },
    {
      name: "Customer Check-in",
      description: "Regular check-in with existing customers",
      isSystem: true,
      campaignData: {
        description: "Maintain relationships with current customers",
        goals: [
          {
            title: "Satisfaction Check",
            description: "Verify customer satisfaction",
            target: 100,
          },
          {
            title: "Upsell Opportunities",
            description: "Identify upsell opportunities",
            target: 20,
          },
        ],
        channelSettings: {
          voice: {
            enabled: true,
            message:
              "Hello {{contact.name}}, this is {{user.name}} from {{user.company}}. I'm calling to check in on how things are going with our service.",
          },
          email: {
            enabled: true,
            template: "customer_check_in_email",
          },
        },
        automationSettings: {
          schedule: {
            recurring: {
              pattern: "MONTHLY",
            },
          },
        },
      },
      userId: admin.id,
    },
  ];

  for (const template of campaignTemplates) {
    await prisma.campaignTemplate.create({
      data: template,
    });
  }

  // Create a demo campaign
  const demoCampaign = await prisma.campaign.create({
    data: {
      userId: demoUser.id,
      name: "Q4 Sales Outreach",
      description: "Targeting high-value prospects for Q4 sales push",
      startDate: new Date(),
      endDate: new Date(new Date().setMonth(new Date().getMonth() + 3)),
      status: "ACTIVE",
      metrics: {
        totalCalls: 12,
        successfulCalls: 8,
        failedCalls: 4,
        averageDuration: 320,
        averageSentiment: 0.7,
      },
      automationSettings: {
        schedule: {
          recurring: {
            pattern: "WEEKDAY_MORNING",
          },
        },
        callSettings: {
          maxAttempts: 3,
          retryDelay: 2,
        },
      },
      callScript:
        "Hello {{contact.name}}, this is {{user.name}} from {{user.company}}. I'm calling to discuss our new solution that can help with your {{contact.tags[0]}} needs. Do you have a few minutes to talk?",
      contacts: {
        connect: createdContacts.map((contact) => ({ id: contact.id })),
      },
      teamMembers: {
        create: {
          userId: demoUser.id,
          role: "OWNER",
          status: "ACCEPTED",
        },
      },
    },
  });

  // Add goals to the campaign
  await prisma.campaignGoal.createMany({
    data: [
      {
        campaignId: demoCampaign.id,
        title: "Initial Calls",
        description: "Complete first call with all prospects",
        target: 50,
        progress: 12,
        completed: false,
      },
      {
        campaignId: demoCampaign.id,
        title: "Meetings Scheduled",
        description: "Schedule follow-up meetings",
        target: 20,
        progress: 5,
        completed: false,
      },
      {
        campaignId: demoCampaign.id,
        title: "Deals Closed",
        description: "Close deals with qualified prospects",
        target: 10,
        progress: 2,
        completed: false,
      },
    ],
  });

  // Add channel settings
  await prisma.channelSettings.create({
    data: {
      campaignId: demoCampaign.id,
      voice: {
        enabled: true,
        message:
          "Hello {{contact.name}}, this is {{user.name}} from {{user.company}}. I'm calling to discuss our new solution.",
      },
      sms: {
        enabled: true,
        message:
          "Hi {{contact.name}}, this is {{user.name}} from {{user.company}}. I'd like to schedule a call to discuss our solutions. What time works for you?",
      },
      email: {
        enabled: true,
        template: "sales_outreach_email",
      },
    },
  });

  // Add comments to the campaign
  await prisma.campaignComment.createMany({
    data: [
      {
        campaignId: demoCampaign.id,
        userId: demoUser.id,
        content:
          "Campaign is off to a great start. We've already scheduled 5 meetings.",
        attachments: [],
      },
      {
        campaignId: demoCampaign.id,
        userId: demoUser.id,
        content:
          "Updated the call script to focus more on ROI. Let's see if this improves our conversion rate.",
        attachments: [],
      },
    ],
  });

  console.log("Database seeded successfully!");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
