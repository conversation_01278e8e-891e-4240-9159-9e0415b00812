version: '3.8'

services:
  whisper-agent:
    build: .
    container_name: whisper-ai-agent
    restart: unless-stopped
    environment:
      # LiveKit Configuration
      - LIVEKIT_URL=${LIVEKIT_URL}
      - LIVEKIT_API_KEY=${LIVEKIT_API_KEY}
      - LIVEKIT_API_SECRET=${LIVEKIT_API_SECRET}
      
      # OpenAI Configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-4}
      
      # Deepgram Configuration
      - DEEPGRAM_API_KEY=${DEEPGRAM_API_KEY}
      
      # ElevenLabs Configuration
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      
      # Database Configuration
      - DATABASE_URL=${DATABASE_URL}
      
      # Server Configuration
      - SERVER_API_URL=${SERVER_API_URL:-http://host.docker.internal:3030/api/v1}
      
      # Logging Configuration
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FORMAT=${LOG_FORMAT:-json}
      
      # Agent Configuration
      - AGENT_NAME=${AGENT_NAME:-whisper-ai-assistant}
      - AGENT_VERSION=${AGENT_VERSION:-1.0.0}
      
      # Whisper Feature Configuration
      - WHISPER_ENABLED=${WHISPER_ENABLED:-true}
      - WHISPER_TRIGGER_THRESHOLD=${WHISPER_TRIGGER_THRESHOLD:-0.7}
      - WHISPER_RESPONSE_DELAY=${WHISPER_RESPONSE_DELAY:-1.0}
    
    volumes:
      - ./logs:/app/logs
    
    networks:
      - whisper-network
    
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  whisper-network:
    driver: bridge
