#!/usr/bin/env python3
"""
Startup script for the Whisper AI Agent
"""
import asyncio
import signal
import sys
import logging
from typing import Optional

from livekit.agents import WorkerOptions, cli
from livekit.plugins import silero

from whisper_agent import entrypoint
from config import config
from utils.logger import setup_logger

# Setup logging
logger = setup_logger(__name__)

class WhisperAgentRunner:
    """Runner for the Whisper AI Agent"""
    
    def __init__(self):
        self.worker_options: Optional[WorkerOptions] = None
        self.shutdown_event = asyncio.Event()
        
    async def setup(self):
        """Set up the agent runner"""
        try:
            logger.info("Setting up Whisper AI Agent...")
            
            # Validate configuration
            config.validate()
            logger.info("Configuration validated successfully")
            
            # Prewarm VAD model
            logger.info("Prewarming VAD model...")
            vad = await silero.VAD.load()
            logger.info("VAD model loaded successfully")
            
            # Create worker options
            self.worker_options = WorkerOptions(
                entrypoint_fnc=entrypoint,
                prewarm_fnc=lambda: vad,
                agent_name=config.AGENT_NAME,
            )
            
            logger.info("Whisper AI Agent setup completed")
            
        except Exception as e:
            logger.error(f"Failed to set up Whisper AI Agent: {e}")
            raise
    
    async def run(self):
        """Run the agent"""
        try:
            if not self.worker_options:
                await self.setup()
            
            logger.info(f"Starting Whisper AI Agent v{config.AGENT_VERSION}")
            logger.info(f"LiveKit URL: {config.LIVEKIT_URL}")
            logger.info(f"Agent Name: {config.AGENT_NAME}")
            
            # Set up signal handlers
            self._setup_signal_handlers()
            
            # Run the agent
            await cli.run_app(self.worker_options)
            
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, shutting down...")
            await self.shutdown()
        except Exception as e:
            logger.error(f"Error running Whisper AI Agent: {e}")
            raise
    
    def _setup_signal_handlers(self):
        """Set up signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating shutdown...")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def shutdown(self):
        """Graceful shutdown"""
        try:
            logger.info("Shutting down Whisper AI Agent...")
            self.shutdown_event.set()
            
            # Add any cleanup logic here
            
            logger.info("Whisper AI Agent shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

async def main():
    """Main entry point"""
    runner = WhisperAgentRunner()
    
    try:
        await runner.run()
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # Check Python version
    if sys.version_info < (3, 8):
        print("Python 3.8 or higher is required")
        sys.exit(1)
    
    # Run the agent
    asyncio.run(main())
