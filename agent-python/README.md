# Whisper AI Agent

A Python-based AI agent for the Real-Time AI Whisper Feature using LiveKit and Twilio integration.

## Overview

This agent implements the architecture described in `livekit.md` for providing real-time AI whisper suggestions during phone calls. The agent acts as a hidden participant in LiveKit rooms and provides contextual advice based on predefined goals.

## Features

- **Real-time Speech Processing**: Continuous audio stream processing with low-latency STT
- **Intelligent Whisper Generation**: Context-aware AI responses based on conversation goals
- **Three-Participant Architecture**: Supports User, Caller, and AI Assistant participants
- **Selective Audio Routing**: Server-enforced audio routing for different whisper modes
- **Goal-Based Triggers**: Automatic whisper generation based on conversation triggers
- **Multiple AI Providers**: Support for OpenAI, Deepgram, and ElevenLabs

## Architecture

The agent follows the three-participant room architecture:

1. **User**: The person using the whisper feature (hears Caller + AI)
2. **Caller**: The external person on the phone (hears User only)
3. **AI Assistant**: Hidden participant that provides whispers (hears User + Caller)

## Prerequisites

- Python 3.8 or higher
- LiveKit account and credentials
- OpenAI API key
- Deepgram API key (for STT)
- ElevenLabs API key (for TTS)
- PostgreSQL database (for goals and analytics)

## Installation

### 1. Clone and Setup

```bash
cd agent-python
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 2. Environment Configuration

Copy the example environment file and configure your credentials:

```bash
cp .env.example .env
```

Edit `.env` with your actual credentials:

```env
# LiveKit Configuration
LIVEKIT_URL=wss://your-livekit-instance.livekit.cloud
LIVEKIT_API_KEY=your_api_key
LIVEKIT_API_SECRET=your_api_secret

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Deepgram Configuration
DEEPGRAM_API_KEY=your_deepgram_api_key

# ElevenLabs Configuration
ELEVENLABS_API_KEY=your_elevenlabs_api_key

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/talkai247

# Server Configuration
SERVER_API_URL=http://localhost:3030/api/v1
```

### 3. Verify Configuration

```bash
python -c "from config import config; config.validate(); print('Configuration is valid!')"
```

## Running the Agent

### Development Mode

```bash
python start_agent.py
```

### Production Mode with Docker

```bash
# Build the image
docker build -t whisper-ai-agent .

# Run with environment file
docker run --env-file .env whisper-ai-agent
```

### Using Docker Compose

```bash
# Copy environment variables
cp .env.example .env
# Edit .env with your credentials

# Start the agent
docker-compose up -d

# View logs
docker-compose logs -f whisper-agent
```

## Usage

### 1. Trigger the Agent

The agent is automatically triggered when a whisper call is initiated from the client application. You can also manually trigger it via the API:

```bash
curl -X POST http://localhost:3030/api/v1/whisper/agent/trigger \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "callId": "call_123",
    "roomName": "whisper-call-123",
    "agentInstructions": {
      "systemPrompt": "You are helping with a sales call...",
      "contactInfo": {
        "name": "John Doe",
        "type": "business"
      }
    }
  }'
```

### 2. Whisper Modes

The agent supports three whisper modes:

- **AI-to-User**: AI provides whisper suggestions to the User
- **User-to-AI**: User can whisper instructions to the AI
- **Normal**: Standard conference mode (everyone hears everyone)

### 3. Goal Configuration

Goals are automatically fetched from the server API based on the contact. You can also configure default goals in `services/goal_service.py`.

## Configuration

### Audio Processing

```env
AUDIO_SAMPLE_RATE=16000
AUDIO_CHANNELS=1
AUDIO_FRAME_SIZE=320
```

### Whisper Behavior

```env
WHISPER_TRIGGER_THRESHOLD=0.7
WHISPER_RESPONSE_DELAY=1.0
```

### AI Models

```env
OPENAI_MODEL=gpt-4
DEEPGRAM_MODEL=nova-2
ELEVENLABS_VOICE_ID=21m00Tcm4TlvDq8ikWAM
```

## Monitoring and Logging

### Logs

The agent uses structured logging with configurable output formats:

```env
LOG_LEVEL=INFO
LOG_FORMAT=json  # or 'console' for development
```

### Health Checks

The agent includes health check endpoints for monitoring:

```bash
# Check if agent is running
docker exec whisper-ai-agent python -c "import sys; sys.exit(0)"
```

### Metrics

The agent can be configured to export Prometheus metrics for monitoring performance and usage.

## Development

### Project Structure

```
agent-python/
├── whisper_agent.py          # Main agent implementation
├── start_agent.py            # Startup script
├── config.py                 # Configuration management
├── services/
│   ├── whisper_service.py    # Core whisper logic
│   └── goal_service.py       # Goal management
├── utils/
│   └── logger.py             # Logging utilities
├── requirements.txt          # Python dependencies
├── Dockerfile               # Container configuration
└── docker-compose.yml      # Multi-container setup
```

### Adding New Features

1. **Custom Triggers**: Modify `services/whisper_service.py` to add new trigger detection logic
2. **AI Providers**: Add new STT/TTS providers in the main agent file
3. **Goal Types**: Extend `services/goal_service.py` for new goal categories

### Testing

```bash
# Install test dependencies
pip install pytest pytest-asyncio

# Run tests
pytest tests/
```

## Troubleshooting

### Common Issues

1. **Connection Errors**: Verify LiveKit credentials and network connectivity
2. **Audio Issues**: Check audio device permissions and sample rate configuration
3. **API Errors**: Ensure all API keys are valid and have sufficient quota

### Debug Mode

Enable debug logging for troubleshooting:

```env
LOG_LEVEL=DEBUG
```

### Support

For issues and questions:

1. Check the logs for error messages
2. Verify all environment variables are set correctly
3. Test individual components (STT, TTS, LLM) separately
4. Review the LiveKit documentation for agent development

## License

This project is part of the tAlkai247 application and follows the same licensing terms.
