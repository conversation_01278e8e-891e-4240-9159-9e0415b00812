"""
Goal Service - Manages goals and triggers for whisper sessions
"""
import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Any

from config import config

logger = logging.getLogger(__name__)

class GoalService:
    """Service for managing goals and triggers"""
    
    def __init__(self):
        self.base_url = config.SERVER_API_URL
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session"""
        if self.session is None or self.session.closed:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def get_contact_goals(self, user_id: str, contact_id: str) -> List[Dict[str, Any]]:
        """
        Fetch goals for a specific contact
        
        Args:
            user_id: The user ID
            contact_id: The contact ID
            
        Returns:
            List of goals with triggers and prompts
        """
        try:
            session = await self._get_session()
            
            # Make API request to get contact goals
            url = f"{self.base_url}/whisper/contacts/{contact_id}/goals"
            headers = {
                "Content-Type": "application/json",
                # Note: In production, you'd need proper authentication
                # For now, we'll use a service token or internal API
            }
            
            async with session.get(url, headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("data", [])
                else:
                    logger.warning(f"Failed to fetch goals: {response.status}")
                    return self._get_default_goals()
                    
        except Exception as e:
            logger.error(f"Error fetching contact goals: {e}")
            return self._get_default_goals()
    
    def _get_default_goals(self) -> List[Dict[str, Any]]:
        """Get default goals when API is unavailable"""
        return [
            {
                "id": "default_1",
                "title": "Build Rapport",
                "aiPrompt": "Help the user build a positive relationship with the contact",
                "triggers": [
                    "how are you",
                    "nice to meet",
                    "pleasure to",
                    "good to hear",
                    "thanks for",
                    "appreciate"
                ],
                "type": "relationship"
            },
            {
                "id": "default_2",
                "title": "Understand Needs",
                "aiPrompt": "Guide the user to discover the contact's specific needs and pain points",
                "triggers": [
                    "need",
                    "problem",
                    "challenge",
                    "issue",
                    "difficulty",
                    "struggle",
                    "looking for",
                    "want to",
                    "trying to"
                ],
                "type": "discovery"
            },
            {
                "id": "default_3",
                "title": "Address Objections",
                "aiPrompt": "Help the user handle concerns and objections professionally",
                "triggers": [
                    "but",
                    "however",
                    "concern",
                    "worried",
                    "not sure",
                    "doubt",
                    "expensive",
                    "cost",
                    "price",
                    "budget",
                    "think about it"
                ],
                "type": "objection_handling"
            },
            {
                "id": "default_4",
                "title": "Close the Deal",
                "aiPrompt": "Guide the user toward a commitment or next step",
                "triggers": [
                    "sounds good",
                    "interested",
                    "like that",
                    "makes sense",
                    "next step",
                    "move forward",
                    "when can",
                    "how soon",
                    "ready to"
                ],
                "type": "closing"
            }
        ]
    
    async def update_goal_progress(
        self,
        user_id: str,
        contact_id: str,
        goal_id: str,
        progress: float
    ) -> bool:
        """
        Update progress for a specific goal
        
        Args:
            user_id: The user ID
            contact_id: The contact ID
            goal_id: The goal ID
            progress: Progress value (0.0 to 1.0)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            session = await self._get_session()
            
            url = f"{self.base_url}/whisper/goals/{goal_id}/progress"
            data = {
                "user_id": user_id,
                "contact_id": contact_id,
                "progress": progress
            }
            
            async with session.put(url, json=data) as response:
                return response.status == 200
                
        except Exception as e:
            logger.error(f"Error updating goal progress: {e}")
            return False
    
    async def get_goal_templates(self, goal_type: str = None) -> List[Dict[str, Any]]:
        """
        Get goal templates for creating new goals
        
        Args:
            goal_type: Optional filter by goal type
            
        Returns:
            List of goal templates
        """
        try:
            session = await self._get_session()
            
            url = f"{self.base_url}/whisper/templates"
            params = {"type": goal_type} if goal_type else {}
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("data", [])
                else:
                    return []
                    
        except Exception as e:
            logger.error(f"Error fetching goal templates: {e}")
            return []
    
    def extract_triggers_from_text(self, text: str) -> List[str]:
        """
        Extract potential trigger words/phrases from text
        
        Args:
            text: Text to analyze for triggers
            
        Returns:
            List of potential trigger words/phrases
        """
        import re
        
        # Common trigger patterns
        patterns = [
            r'\b(?:need|want|looking for|trying to|hoping to)\s+\w+',
            r'\b(?:problem|issue|challenge|difficulty)\s+with\s+\w+',
            r'\b(?:cost|price|budget|expensive|cheap)\b',
            r'\b(?:when|how|what|where|why)\s+\w+',
            r'\b(?:can you|could you|would you)\s+\w+',
        ]
        
        triggers = []
        text_lower = text.lower()
        
        for pattern in patterns:
            matches = re.findall(pattern, text_lower)
            triggers.extend(matches)
        
        # Also extract key nouns and verbs
        words = re.findall(r'\b\w{4,}\b', text_lower)
        
        # Filter for meaningful words (simple approach)
        meaningful_words = [
            word for word in words
            if word not in {
                'this', 'that', 'with', 'have', 'will', 'from', 'they',
                'know', 'want', 'been', 'good', 'much', 'some', 'time',
                'very', 'when', 'come', 'here', 'just', 'like', 'long',
                'make', 'many', 'over', 'such', 'take', 'than', 'them',
                'well', 'were', 'what'
            }
        ]
        
        triggers.extend(meaningful_words[:10])  # Limit to top 10
        
        return list(set(triggers))  # Remove duplicates
    
    async def close(self):
        """Close the HTTP session"""
        if self.session and not self.session.closed:
            await self.session.close()
