"""
Dynamic Goals Service - Intelligent goal management and real-time trigger processing
"""
import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import re

from config import config
from .goal_service import GoalService

logger = logging.getLogger(__name__)

class DynamicGoalsService:
    """Service for dynamic goal management and real-time trigger processing"""
    
    def __init__(self):
        self.goal_service = GoalService()
        self.conversation_context = {}
        self.trigger_history = {}
        self.goal_progress_cache = {}
        
    async def process_conversation_for_triggers(
        self,
        conversation_text: str,
        speaker: str,  # 'user' or 'caller'
        session_id: str,
        goals: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Process conversation text for dynamic trigger detection
        
        Args:
            conversation_text: The spoken text
            speaker: Who spoke ('user' or 'caller')
            session_id: Current session ID
            goals: Current active goals
            
        Returns:
            List of triggered goals with context and suggestions
        """
        try:
            # Update conversation context
            await self._update_conversation_context(session_id, conversation_text, speaker)
            
            # Analyze conversation for triggers
            triggered_goals = []
            
            for goal in goals:
                trigger_result = await self._analyze_goal_triggers(
                    goal,
                    conversation_text,
                    speaker,
                    session_id
                )
                
                if trigger_result:
                    triggered_goals.append(trigger_result)
            
            # Sort by priority and confidence
            triggered_goals.sort(key=lambda x: (x['priority'], x['confidence']), reverse=True)
            
            return triggered_goals
            
        except Exception as e:
            logger.error(f"Error processing conversation for triggers: {e}")
            return []
    
    async def _update_conversation_context(
        self,
        session_id: str,
        text: str,
        speaker: str
    ):
        """Update conversation context for better trigger detection"""
        if session_id not in self.conversation_context:
            self.conversation_context[session_id] = {
                'messages': [],
                'topics': set(),
                'sentiment': 'neutral',
                'urgency_level': 'normal',
                'last_updated': datetime.now()
            }
        
        context = self.conversation_context[session_id]
        
        # Add message to context
        context['messages'].append({
            'text': text,
            'speaker': speaker,
            'timestamp': datetime.now().isoformat()
        })
        
        # Keep only last 10 messages for context
        if len(context['messages']) > 10:
            context['messages'] = context['messages'][-10:]
        
        # Extract topics and keywords
        topics = self._extract_topics(text)
        context['topics'].update(topics)
        
        # Analyze sentiment
        context['sentiment'] = self._analyze_sentiment(text)
        
        # Detect urgency
        context['urgency_level'] = self._detect_urgency(text)
        
        context['last_updated'] = datetime.now()
    
    async def _analyze_goal_triggers(
        self,
        goal: Dict[str, Any],
        conversation_text: str,
        speaker: str,
        session_id: str
    ) -> Optional[Dict[str, Any]]:
        """Analyze if a specific goal should be triggered"""
        
        # Get goal configuration
        goal_id = goal.get('id')
        goal_type = goal.get('type', 'general')
        triggers = goal.get('triggers', [])
        ai_prompt = goal.get('aiPrompt', '')
        
        # Check basic keyword triggers
        basic_match = self._check_basic_triggers(conversation_text, triggers)
        
        # Check contextual triggers
        contextual_match = await self._check_contextual_triggers(
            goal, conversation_text, speaker, session_id
        )
        
        # Check temporal triggers (timing-based)
        temporal_match = self._check_temporal_triggers(goal, session_id)
        
        # Check progress-based triggers
        progress_match = await self._check_progress_triggers(goal, session_id)
        
        # Calculate overall confidence
        confidence = self._calculate_trigger_confidence(
            basic_match, contextual_match, temporal_match, progress_match
        )
        
        if confidence > config.WHISPER_TRIGGER_THRESHOLD:
            return {
                'goal_id': goal_id,
                'goal_title': goal.get('title', 'Unknown Goal'),
                'goal_type': goal_type,
                'confidence': confidence,
                'priority': goal.get('priority', 5),
                'trigger_type': self._determine_trigger_type(
                    basic_match, contextual_match, temporal_match, progress_match
                ),
                'suggestion': await self._generate_dynamic_suggestion(
                    goal, conversation_text, speaker, session_id, confidence
                ),
                'context': {
                    'speaker': speaker,
                    'conversation_snippet': conversation_text[-100:],  # Last 100 chars
                    'session_context': self.conversation_context.get(session_id, {}),
                }
            }
        
        return None
    
    def _check_basic_triggers(self, text: str, triggers: List[str]) -> float:
        """Check basic keyword/phrase triggers"""
        if not triggers:
            return 0.0
        
        text_lower = text.lower()
        matches = 0
        
        for trigger in triggers:
            if isinstance(trigger, str):
                if trigger.lower() in text_lower:
                    matches += 1
                # Check for partial matches
                elif self._fuzzy_match(trigger.lower(), text_lower):
                    matches += 0.5
        
        return min(matches / len(triggers), 1.0)
    
    async def _check_contextual_triggers(
        self,
        goal: Dict[str, Any],
        text: str,
        speaker: str,
        session_id: str
    ) -> float:
        """Check contextual triggers based on conversation flow"""
        context = self.conversation_context.get(session_id, {})
        
        # Analyze conversation flow
        flow_score = self._analyze_conversation_flow(goal, context, speaker)
        
        # Check sentiment alignment
        sentiment_score = self._check_sentiment_triggers(goal, context)
        
        # Check topic relevance
        topic_score = self._check_topic_relevance(goal, context)
        
        # Check speaker-specific triggers
        speaker_score = self._check_speaker_triggers(goal, speaker, text)
        
        return (flow_score + sentiment_score + topic_score + speaker_score) / 4
    
    def _check_temporal_triggers(self, goal: Dict[str, Any], session_id: str) -> float:
        """Check timing-based triggers"""
        context = self.conversation_context.get(session_id, {})
        
        if not context:
            return 0.0
        
        # Check if enough time has passed since last trigger
        last_trigger = self.trigger_history.get(f"{session_id}_{goal.get('id')}")
        if last_trigger:
            time_since = datetime.now() - last_trigger
            if time_since < timedelta(minutes=2):  # Cooldown period
                return 0.0
        
        # Check conversation duration
        messages = context.get('messages', [])
        if len(messages) < 3:  # Need some conversation before triggering
            return 0.0
        
        # Check if goal is time-sensitive
        goal_urgency = goal.get('urgency', 'normal')
        if goal_urgency == 'high':
            return 0.8
        elif goal_urgency == 'medium':
            return 0.5
        
        return 0.3
    
    async def _check_progress_triggers(self, goal: Dict[str, Any], session_id: str) -> float:
        """Check progress-based triggers"""
        goal_id = goal.get('id')
        progress = self.goal_progress_cache.get(f"{session_id}_{goal_id}", 0.0)
        
        # Trigger if goal progress is stalled
        if progress < 0.3:  # Less than 30% progress
            return 0.7
        elif progress < 0.6:  # Less than 60% progress
            return 0.4
        
        return 0.1
    
    def _calculate_trigger_confidence(
        self,
        basic: float,
        contextual: float,
        temporal: float,
        progress: float
    ) -> float:
        """Calculate overall trigger confidence"""
        # Weighted average with emphasis on contextual and basic triggers
        weights = {
            'basic': 0.4,
            'contextual': 0.3,
            'temporal': 0.2,
            'progress': 0.1
        }
        
        confidence = (
            basic * weights['basic'] +
            contextual * weights['contextual'] +
            temporal * weights['temporal'] +
            progress * weights['progress']
        )
        
        return min(confidence, 1.0)
    
    def _determine_trigger_type(
        self,
        basic: float,
        contextual: float,
        temporal: float,
        progress: float
    ) -> str:
        """Determine the primary trigger type"""
        scores = {
            'keyword': basic,
            'contextual': contextual,
            'temporal': temporal,
            'progress': progress
        }
        
        return max(scores, key=scores.get)
    
    async def _generate_dynamic_suggestion(
        self,
        goal: Dict[str, Any],
        conversation_text: str,
        speaker: str,
        session_id: str,
        confidence: float
    ) -> str:
        """Generate dynamic, context-aware suggestions"""
        context = self.conversation_context.get(session_id, {})
        
        # Build context for AI
        context_prompt = self._build_context_prompt(goal, context, speaker, conversation_text)
        
        # Generate suggestion based on goal type
        goal_type = goal.get('type', 'general')
        
        if goal_type == 'sales':
            return await self._generate_sales_suggestion(context_prompt, confidence)
        elif goal_type == 'support':
            return await self._generate_support_suggestion(context_prompt, confidence)
        elif goal_type == 'negotiation':
            return await self._generate_negotiation_suggestion(context_prompt, confidence)
        else:
            return await self._generate_general_suggestion(context_prompt, confidence)
    
    def _build_context_prompt(
        self,
        goal: Dict[str, Any],
        context: Dict[str, Any],
        speaker: str,
        conversation_text: str
    ) -> str:
        """Build context prompt for AI suggestion generation"""
        recent_messages = context.get('messages', [])[-3:]  # Last 3 messages
        topics = list(context.get('topics', set()))[:5]  # Top 5 topics
        sentiment = context.get('sentiment', 'neutral')
        urgency = context.get('urgency_level', 'normal')
        
        prompt = f"""
        Goal: {goal.get('title', 'Unknown')}
        Goal Type: {goal.get('type', 'general')}
        Current Speaker: {speaker}
        Last Statement: "{conversation_text}"
        
        Recent Conversation:
        {chr(10).join([f"- {msg['speaker']}: {msg['text'][:50]}..." for msg in recent_messages])}
        
        Context:
        - Topics: {', '.join(topics)}
        - Sentiment: {sentiment}
        - Urgency: {urgency}
        
        Goal Instructions: {goal.get('aiPrompt', 'Provide helpful guidance')}
        """
        
        return prompt
    
    async def _generate_sales_suggestion(self, context_prompt: str, confidence: float) -> str:
        """Generate sales-specific suggestions"""
        suggestions = [
            "Ask about their budget and decision timeline",
            "Highlight the ROI and long-term benefits",
            "Address their main concern directly",
            "Suggest a trial or pilot program",
            "Ask what would make this a perfect fit for them"
        ]
        
        # Return confidence-weighted suggestion
        if confidence > 0.8:
            return "Now's a great time to ask about their decision process and timeline."
        elif confidence > 0.6:
            return "Consider exploring their specific needs and pain points."
        else:
            return suggestions[int(confidence * len(suggestions))]
    
    async def _generate_support_suggestion(self, context_prompt: str, confidence: float) -> str:
        """Generate support-specific suggestions"""
        suggestions = [
            "Ask for specific error messages or symptoms",
            "Suggest checking the most common causes first",
            "Offer to escalate to a specialist if needed",
            "Ask about their current workaround",
            "Confirm their understanding before proceeding"
        ]
        
        return suggestions[int(confidence * len(suggestions))]
    
    async def _generate_negotiation_suggestion(self, context_prompt: str, confidence: float) -> str:
        """Generate negotiation-specific suggestions"""
        suggestions = [
            "Ask what their ideal outcome looks like",
            "Find out what's most important to them",
            "Suggest a compromise that benefits both parties",
            "Ask about their decision timeline",
            "Clarify any concerns or objections"
        ]
        
        return suggestions[int(confidence * len(suggestions))]
    
    async def _generate_general_suggestion(self, context_prompt: str, confidence: float) -> str:
        """Generate general suggestions"""
        return "Ask clarifying questions to understand their perspective better."
    
    # Helper methods
    def _extract_topics(self, text: str) -> set:
        """Extract topics from text"""
        # Simple keyword extraction - can be enhanced with NLP
        keywords = re.findall(r'\b\w{4,}\b', text.lower())
        return set(keywords[:5])  # Top 5 keywords
    
    def _analyze_sentiment(self, text: str) -> str:
        """Analyze sentiment of text"""
        positive_words = ['good', 'great', 'excellent', 'perfect', 'love', 'like', 'yes']
        negative_words = ['bad', 'terrible', 'hate', 'no', 'problem', 'issue', 'concern']
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'
    
    def _detect_urgency(self, text: str) -> str:
        """Detect urgency level in text"""
        urgent_words = ['urgent', 'asap', 'immediately', 'quickly', 'rush', 'emergency']
        text_lower = text.lower()
        
        if any(word in text_lower for word in urgent_words):
            return 'high'
        elif any(word in text_lower for word in ['soon', 'fast', 'quick']):
            return 'medium'
        else:
            return 'normal'
    
    def _fuzzy_match(self, trigger: str, text: str) -> bool:
        """Simple fuzzy matching"""
        trigger_words = trigger.split()
        return all(word in text for word in trigger_words if len(word) > 3)
    
    def _analyze_conversation_flow(self, goal: Dict, context: Dict, speaker: str) -> float:
        """Analyze conversation flow for goal relevance"""
        # Simple implementation - can be enhanced
        return 0.5
    
    def _check_sentiment_triggers(self, goal: Dict, context: Dict) -> float:
        """Check sentiment-based triggers"""
        return 0.5
    
    def _check_topic_relevance(self, goal: Dict, context: Dict) -> float:
        """Check topic relevance for goal"""
        return 0.5
    
    def _check_speaker_triggers(self, goal: Dict, speaker: str, text: str) -> float:
        """Check speaker-specific triggers"""
        return 0.5
