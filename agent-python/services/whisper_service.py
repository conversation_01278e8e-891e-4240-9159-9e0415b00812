"""
Whisper Service - Core logic for processing speech and generating whisper responses
"""
import asyncio
import re
from typing import Dict, List, Optional, Any
import logging
from openai import Async<PERSON>penA<PERSON>

from config import config

logger = logging.getLogger(__name__)

class WhisperService:
    """Service for processing speech and generating whisper responses"""
    
    def __init__(self):
        self.openai_client = AsyncOpenAI(api_key=config.OPENAI_API_KEY)
        self.trigger_cache: Dict[str, List[str]] = {}
        
    async def process_speech_for_whispers(
        self,
        speech_text: str,
        goals: List[Dict[str, Any]],
        mode: str = "ai-to-user"
    ) -> Optional[str]:
        """
        Process speech text and generate whisper response if triggers are detected
        
        Args:
            speech_text: The transcribed speech text
            goals: List of goals with triggers and prompts
            mode: Current whisper mode
            
        Returns:
            Whisper response text or None if no triggers detected
        """
        try:
            # Only generate whispers in ai-to-user mode
            if mode != "ai-to-user":
                return None
            
            # Check for trigger words/phrases
            triggered_goals = self._detect_triggers(speech_text, goals)
            
            if not triggered_goals:
                return None
            
            # Generate whisper response for the most relevant goal
            primary_goal = triggered_goals[0]
            whisper_response = await self._generate_whisper_response(
                speech_text,
                primary_goal
            )
            
            return whisper_response
            
        except Exception as e:
            logger.error(f"Error processing speech for whispers: {e}")
            return None
    
    def _detect_triggers(
        self,
        speech_text: str,
        goals: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """
        Detect trigger words/phrases in speech text
        
        Args:
            speech_text: The speech text to analyze
            goals: List of goals with trigger information
            
        Returns:
            List of triggered goals, sorted by relevance
        """
        triggered_goals = []
        speech_lower = speech_text.lower()
        
        for goal in goals:
            triggers = goal.get("triggers", [])
            if not triggers:
                # If no specific triggers, use keywords from the goal title and prompt
                triggers = self._extract_keywords_from_goal(goal)
            
            # Check if any triggers are present in the speech
            trigger_matches = []
            for trigger in triggers:
                if isinstance(trigger, str):
                    trigger_lower = trigger.lower()
                    if trigger_lower in speech_lower:
                        trigger_matches.append(trigger)
                    # Also check for partial matches and synonyms
                    elif self._fuzzy_match(trigger_lower, speech_lower):
                        trigger_matches.append(trigger)
            
            if trigger_matches:
                goal_copy = goal.copy()
                goal_copy["matched_triggers"] = trigger_matches
                goal_copy["relevance_score"] = len(trigger_matches)
                triggered_goals.append(goal_copy)
        
        # Sort by relevance score (number of matched triggers)
        triggered_goals.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)
        
        return triggered_goals
    
    def _extract_keywords_from_goal(self, goal: Dict[str, Any]) -> List[str]:
        """Extract keywords from goal title and AI prompt"""
        keywords = []
        
        # Extract from title
        title = goal.get("title", "")
        title_keywords = re.findall(r'\b\w{4,}\b', title.lower())
        keywords.extend(title_keywords)
        
        # Extract from AI prompt
        ai_prompt = goal.get("aiPrompt", "")
        prompt_keywords = re.findall(r'\b\w{4,}\b', ai_prompt.lower())
        keywords.extend(prompt_keywords)
        
        # Remove common words
        stop_words = {
            "this", "that", "with", "have", "will", "from", "they", "know",
            "want", "been", "good", "much", "some", "time", "very", "when",
            "come", "here", "just", "like", "long", "make", "many", "over",
            "such", "take", "than", "them", "well", "were", "what"
        }
        
        keywords = [kw for kw in keywords if kw not in stop_words]
        
        return list(set(keywords))  # Remove duplicates
    
    def _fuzzy_match(self, trigger: str, speech: str) -> bool:
        """Check for fuzzy matches (partial words, similar phrases)"""
        # Simple fuzzy matching - can be enhanced with more sophisticated algorithms
        trigger_words = trigger.split()
        
        # Check if all words in trigger appear in speech (not necessarily consecutive)
        for word in trigger_words:
            if len(word) > 3 and word not in speech:
                return False
        
        return len(trigger_words) > 0
    
    async def _generate_whisper_response(
        self,
        speech_text: str,
        goal: Dict[str, Any]
    ) -> str:
        """
        Generate AI whisper response based on speech and goal
        
        Args:
            speech_text: The original speech text
            goal: The triggered goal with context
            
        Returns:
            Generated whisper response
        """
        try:
            system_prompt = f"""
            You are providing whisper advice during a call. The user's goal is: {goal.get('title', 'Unknown')}
            
            Specific instructions: {goal.get('aiPrompt', 'Provide helpful advice')}
            
            The other person just said: "{speech_text}"
            
            Provide a brief, actionable whisper suggestion (under 20 words) to help the user achieve their goal.
            Be specific and strategic. Focus on what the user should say or ask next.
            """
            
            response = await self.openai_client.chat.completions.create(
                model=config.OPENAI_MODEL,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": f"What should I say or ask next?"}
                ],
                max_tokens=config.OPENAI_MAX_TOKENS,
                temperature=config.OPENAI_TEMPERATURE,
            )
            
            whisper_text = response.choices[0].message.content.strip()
            
            # Ensure the response is concise
            if len(whisper_text.split()) > 25:
                # Truncate if too long
                words = whisper_text.split()[:20]
                whisper_text = " ".join(words) + "..."
            
            return whisper_text
            
        except Exception as e:
            logger.error(f"Error generating whisper response: {e}")
            # Fallback response
            return "Consider asking for more details about their needs."
    
    async def log_interaction(self, interaction_data: Dict[str, Any]):
        """Log whisper interaction for analytics"""
        try:
            # In a production environment, this would send data to analytics service
            logger.info(f"Whisper interaction logged: {interaction_data}")
            
            # Could also store in database or send to monitoring service
            
        except Exception as e:
            logger.error(f"Error logging interaction: {e}")
    
    def get_predefined_responses(self, goal_type: str) -> List[str]:
        """Get predefined responses for common goal types"""
        responses = {
            "sales": [
                "Ask about their budget and timeline.",
                "Mention the ROI and long-term benefits.",
                "Address their main concern directly.",
                "Suggest a trial or demo period.",
                "Ask what would make this a perfect solution for them."
            ],
            "support": [
                "Ask for specific error messages or symptoms.",
                "Suggest checking the most common causes first.",
                "Offer to escalate to a specialist if needed.",
                "Ask about their current workaround or process.",
                "Confirm their understanding before proceeding."
            ],
            "negotiation": [
                "Ask what their ideal outcome looks like.",
                "Find out what's most important to them.",
                "Suggest a compromise that benefits both parties.",
                "Ask about their decision-making timeline.",
                "Clarify any concerns or objections they have."
            ]
        }
        
        return responses.get(goal_type.lower(), [
            "Ask clarifying questions to understand better.",
            "Acknowledge their point and build on it.",
            "Suggest the next logical step in the conversation."
        ])
