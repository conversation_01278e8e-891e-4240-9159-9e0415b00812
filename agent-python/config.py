"""
Configuration module for the Whisper AI Agent
"""
import os
from typing import Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for the Whisper AI Agent"""
    
    # LiveKit Configuration
    LIVEKIT_URL: str = os.getenv("LIVEKIT_URL", "")
    LIVEKIT_API_KEY: str = os.getenv("LIVEKIT_API_KEY", "")
    LIVEKIT_API_SECRET: str = os.getenv("LIVEKIT_API_SECRET", "")
    
    # OpenAI Configuration
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    OPENAI_MODEL: str = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
    OPENAI_MAX_TOKENS: int = int(os.getenv("OPENAI_MAX_TOKENS", "150"))
    OPENAI_TEMPERATURE: float = float(os.getenv("OPENAI_TEMPERATURE", "0.7"))

    # Deepgram Configuration
    DEEPGRAM_API_KEY: str = os.getenv("DEEPGRAM_API_KEY", "")
    DEEPGRAM_MODEL: str = os.getenv("DEEPGRAM_MODEL", "nova-2")
    DEEPGRAM_LANGUAGE: str = os.getenv("DEEPGRAM_LANGUAGE", "en-US")

    # ElevenLabs Configuration
    ELEVENLABS_API_KEY: str = os.getenv("ELEVENLABS_API_KEY", "")
    ELEVENLABS_VOICE_ID: str = os.getenv("ELEVENLABS_VOICE_ID", "21m00Tcm4TlvDq8ikWAM")
    ELEVENLABS_MODEL: str = os.getenv("ELEVENLABS_MODEL", "eleven_turbo_v2_5")
    
    # Database Configuration
    DATABASE_URL: str = os.getenv("DATABASE_URL", "")
    
    # Server Configuration
    SERVER_API_URL: str = os.getenv("SERVER_API_URL", "http://localhost:3030/api/v1")
    
    # Logging Configuration
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FORMAT: str = os.getenv("LOG_FORMAT", "json")
    
    # Agent Configuration
    AGENT_NAME: str = os.getenv("AGENT_NAME", "whisper-ai-assistant")
    AGENT_VERSION: str = os.getenv("AGENT_VERSION", "1.0.0")
    
    # Whisper Feature Configuration
    WHISPER_ENABLED: bool = os.getenv("WHISPER_ENABLED", "true").lower() == "true"
    WHISPER_TRIGGER_THRESHOLD: float = float(os.getenv("WHISPER_TRIGGER_THRESHOLD", "0.7"))
    WHISPER_RESPONSE_DELAY: float = float(os.getenv("WHISPER_RESPONSE_DELAY", "1.0"))
    
    # Audio Processing Configuration
    AUDIO_SAMPLE_RATE: int = int(os.getenv("AUDIO_SAMPLE_RATE", "16000"))
    AUDIO_CHANNELS: int = int(os.getenv("AUDIO_CHANNELS", "1"))
    AUDIO_FRAME_SIZE: int = int(os.getenv("AUDIO_FRAME_SIZE", "320"))
    
    @classmethod
    def validate(cls) -> bool:
        """Validate required configuration values"""
        required_configs = [
            ("LIVEKIT_URL", cls.LIVEKIT_URL),
            ("LIVEKIT_API_KEY", cls.LIVEKIT_API_KEY),
            ("LIVEKIT_API_SECRET", cls.LIVEKIT_API_SECRET),
            ("OPENAI_API_KEY", cls.OPENAI_API_KEY),
            ("DEEPGRAM_API_KEY", cls.DEEPGRAM_API_KEY),
        ]
        
        missing_configs = [name for name, value in required_configs if not value]
        
        if missing_configs:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_configs)}")
        
        return True
    
    @classmethod
    def get_livekit_config(cls) -> dict:
        """Get LiveKit configuration as dictionary"""
        return {
            "url": cls.LIVEKIT_URL,
            "api_key": cls.LIVEKIT_API_KEY,
            "api_secret": cls.LIVEKIT_API_SECRET,
        }
    
    @classmethod
    def get_openai_config(cls) -> dict:
        """Get OpenAI configuration as dictionary"""
        return {
            "api_key": cls.OPENAI_API_KEY,
            "model": cls.OPENAI_MODEL,
            "max_tokens": cls.OPENAI_MAX_TOKENS,
            "temperature": cls.OPENAI_TEMPERATURE,
        }
    
    @classmethod
    def get_deepgram_config(cls) -> dict:
        """Get Deepgram configuration as dictionary"""
        return {
            "api_key": cls.DEEPGRAM_API_KEY,
            "model": cls.DEEPGRAM_MODEL,
            "language": cls.DEEPGRAM_LANGUAGE,
        }
    
    @classmethod
    def get_elevenlabs_config(cls) -> dict:
        """Get ElevenLabs configuration as dictionary"""
        return {
            "api_key": cls.ELEVENLABS_API_KEY,
            "voice_id": cls.ELEVENLABS_VOICE_ID,
            "model": cls.ELEVENLABS_MODEL,
        }

# Global config instance
config = Config()

# Validate configuration on import
config.validate()
