"""
Logging utilities for the Whisper AI Agent
"""
import logging
import sys
from typing import Optional
import structlog
from config import config

def setup_logger(name: str, level: Optional[str] = None) -> logging.Logger:
    """
    Set up structured logging for the agent
    
    Args:
        name: Logger name
        level: Log level (defaults to config.LOG_LEVEL)
        
    Returns:
        Configured logger instance
    """
    log_level = level or config.LOG_LEVEL
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if config.LOG_FORMAT == "json" else structlog.dev.Console<PERSON>enderer(),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, log_level.upper()),
    )
    
    # Get logger
    logger = structlog.get_logger(name)
    
    return logger

def get_logger(name: str) -> logging.Logger:
    """Get a logger instance"""
    return structlog.get_logger(name)
