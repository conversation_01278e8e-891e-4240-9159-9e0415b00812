#!/usr/bin/env python3
"""
Test script to verify the agent can start without errors
"""
import asyncio
import logging
from start_agent import WhisperAgentRunner

# Setup basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_agent_setup():
    """Test that the agent can be set up without errors"""
    try:
        logger.info("Testing Whisper Agent setup...")
        
        runner = WhisperAgentRunner()
        await runner.setup()
        
        logger.info("✅ Agent setup completed successfully!")
        logger.info(f"Worker options created: {runner.worker_options is not None}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Agent setup failed: {e}")
        return False

async def main():
    """Main test function"""
    success = await test_agent_setup()
    
    if success:
        print("\n🎉 All tests passed! The agent is ready to run.")
        print("\nTo start the agent with LiveKit:")
        print("1. Set up your environment variables in .env")
        print("2. Run: python start_agent.py")
    else:
        print("\n❌ Tests failed. Check the error messages above.")
        return 1
    
    return 0

if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
