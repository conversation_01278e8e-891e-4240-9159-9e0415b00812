"""
Whisper AI Agent - Real-time AI assistant for whisper calls
Updated for LiveKit Agents 1.0+ API
"""
import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from livekit import agents, rtc
from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    RunContext,
    WorkerOptions,
    cli,
    function_tool,
)

# Import plugins - Updated for 1.0+ API
from livekit.plugins import deepgram, elevenlabs, openai, silero

from config import config
from services.whisper_service import WhisperService
from services.goal_service import GoalService
from services.dynamic_goals_service import DynamicGoalsService
from utils.logger import setup_logger

# Setup logging
logger = setup_logger(__name__)

@dataclass
class WhisperContext:
    """Context information for a whisper session"""
    user_id: str
    contact_id: str
    contact_name: str
    contact_type: str
    goals: List[Dict[str, Any]]
    mode: str = "ai-to-user"  # ai-to-user, user-to-ai, normal
    triggers: List[str] = None

class WhisperAgent(Agent):
    """
    AI Agent for Whisper feature using LiveKit Agents 1.0+ API
    Acts as a hidden participant that listens to conversations and provides whispers
    """

    def __init__(self):
        super().__init__(
            instructions="""You are an AI assistant providing whisper advice during calls.
            Listen carefully to conversations and provide brief, actionable suggestions.
            Keep responses under 20 words for quick whispers.
            Only speak when you detect relevant opportunities to help."""
        )
        self.whisper_service = WhisperService()
        self.goal_service = GoalService()
        self.dynamic_goals_service = DynamicGoalsService()
        self.whisper_context: Optional[WhisperContext] = None

    async def on_enter(self):
        """Called when the agent enters a session"""
        logger.info("Whisper AI Agent entering session")

        # Extract whisper context from session metadata
        self.whisper_context = await self._extract_whisper_context()
        if not self.whisper_context:
            logger.error("Failed to extract whisper context")
            return

        logger.info(f"Whisper context loaded for contact: {self.whisper_context.contact_name}")

    @function_tool
    async def provide_whisper_suggestion(
        self,
        context: RunContext,
        conversation_text: str,
        speaker: str = "user"
    ):
        """Analyze conversation and provide whisper suggestions"""
        if not self.whisper_context:
            return "Whisper context not available"

        try:
            # Use dynamic goals service for intelligent trigger detection
            triggered_goals = await self.dynamic_goals_service.process_conversation_for_triggers(
                conversation_text,
                speaker,
                context.room.name,
                self.whisper_context.goals
            )

            if triggered_goals:
                best_trigger = triggered_goals[0]
                suggestion = best_trigger['suggestion']

                # Log the interaction
                await self._log_whisper_interaction(
                    context.room.name,
                    conversation_text,
                    suggestion,
                    "ai_to_user_whisper",
                    {
                        'triggered_goal': best_trigger,
                        'confidence': best_trigger['confidence'],
                        'trigger_type': best_trigger['trigger_type']
                    }
                )

                return suggestion

            return None

        except Exception as e:
            logger.error(f"Error providing whisper suggestion: {e}")
            return None
    
    async def _extract_whisper_context(self) -> Optional[WhisperContext]:
        """Extract whisper context from session metadata"""
        try:
            # In 1.0+ API, we get context from session userdata or room metadata
            if hasattr(self.session, 'userdata') and self.session.userdata:
                metadata = self.session.userdata
            else:
                # Fallback to room metadata
                room_metadata = getattr(self.session.room, 'metadata', '{}')
                metadata = json.loads(room_metadata) if room_metadata else {}

            if not metadata:
                return None

            # Get goals for the contact
            goals = await self.goal_service.get_contact_goals(
                metadata.get("userId"),
                metadata.get("contactId")
            )

            return WhisperContext(
                user_id=metadata.get("userId"),
                contact_id=metadata.get("contactId"),
                contact_name=metadata.get("contactName"),
                contact_type=metadata.get("contactType"),
                goals=goals,
                triggers=[goal.get("triggers", []) for goal in goals]
            )
        except Exception as e:
            logger.error(f"Error extracting whisper context: {e}")
            return None
    
    def _format_goals_for_prompt(self, goals: List[Dict[str, Any]]) -> str:
        """Format goals for the system prompt"""
        if not goals:
            return "No specific goals set for this call."

        formatted_goals = []
        for i, goal in enumerate(goals, 1):
            formatted_goals.append(
                f"{i}. {goal.get('title', 'Unknown Goal')}: {goal.get('aiPrompt', 'No specific instructions')}"
            )

        return "\n".join(formatted_goals)
    
    async def _is_whisper_to_ai(self, message: str) -> bool:
        """Detect if the user is whispering to the AI"""
        whisper_indicators = [
            "ai", "assistant", "help me", "suggest", "what should i",
            "tell me", "advice", "recommend", "whisper", "privately"
        ]

        message_lower = message.lower()
        return any(indicator in message_lower for indicator in whisper_indicators)

    async def _log_whisper_interaction(
        self,
        room_name: str,
        user_speech: str,
        whisper_response: str,
        interaction_type: str = "ai_to_user_whisper",
        additional_metadata: dict = None
    ):
        """Log whisper interaction for analytics"""
        try:
            if not self.whisper_context:
                return

            interaction_data = {
                "room_name": room_name,
                "user_id": self.whisper_context.user_id,
                "contact_id": self.whisper_context.contact_id,
                "user_speech": user_speech,
                "whisper_response": whisper_response,
                "mode": self.whisper_context.mode,
                "interaction_type": interaction_type,
                "timestamp": asyncio.get_event_loop().time(),
                "metadata": additional_metadata or {},
            }

            # Send to analytics service
            await self.whisper_service.log_interaction(interaction_data)

        except Exception as e:
            logger.error(f"Error logging whisper interaction: {e}")

# Agent entry point using new 1.0+ API
async def entrypoint(ctx: JobContext):
    """Entry point for the Whisper AI Agent using LiveKit Agents 1.0+ API"""
    await ctx.connect()

    # Create the whisper agent
    agent = WhisperAgent()

    # Create agent session with STT, LLM, TTS configuration
    session = AgentSession(
        vad=silero.VAD.load(),
        stt=deepgram.STT(
            model=config.DEEPGRAM_MODEL,
            language=config.DEEPGRAM_LANGUAGE,
        ),
        llm=openai.LLM(
            model=config.OPENAI_MODEL,
            temperature=config.OPENAI_TEMPERATURE,
        ),
        tts=elevenlabs.TTS(
            voice=config.ELEVENLABS_VOICE_ID,
            model=config.ELEVENLABS_MODEL,
        ),
    )

    # Start the agent session
    await session.start(agent=agent, room=ctx.room)

    # Generate initial greeting
    await session.generate_reply(
        instructions="You are now connected as a whisper assistant. Listen for opportunities to help."
    )

if __name__ == "__main__":
    # Run the agent using new 1.0+ API
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
        )
    )
