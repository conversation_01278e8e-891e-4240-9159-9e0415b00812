"""
Whisper AI Agent - Real-time AI assistant for whisper calls
Implements the architecture described in livekit.md Section 3
"""
import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from livekit import agents, rtc
from livekit.agents import JobContext, WorkerOptions, cli
from livekit.agents.llm import ChatContext, ChatMessage, ChatRole
from livekit.agents.pipeline import VoicePipelineAgent

# Import plugins
from livekit.plugins import deepgram, elevenlabs, openai, silero

from config import config
from services.whisper_service import WhisperService
from services.goal_service import GoalService
from utils.logger import setup_logger

# Setup logging
logger = setup_logger(__name__)

@dataclass
class WhisperContext:
    """Context information for a whisper session"""
    user_id: str
    contact_id: str
    contact_name: str
    contact_type: str
    goals: List[Dict[str, Any]]
    mode: str = "ai-to-user"  # ai-to-user, user-to-ai, normal
    triggers: List[str] = None

class WhisperAIAgent:
    """
    AI Agent for Whisper feature
    Acts as a hidden participant that listens to conversations and provides whispers
    """
    
    def __init__(self):
        self.whisper_service = WhisperService()
        self.goal_service = GoalService()
        self.active_sessions: Dict[str, WhisperContext] = {}
        
    async def entrypoint(self, ctx: JobContext):
        """Main entry point for the agent"""
        try:
            logger.info(f"Whisper AI Agent starting for room: {ctx.room.name}")
            
            # Connect to the room
            await ctx.connect(auto_subscribe=agents.AutoSubscribe.AUDIO_ONLY)
            
            # Wait for participants to join
            participant = await ctx.wait_for_participant()
            logger.info(f"Participant joined: {participant.identity}")
            
            # Extract whisper context from participant metadata
            whisper_context = await self._extract_whisper_context(participant)
            if not whisper_context:
                logger.error("Failed to extract whisper context from participant metadata")
                return
            
            # Store session context
            self.active_sessions[ctx.room.name] = whisper_context
            
            # Initialize the voice pipeline agent
            agent = VoicePipelineAgent(
                vad=silero.VAD.load(),
                stt=deepgram.STT(
                    model=config.DEEPGRAM_MODEL,
                    language=config.DEEPGRAM_LANGUAGE,
                ),
                llm=openai.LLM(
                    model=config.OPENAI_MODEL,
                    temperature=config.OPENAI_TEMPERATURE,
                ),
                tts=elevenlabs.TTS(
                    voice=config.ELEVENLABS_VOICE_ID,
                    model=config.ELEVENLABS_MODEL,
                ),
                chat_ctx=self._create_initial_chat_context(whisper_context),
            )
            
            # Set up event handlers
            self._setup_event_handlers(ctx, agent, whisper_context)
            
            # Start the agent with hidden permissions
            agent.start(ctx.room, participant)
            
            # Send initial whisper message
            await agent.say(
                "I'm your AI assistant, ready to help with whisper suggestions during your call.",
                allow_interruptions=False
            )
            
            logger.info(f"Whisper AI Agent started successfully for room: {ctx.room.name}")
            
        except Exception as e:
            logger.error(f"Error in Whisper AI Agent entrypoint: {e}")
            raise
    
    async def _extract_whisper_context(self, participant) -> Optional[WhisperContext]:
        """Extract whisper context from participant metadata"""
        try:
            if not participant.metadata:
                return None
            
            metadata = json.loads(participant.metadata)
            
            # Get goals for the contact
            goals = await self.goal_service.get_contact_goals(
                metadata.get("userId"),
                metadata.get("contactId")
            )
            
            return WhisperContext(
                user_id=metadata.get("userId"),
                contact_id=metadata.get("contactId"),
                contact_name=metadata.get("contactName"),
                contact_type=metadata.get("contactType"),
                goals=goals,
                triggers=[goal.get("triggers", []) for goal in goals]
            )
        except Exception as e:
            logger.error(f"Error extracting whisper context: {e}")
            return None
    
    def _create_initial_chat_context(self, whisper_context: WhisperContext) -> ChatContext:
        """Create initial chat context for the AI agent"""
        system_prompt = f"""
        You are an AI assistant providing whisper advice during a call between a user and {whisper_context.contact_name}.
        
        Contact Information:
        - Name: {whisper_context.contact_name}
        - Type: {whisper_context.contact_type}
        
        Your Goals:
        {self._format_goals_for_prompt(whisper_context.goals)}
        
        Instructions:
        1. Listen to the conversation carefully
        2. Provide brief, actionable whisper suggestions to help achieve the goals
        3. Only speak when you detect relevant triggers or opportunities
        4. Keep responses under 20 words for quick whispers
        5. Be supportive and strategic in your advice
        6. Focus on helping the user achieve their objectives
        
        Remember: You are hidden from the other participant. Only the user can hear you.
        """
        
        chat_ctx = ChatContext()
        chat_ctx.messages.append(
            ChatMessage(role=ChatRole.SYSTEM, content=system_prompt)
        )
        
        return chat_ctx
    
    def _format_goals_for_prompt(self, goals: List[Dict[str, Any]]) -> str:
        """Format goals for the system prompt"""
        if not goals:
            return "No specific goals set for this call."
        
        formatted_goals = []
        for i, goal in enumerate(goals, 1):
            formatted_goals.append(
                f"{i}. {goal.get('title', 'Unknown Goal')}: {goal.get('aiPrompt', 'No specific instructions')}"
            )
        
        return "\n".join(formatted_goals)
    
    def _setup_event_handlers(self, ctx: JobContext, agent: VoicePipelineAgent, whisper_context: WhisperContext):
        """Set up event handlers for the agent"""
        
        @agent.on("user_speech_committed")
        async def on_user_speech(message: str):
            """Handle user speech for trigger detection"""
            try:
                logger.debug(f"User speech detected: {message}")
                
                # Process speech for whisper triggers
                whisper_response = await self.whisper_service.process_speech_for_whispers(
                    message,
                    whisper_context.goals,
                    whisper_context.mode
                )
                
                if whisper_response:
                    # Send whisper to user
                    await agent.say(whisper_response, allow_interruptions=True)
                    
                    # Log the whisper for analytics
                    await self._log_whisper_interaction(
                        ctx.room.name,
                        message,
                        whisper_response,
                        whisper_context
                    )
                    
            except Exception as e:
                logger.error(f"Error processing user speech: {e}")
        
        @ctx.room.on("participant_connected")
        async def on_participant_connected(participant: rtc.RemoteParticipant):
            """Handle new participant joining"""
            logger.info(f"New participant connected: {participant.identity}")
            
            # Update audio routing if needed
            await self._update_audio_routing(ctx.room.name, whisper_context.mode)
        
        @ctx.room.on("data_received")
        async def on_data_received(data: rtc.DataPacket):
            """Handle data messages from the server"""
            try:
                message = json.loads(data.data.decode())
                
                if message.get("type") == "whisper_mode_change":
                    # Update whisper mode
                    new_mode = message.get("mode")
                    whisper_context.mode = new_mode
                    logger.info(f"Whisper mode changed to: {new_mode}")
                    
                elif message.get("type") == "goal_update":
                    # Update goals
                    whisper_context.goals = message.get("goals", [])
                    logger.info("Goals updated for whisper session")
                    
            except Exception as e:
                logger.error(f"Error processing data message: {e}")
    
    async def _update_audio_routing(self, room_name: str, mode: str):
        """Update audio routing based on whisper mode"""
        try:
            # This would typically call the server API to update subscriptions
            # For now, we'll log the action
            logger.info(f"Audio routing update requested for room {room_name}, mode: {mode}")
            
        except Exception as e:
            logger.error(f"Error updating audio routing: {e}")
    
    async def _log_whisper_interaction(
        self,
        room_name: str,
        user_speech: str,
        whisper_response: str,
        context: WhisperContext
    ):
        """Log whisper interaction for analytics"""
        try:
            interaction_data = {
                "room_name": room_name,
                "user_id": context.user_id,
                "contact_id": context.contact_id,
                "user_speech": user_speech,
                "whisper_response": whisper_response,
                "mode": context.mode,
                "timestamp": asyncio.get_event_loop().time(),
            }
            
            # Send to analytics service
            await self.whisper_service.log_interaction(interaction_data)
            
        except Exception as e:
            logger.error(f"Error logging whisper interaction: {e}")

# Agent entry point
async def entrypoint(ctx: JobContext):
    """Entry point for the Whisper AI Agent"""
    agent = WhisperAIAgent()
    await agent.entrypoint(ctx)

if __name__ == "__main__":
    # Run the agent
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            prewarm_fnc=lambda: silero.VAD.load(),
        )
    )
