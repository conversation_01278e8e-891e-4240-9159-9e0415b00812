"""
Whisper AI Agent - Real-time AI assistant for whisper calls
Updated for LiveKit Agents 1.0+ API
"""
import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from livekit import agents, rtc
from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    RunContext,
    WorkerOptions,
    cli,
    function_tool,
)

# Import plugins - Updated for 1.0+ API
from livekit.plugins import deepgram, elevenlabs, openai, silero

from config import config
from services.whisper_service import WhisperService
from services.goal_service import GoalService
from services.dynamic_goals_service import DynamicGoalsService
from utils.logger import setup_logger

# Setup logging
logger = setup_logger(__name__)

@dataclass
class WhisperContext:
    """Context information for a whisper session"""
    user_id: str
    contact_id: str
    contact_name: str
    contact_type: str
    goals: List[Dict[str, Any]]
    mode: str = "ai-to-user"  # ai-to-user, user-to-ai, normal
    triggers: List[str] = None

class WhisperAgent:
    """
    AI Agent for Whisper feature using LiveKit Agents 1.0+ API
    Acts as a hidden participant that listens to conversations and provides whispers
    """

    def __init__(self):
        self.whisper_service = WhisperService()
        self.goal_service = GoalService()
        self.dynamic_goals_service = DynamicGoalsService()
        self.whisper_context: Optional[WhisperContext] = None
        self.ctx: Optional[JobContext] = None

    async def start(self, ctx: JobContext):
        """Start the whisper agent"""
        self.ctx = ctx

        logger.info("Whisper AI Agent starting...")

        # Extract whisper context from room metadata
        self.whisper_context = await self._extract_whisper_context()
        if not self.whisper_context:
            logger.error("Failed to extract whisper context")
            return

        logger.info(f"Whisper context loaded for contact: {self.whisper_context.contact_name}")

        # Set up event handlers
        await self._setup_event_handlers()

        # Keep the agent running
        await self._run_agent_loop()

    async def _run_agent_loop(self):
        """Main agent loop"""
        try:
            logger.info("Whisper agent loop started")

            # Wait for the room to be disconnected
            await self.ctx.wait_for_participant()

            # Keep running until disconnected
            while self.ctx.room.connection_state == rtc.ConnectionState.CONN_CONNECTED:
                await asyncio.sleep(1)

        except Exception as e:
            logger.error(f"Error in agent loop: {e}")
        finally:
            logger.info("Whisper agent loop ended")

    async def _setup_event_handlers(self):
        """Set up event handlers for the agent"""

        @self.ctx.room.on("participant_connected")
        def on_participant_connected(participant: rtc.RemoteParticipant):
            logger.info(f"Participant connected: {participant.identity}")

        @self.ctx.room.on("participant_disconnected")
        def on_participant_disconnected(participant: rtc.RemoteParticipant):
            logger.info(f"Participant disconnected: {participant.identity}")

        @self.ctx.room.on("track_published")
        def on_track_published(publication: rtc.RemoteTrackPublication, participant: rtc.RemoteParticipant):
            logger.info(f"Track published: {publication.sid} by {participant.identity}")

            # Subscribe to audio tracks for whisper processing
            if publication.kind == rtc.TrackKind.KIND_AUDIO:
                publication.set_subscribed(True)

        @self.ctx.room.on("track_subscribed")
        def on_track_subscribed(track: rtc.Track, publication: rtc.RemoteTrackPublication, participant: rtc.RemoteParticipant):
            logger.info(f"Track subscribed: {publication.sid} from {participant.identity}")

            # Process audio for whisper triggers
            if isinstance(track, rtc.AudioTrack):
                asyncio.create_task(self._process_audio_track(track, participant))

    async def _process_audio_track(self, track: rtc.AudioTrack, participant: rtc.RemoteParticipant):
        """Process audio track for whisper triggers"""
        try:
            logger.info(f"Processing audio track from {participant.identity}")

            # This is a simplified version - in production you'd use STT here
            # For now, we'll simulate speech detection
            await asyncio.sleep(5)  # Simulate processing delay

            # Simulate detected speech
            simulated_speech = "Hello, I'm interested in your product"

            # Process for whisper triggers
            await self._process_speech_for_whispers(simulated_speech, participant.identity)

        except Exception as e:
            logger.error(f"Error processing audio track: {e}")

    async def _process_speech_for_whispers(self, speech_text: str, speaker_identity: str):
        """Process speech for whisper triggers"""
        try:
            if not self.whisper_context:
                return

            # Determine speaker type
            speaker = "user" if "user" in speaker_identity.lower() else "caller"

            # Use dynamic goals service for intelligent trigger detection
            triggered_goals = await self.dynamic_goals_service.process_conversation_for_triggers(
                speech_text,
                speaker,
                self.ctx.room.name,
                self.whisper_context.goals
            )

            if triggered_goals and self.whisper_context.mode == "ai-to-user":
                best_trigger = triggered_goals[0]
                suggestion = best_trigger['suggestion']

                logger.info(f"Whisper suggestion: {suggestion}")

                # Log the interaction
                await self._log_whisper_interaction(
                    self.ctx.room.name,
                    speech_text,
                    suggestion,
                    "ai_to_user_whisper",
                    {
                        'triggered_goal': best_trigger,
                        'confidence': best_trigger['confidence'],
                        'trigger_type': best_trigger['trigger_type']
                    }
                )

                # In a real implementation, you would send this as audio
                # For now, we'll just log it
                logger.info(f"Would send whisper to user: {suggestion}")

        except Exception as e:
            logger.error(f"Error processing speech for whispers: {e}")
    
    async def _extract_whisper_context(self) -> Optional[WhisperContext]:
        """Extract whisper context from session metadata"""
        try:
            # In 1.0+ API, we get context from session userdata or room metadata
            if hasattr(self.session, 'userdata') and self.session.userdata:
                metadata = self.session.userdata
            else:
                # Fallback to room metadata
                room_metadata = getattr(self.session.room, 'metadata', '{}')
                metadata = json.loads(room_metadata) if room_metadata else {}

            if not metadata:
                return None

            # Get goals for the contact
            goals = await self.goal_service.get_contact_goals(
                metadata.get("userId"),
                metadata.get("contactId")
            )

            return WhisperContext(
                user_id=metadata.get("userId"),
                contact_id=metadata.get("contactId"),
                contact_name=metadata.get("contactName"),
                contact_type=metadata.get("contactType"),
                goals=goals,
                triggers=[goal.get("triggers", []) for goal in goals]
            )
        except Exception as e:
            logger.error(f"Error extracting whisper context: {e}")
            return None
    
    def _format_goals_for_prompt(self, goals: List[Dict[str, Any]]) -> str:
        """Format goals for the system prompt"""
        if not goals:
            return "No specific goals set for this call."

        formatted_goals = []
        for i, goal in enumerate(goals, 1):
            formatted_goals.append(
                f"{i}. {goal.get('title', 'Unknown Goal')}: {goal.get('aiPrompt', 'No specific instructions')}"
            )

        return "\n".join(formatted_goals)
    
    async def _is_whisper_to_ai(self, message: str) -> bool:
        """Detect if the user is whispering to the AI"""
        whisper_indicators = [
            "ai", "assistant", "help me", "suggest", "what should i",
            "tell me", "advice", "recommend", "whisper", "privately"
        ]

        message_lower = message.lower()
        return any(indicator in message_lower for indicator in whisper_indicators)

    async def _log_whisper_interaction(
        self,
        room_name: str,
        user_speech: str,
        whisper_response: str,
        interaction_type: str = "ai_to_user_whisper",
        additional_metadata: dict = None
    ):
        """Log whisper interaction for analytics"""
        try:
            if not self.whisper_context:
                return

            interaction_data = {
                "room_name": room_name,
                "user_id": self.whisper_context.user_id,
                "contact_id": self.whisper_context.contact_id,
                "user_speech": user_speech,
                "whisper_response": whisper_response,
                "mode": self.whisper_context.mode,
                "interaction_type": interaction_type,
                "timestamp": asyncio.get_event_loop().time(),
                "metadata": additional_metadata or {},
            }

            # Send to analytics service
            await self.whisper_service.log_interaction(interaction_data)

        except Exception as e:
            logger.error(f"Error logging whisper interaction: {e}")

# Agent entry point using new 1.0+ API
async def entrypoint(ctx: JobContext):
    """Entry point for the Whisper AI Agent using LiveKit Agents 1.0+ API"""
    await ctx.connect()

    logger.info(f"Whisper AI Agent connected to room: {ctx.room.name}")

    # Create the whisper agent
    agent = WhisperAgent()

    # Start the agent with the context
    await agent.start(ctx)

    logger.info("Whisper AI Agent started successfully")

if __name__ == "__main__":
    # Run the agent using new 1.0+ API
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
        )
    )
