# LiveKit Configuration
LIVEKIT_URL=wss://your-livekit-instance.livekit.cloud
LIVEKIT_API_KEY=your_api_key
LIVEKIT_API_SECRET=your_api_secret

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Deepgram Configuration (for STT)
DEEPGRAM_API_KEY=your_deepgram_api_key

# ElevenLabs Configuration (for TTS)
ELEVENLABS_API_KEY=your_elevenlabs_api_key

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/talkai247

# Server Configuration
SERVER_API_URL=http://localhost:3030/api/v1

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Agent Configuration
AGENT_NAME=whisper-ai-assistant
AGENT_VERSION=1.0.0

# Whisper Feature Configuration
WHISPER_ENABLED=true
WHISPER_TRIGGER_THRESHOLD=0.7
WHISPER_RESPONSE_DELAY=1.0

# Audio Processing Configuration
AUDIO_SAMPLE_RATE=16000
AUDIO_CHANNELS=1
AUDIO_FRAME_SIZE=320
