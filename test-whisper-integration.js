#!/usr/bin/env node

/**
 * Comprehensive test script for Real-Time AI Whisper Feature
 * Tests the three-participant room setup and whisper functionality
 */

import axios from 'axios';
import { WebSocket } from 'ws';

const BASE_URL = process.env.SERVER_URL || 'http://localhost:3030';
const API_BASE = `${BASE_URL}/api/v1`;

// Test configuration
const TEST_CONFIG = {
  user: {
    email: '<EMAIL>',
    password: 'testpassword123',
  },
  contact: {
    name: 'Test Contact',
    phone: '+1234567890',
    email: '<EMAIL>',
    type: 'business',
  },
};

class WhisperIntegrationTester {
  constructor() {
    this.authToken = null;
    this.userId = null;
    this.contactId = null;
    this.callId = null;
    this.sessionId = null;
    this.roomName = null;
  }

  async runTests() {
    console.log('🚀 Starting Real-Time AI Whisper Feature Integration Tests\n');

    try {
      // Phase 1: Authentication and Setup
      await this.testAuthentication();
      await this.testContactCreation();
      
      // Phase 2: SIP Configuration (Admin only)
      await this.testSipConfiguration();
      
      // Phase 3: Three-Participant Room Setup
      await this.testWhisperConnectionDetails();
      await this.testRoomCreation();
      
      // Phase 4: AI Agent Integration
      await this.testAiAgentTrigger();
      
      // Phase 5: Whisper Mode Control
      await this.testWhisperModeChanges();
      
      // Phase 6: Session Management
      await this.testSessionManagement();
      
      // Phase 7: Audio Routing
      await this.testAudioRouting();
      
      // Phase 8: Analytics and Cleanup
      await this.testAnalytics();
      await this.cleanup();
      
      console.log('\n✅ All tests completed successfully!');
      
    } catch (error) {
      console.error('\n❌ Test failed:', error.message);
      console.error('Stack trace:', error.stack);
      process.exit(1);
    }
  }

  async testAuthentication() {
    console.log('📝 Testing Authentication...');
    
    try {
      // Try to login (user might already exist)
      const loginResponse = await axios.post(`${API_BASE}/auth/login`, TEST_CONFIG.user);
      this.authToken = loginResponse.data.data.token;
      this.userId = loginResponse.data.data.user.id;
      console.log('✅ Login successful');
    } catch (error) {
      if (error.response?.status === 401) {
        // User doesn't exist, create account
        console.log('Creating new test user...');
        const registerResponse = await axios.post(`${API_BASE}/auth/register`, {
          ...TEST_CONFIG.user,
          name: 'Test User',
        });
        this.authToken = registerResponse.data.data.token;
        this.userId = registerResponse.data.data.user.id;
        console.log('✅ Registration successful');
      } else {
        throw error;
      }
    }
  }

  async testContactCreation() {
    console.log('👤 Testing Contact Creation...');
    
    const response = await this.apiCall('POST', '/contacts', TEST_CONFIG.contact);
    this.contactId = response.data.id;
    console.log(`✅ Contact created with ID: ${this.contactId}`);
  }

  async testSipConfiguration() {
    console.log('📞 Testing SIP Configuration...');
    
    try {
      // Check SIP status
      const statusResponse = await this.apiCall('GET', '/whisper/sip/status');
      console.log('✅ SIP status retrieved:', statusResponse.data);
      
      // Try to initialize SIP (might fail if not admin)
      try {
        const initResponse = await this.apiCall('POST', '/whisper/sip/initialize');
        console.log('✅ SIP initialization successful');
      } catch (error) {
        if (error.response?.status === 403) {
          console.log('⚠️  SIP initialization skipped (admin required)');
        } else {
          throw error;
        }
      }
    } catch (error) {
      console.log('⚠️  SIP configuration test skipped:', error.message);
    }
  }

  async testWhisperConnectionDetails() {
    console.log('🔗 Testing Whisper Connection Details...');
    
    const response = await this.apiCall('GET', `/whisper/connection-details?contactId=${this.contactId}`);
    const connectionDetails = response.data;
    
    // Verify three-participant architecture
    if (!connectionDetails.userParticipant) {
      throw new Error('Missing user participant details');
    }
    if (!connectionDetails.aiParticipant) {
      throw new Error('Missing AI participant details');
    }
    if (!connectionDetails.callerParticipant) {
      throw new Error('Missing caller participant details');
    }
    
    this.callId = connectionDetails.callId;
    this.roomName = connectionDetails.roomName;
    
    console.log('✅ Three-participant architecture verified');
    console.log(`   Room: ${this.roomName}`);
    console.log(`   Call ID: ${this.callId}`);
  }

  async testRoomCreation() {
    console.log('🏠 Testing Room Creation...');
    
    // Get room state
    const response = await this.apiCall('GET', `/whisper/rooms/${this.roomName}/state`);
    console.log('✅ Room state retrieved:', response.data);
  }

  async testAiAgentTrigger() {
    console.log('🤖 Testing AI Agent Trigger...');
    
    const response = await this.apiCall('POST', '/whisper/agent/trigger', {
      callId: this.callId,
      roomName: this.roomName,
      agentInstructions: {
        systemPrompt: 'Test AI agent for whisper functionality',
        contactInfo: TEST_CONFIG.contact,
      },
    });
    
    console.log('✅ AI Agent triggered successfully:', response.data.message);
  }

  async testWhisperModeChanges() {
    console.log('🔄 Testing Whisper Mode Changes...');
    
    const modes = ['ai-to-user', 'user-to-ai', 'normal'];
    
    for (const mode of modes) {
      const response = await this.apiCall('POST', `/whisper/calls/${this.callId}/mode`, {
        mode,
        roomName: this.roomName,
        participants: {
          userIdentity: 'test-user-identity',
          callerIdentity: 'test-caller-identity',
          aiIdentity: 'test-ai-identity',
        },
      });
      
      console.log(`✅ Mode changed to: ${mode}`);
    }
  }

  async testSessionManagement() {
    console.log('📊 Testing Session Management...');
    
    // Create whisper session
    const sessionResponse = await this.apiCall('POST', '/whisper/sessions', {
      callId: this.callId,
      contactId: this.contactId,
      livekitRoomName: this.roomName,
      whisperMode: 'AI_TO_USER',
      participantData: {
        userIdentity: 'test-user-identity',
        callerIdentity: 'test-caller-identity',
        aiIdentity: 'test-ai-identity',
      },
    });
    
    this.sessionId = sessionResponse.data.id;
    console.log(`✅ Whisper session created: ${this.sessionId}`);
    
    // Log interaction
    await this.apiCall('POST', `/whisper/sessions/${this.sessionId}/interactions`, {
      interactionType: 'AI_TO_USER_WHISPER',
      userSpeech: 'Test user speech',
      aiResponse: 'Test AI response',
      triggerGoals: [],
      responseTime: 500,
      confidence: 0.85,
    });
    
    console.log('✅ Whisper interaction logged');
    
    // Get session history
    const historyResponse = await this.apiCall('GET', '/whisper/sessions/history?limit=5');
    console.log(`✅ Session history retrieved: ${historyResponse.data.length} sessions`);
  }

  async testAudioRouting() {
    console.log('🔊 Testing Audio Routing...');
    
    // Test different routing configurations
    const routingModes = ['ai-to-user', 'user-to-ai', 'normal'];
    
    for (const mode of routingModes) {
      try {
        const response = await this.apiCall('POST', `/whisper/calls/${this.callId}/mode`, {
          mode,
          roomName: this.roomName,
          participants: {
            userIdentity: 'test-user-identity',
            callerIdentity: 'test-caller-identity',
            aiIdentity: 'test-ai-identity',
          },
        });
        
        console.log(`✅ Audio routing configured for ${mode} mode`);
      } catch (error) {
        console.log(`⚠️  Audio routing test for ${mode} mode failed:`, error.message);
      }
    }
  }

  async testAnalytics() {
    console.log('📈 Testing Analytics...');
    
    // Test session history
    const historyResponse = await this.apiCall('GET', '/whisper/sessions/history');
    console.log(`✅ Retrieved ${historyResponse.data.length} session(s) from history`);
  }

  async cleanup() {
    console.log('🧹 Cleaning up test data...');
    
    try {
      // End the call
      await this.apiCall('POST', `/whisper/calls/${this.callId}/end`);
      console.log('✅ Call ended');
      
      // Delete test contact
      await this.apiCall('DELETE', `/contacts/${this.contactId}`);
      console.log('✅ Test contact deleted');
      
    } catch (error) {
      console.log('⚠️  Cleanup warning:', error.message);
    }
  }

  async apiCall(method, endpoint, data = null) {
    const config = {
      method,
      url: `${API_BASE}${endpoint}`,
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
        'Content-Type': 'application/json',
      },
    };
    
    if (data) {
      config.data = data;
    }
    
    try {
      const response = await axios(config);
      return response.data;
    } catch (error) {
      if (error.response) {
        throw new Error(`API Error ${error.response.status}: ${error.response.data?.error?.message || error.response.statusText}`);
      }
      throw error;
    }
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new WhisperIntegrationTester();
  tester.runTests().catch(console.error);
}

export default WhisperIntegrationTester;
